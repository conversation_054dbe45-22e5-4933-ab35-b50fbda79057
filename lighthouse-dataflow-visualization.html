<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lighthouse Components Dataflow Visualization</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: auto;
        }

        .container {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .dataflow-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .flow-section {
            margin-bottom: 40px;
        }

        .section-title {
            font-size: 1.5rem;
            color: #2d3748;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #667eea;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .flow-diagram {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: center;
            align-items: flex-start;
        }

        .component-box {
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            min-width: 200px;
            max-width: 280px;
            position: relative;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .component-box:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .component-box.core {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #5a67d8;
        }

        .component-box.module {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
            border-color: #38a169;
        }

        .component-box.shared {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
            color: white;
            border-color: #dd6b20;
        }

        .component-box.store {
            background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);
            color: white;
            border-color: #805ad5;
        }

        .component-title {
            font-weight: bold;
            font-size: 1.1rem;
            margin-bottom: 8px;
        }

        .component-description {
            font-size: 0.9rem;
            opacity: 0.9;
            line-height: 1.4;
        }

        .component-features {
            margin-top: 10px;
            font-size: 0.8rem;
        }

        .component-features ul {
            list-style: none;
            padding-left: 0;
        }

        .component-features li {
            padding: 2px 0;
            opacity: 0.8;
        }

        .component-features li:before {
            content: "▸ ";
            color: #ffd700;
        }

        .arrow {
            position: absolute;
            width: 0;
            height: 0;
            z-index: 10;
        }

        .arrow-right {
            border-left: 15px solid #667eea;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
        }

        .arrow-down {
            border-top: 15px solid #667eea;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
        }

        .flow-path {
            display: flex;
            align-items: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
            justify-content: center;
        }

        .flow-step {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 500;
            position: relative;
        }

        .flow-step:not(:last-child):after {
            content: "→";
            position: absolute;
            right: -25px;
            top: 50%;
            transform: translateY(-50%);
            color: #667eea;
            font-size: 1.2rem;
            font-weight: bold;
        }

        .interaction-matrix {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .interaction-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            transition: all 0.3s ease;
        }

        .interaction-card:hover {
            background: #e3f2fd;
            border-color: #2196f3;
        }

        .interaction-title {
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 8px;
        }

        .interaction-flow {
            font-size: 0.9rem;
            color: #4a5568;
            line-height: 1.4;
        }

        .legend {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: center;
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
        }

        .state-flow {
            background: #fff;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .state-diagram {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .state-layer {
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: 10px;
            background: #f7fafc;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .state-item {
            background: white;
            padding: 8px 16px;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            font-size: 0.9rem;
            font-weight: 500;
        }

        @media (max-width: 768px) {
            .flow-diagram {
                flex-direction: column;
                align-items: center;
            }
            
            .component-box {
                max-width: 100%;
            }
            
            .flow-path {
                flex-direction: column;
            }
            
            .flow-step:not(:last-child):after {
                content: "↓";
                right: 50%;
                top: 100%;
                transform: translateX(50%);
            }
        }

        .tooltip {
            position: absolute;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0.8rem;
            pointer-events: none;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .component-box:hover .tooltip {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏮 Lighthouse Components Dataflow</h1>
            <p>Interactive visualization of component interactions and data flow patterns</p>
        </div>

        <div class="dataflow-container">
            <!-- Core Architecture Section -->
            <div class="flow-section">
                <h2 class="section-title">
                    🏗️ Core Architecture
                </h2>
                <div class="flow-diagram">
                    <div class="component-box core" data-tooltip="Main orchestrator component with module routing">
                        <div class="component-title">LighthouseMain</div>
                        <div class="component-description">Central coordinator managing module lifecycle and session state</div>
                        <div class="component-features">
                            <ul>
                                <li>Module routing & initialization</li>
                                <li>Session management</li>
                                <li>Error boundary handling</li>
                                <li>Loading state coordination</li>
                            </ul>
                        </div>
                    </div>

                    <div class="component-box core" data-tooltip="Enhanced navigation with animated transitions">
                        <div class="component-title">NavigationSystem</div>
                        <div class="component-description">Intelligent navigation with contextual awareness and accessibility</div>
                        <div class="component-features">
                            <ul>
                                <li>Module switching</li>
                                <li>Notification badges</li>
                                <li>Accessibility support</li>
                                <li>Animation controls</li>
                            </ul>
                        </div>
                    </div>

                    <div class="component-box core" data-tooltip="Container with smooth transitions between modules">
                        <div class="component-title">ModuleContainer</div>
                        <div class="component-description">Wrapper providing consistent layout and transition effects</div>
                        <div class="component-features">
                            <ul>
                                <li>Transition animations</li>
                                <li>Context updates</li>
                                <li>Module isolation</li>
                                <li>Error boundaries</li>
                            </ul>
                        </div>
                    </div>

                    <div class="component-box core" data-tooltip="Project state management and context provider">
                        <div class="component-title">ProjectContext</div>
                        <div class="component-description">Manages project-specific state and intelligence context</div>
                        <div class="component-features">
                            <ul>
                                <li>Project lifecycle</li>
                                <li>Context propagation</li>
                                <li>Intelligence updates</li>
                                <li>State persistence</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- State Management Flow -->
            <div class="flow-section">
                <h2 class="section-title">
                    🔄 State Management Flow
                </h2>
                <div class="state-flow">
                    <div class="state-diagram">
                        <div class="state-layer">
                            <div class="state-item">User Interaction</div>
                            <div class="state-item">Navigation Event</div>
                            <div class="state-item">Module Switch</div>
                        </div>
                        <div class="state-layer">
                            <div class="state-item">Zustand Store Update</div>
                            <div class="state-item">Context Propagation</div>
                            <div class="state-item">Component Re-render</div>
                        </div>
                        <div class="state-layer">
                            <div class="state-item">Intelligence Update</div>
                            <div class="state-item">Learning Event</div>
                            <div class="state-item">Insight Generation</div>
                        </div>
                    </div>
                </div>

                <div class="component-box store" data-tooltip="Centralized state management with Zustand">
                    <div class="component-title">Lighthouse Store</div>
                    <div class="component-description">Zustand-based store managing all application state with persistence</div>
                    <div class="component-features">
                        <ul>
                            <li>Navigation state</li>
                            <li>Project management</li>
                            <li>Knowledge graph</li>
                            <li>Intelligence context</li>
                            <li>Agent coordination</li>
                            <li>Insight tracking</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Module Ecosystem -->
            <div class="flow-section">
                <h2 class="section-title">
                    🧩 Module Ecosystem
                </h2>
                <div class="flow-diagram">
                    <div class="component-box module" data-tooltip="Central hub with project overview and insights">
                        <div class="component-title">Dashboard</div>
                        <div class="component-description">Project overview with AI-powered insights and recommendations</div>
                        <div class="component-features">
                            <ul>
                                <li>Project metrics</li>
                                <li>Activity feeds</li>
                                <li>Smart recommendations</li>
                                <li>Status indicators</li>
                            </ul>
                        </div>
                    </div>

                    <div class="component-box module" data-tooltip="Knowledge management with graph visualization">
                        <div class="component-title">Knowledge Hub</div>
                        <div class="component-description">Smart knowledge collections with graph-based connections</div>
                        <div class="component-features">
                            <ul>
                                <li>Knowledge graph</li>
                                <li>Smart collections</li>
                                <li>Concept mapping</li>
                                <li>Quick capture</li>
                            </ul>
                        </div>
                    </div>

                    <div class="component-box module" data-tooltip="Research workspace with source grounding">
                        <div class="component-title">Research Studio</div>
                        <div class="component-description">Source-grounded research with multi-document analysis</div>
                        <div class="component-features">
                            <ul>
                                <li>Source-grounded chat</li>
                                <li>Multi-doc analysis</li>
                                <li>Audio overview</li>
                                <li>Research notebook</li>
                            </ul>
                        </div>
                    </div>

                    <div class="component-box module" data-tooltip="Autonomous agent deployment and monitoring">
                        <div class="component-title">Agent Workspace</div>
                        <div class="component-description">Deploy and monitor autonomous AI agents</div>
                        <div class="component-features">
                            <ul>
                                <li>Agent deployment</li>
                                <li>Process monitoring</li>
                                <li>Template management</li>
                                <li>Result tracking</li>
                            </ul>
                        </div>
                    </div>

                    <div class="component-box module" data-tooltip="Context-aware AI conversations">
                        <div class="component-title">Contextual Chat</div>
                        <div class="component-description">AI chat with project and knowledge context</div>
                        <div class="component-features">
                            <ul>
                                <li>Context awareness</li>
                                <li>Knowledge integration</li>
                                <li>Multi-turn conversations</li>
                                <li>Source citations</li>
                            </ul>
                        </div>
                    </div>

                    <div class="component-box module" data-tooltip="Document and file management system">
                        <div class="component-title">Source Manager</div>
                        <div class="component-description">Manage documents and files with intelligent organization</div>
                        <div class="component-features">
                            <ul>
                                <li>File organization</li>
                                <li>Content extraction</li>
                                <li>Metadata management</li>
                                <li>Search capabilities</li>
                            </ul>
                        </div>
                    </div>

                    <div class="component-box module" data-tooltip="System and project analytics">
                        <div class="component-title">Analytics</div>
                        <div class="component-description">Comprehensive analytics and performance insights</div>
                        <div class="component-features">
                            <ul>
                                <li>Usage analytics</li>
                                <li>Performance metrics</li>
                                <li>Trend analysis</li>
                                <li>Custom dashboards</li>
                            </ul>
                        </div>
                    </div>

                    <div class="component-box module" data-tooltip="AI-generated insights and recommendations">
                        <div class="component-title">AI Insights</div>
                        <div class="component-description">AI-powered insights and intelligent recommendations</div>
                        <div class="component-features">
                            <ul>
                                <li>Pattern recognition</li>
                                <li>Predictive insights</li>
                                <li>Recommendation engine</li>
                                <li>Learning optimization</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Shared Components -->
            <div class="flow-section">
                <h2 class="section-title">
                    🎨 Shared UI Components
                </h2>
                <div class="flow-diagram">
                    <div class="component-box shared" data-tooltip="Enhanced icon system with animations">
                        <div class="component-title">Enhanced Icons</div>
                        <div class="component-description">Sophisticated icon system with states and animations</div>
                        <div class="component-features">
                            <ul>
                                <li>Multiple variants</li>
                                <li>Size scaling</li>
                                <li>State management</li>
                                <li>Accessibility support</li>
                            </ul>
                        </div>
                    </div>

                    <div class="component-box shared" data-tooltip="Rich microinteractions and animations">
                        <div class="component-title">Microinteractions</div>
                        <div class="component-description">Rich interaction patterns with accessibility awareness</div>
                        <div class="component-features">
                            <ul>
                                <li>Hover effects</li>
                                <li>Press feedback</li>
                                <li>Magnetic interactions</li>
                                <li>Reduced motion support</li>
                            </ul>
                        </div>
                    </div>

                    <div class="component-box shared" data-tooltip="Responsive grid and layout systems">
                        <div class="component-title">Grid Layouts</div>
                        <div class="component-description">Adaptive layouts with responsive breakpoints</div>
                        <div class="component-features">
                            <ul>
                                <li>Responsive grids</li>
                                <li>Staggered animations</li>
                                <li>Mantine integration</li>
                                <li>Flexible spacing</li>
                            </ul>
                        </div>
                    </div>

                    <div class="component-box shared" data-tooltip="Enhanced loading and error states">
                        <div class="component-title">State Components</div>
                        <div class="component-description">Sophisticated loading, error, and success states</div>
                        <div class="component-features">
                            <ul>
                                <li>Loading overlays</li>
                                <li>Error boundaries</li>
                                <li>Success indicators</li>
                                <li>Skeleton screens</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Interaction Flow -->
            <div class="flow-section">
                <h2 class="section-title">
                    👤 User Interaction Flow
                </h2>
                <div class="interaction-matrix">
                    <div class="interaction-card">
                        <div class="interaction-title">Navigation Flow</div>
                        <div class="interaction-flow">
                            User clicks module → NavigationSystem handles event → Store updates navigation state → ModuleContainer transitions → Target module renders
                        </div>
                    </div>

                    <div class="interaction-card">
                        <div class="interaction-title">Knowledge Creation</div>
                        <div class="interaction-flow">
                            User creates knowledge → QuickCapture processes → Store updates knowledge graph → KnowledgeHub reflects changes → Intelligence context updates
                        </div>
                    </div>

                    <div class="interaction-card">
                        <div class="interaction-title">Agent Deployment</div>
                        <div class="interaction-flow">
                            User deploys agent → AgentWorkspace validates → Store manages agent state → ProcessMonitor tracks execution → Results update insights
                        </div>
                    </div>

                    <div class="interaction-card">
                        <div class="interaction-title">Research Session</div>
                        <div class="interaction-flow">
                            User starts research → ResearchStudio initializes → Sources loaded → Chat context established → Insights generated → Knowledge graph updated
                        </div>
                    </div>

                    <div class="interaction-card">
                        <div class="interaction-title">Project Context</div>
                        <div class="interaction-flow">
                            Project selected → ProjectContext updates → Intelligence context refreshed → All modules receive new context → UI reflects project state
                        </div>
                    </div>

                    <div class="interaction-card">
                        <div class="interaction-title">Learning Events</div>
                        <div class="interaction-flow">
                            User action triggers learning → Event recorded in store → Intelligence context processes → Suggestions generated → UI shows recommendations
                        </div>
                    </div>
                </div>
            </div>

            <!-- Data Flow Paths -->
            <div class="flow-section">
                <h2 class="section-title">
                    🔄 Key Data Flow Paths
                </h2>
                
                <div class="flow-path">
                    <div class="flow-step">User Input</div>
                    <div class="flow-step">Component Handler</div>
                    <div class="flow-step">Store Action</div>
                    <div class="flow-step">State Update</div>
                    <div class="flow-step">Context Propagation</div>
                    <div class="flow-step">UI Re-render</div>
                </div>

                <div class="flow-path">
                    <div class="flow-step">Knowledge Input</div>
                    <div class="flow-step">Graph Processing</div>
                    <div class="flow-step">Relationship Mapping</div>
                    <div class="flow-step">Intelligence Update</div>
                    <div class="flow-step">Insight Generation</div>
                </div>

                <div class="flow-path">
                    <div class="flow-step">Agent Request</div>
                    <div class="flow-step">Deployment Queue</div>
                    <div class="flow-step">Execution Monitor</div>
                    <div class="flow-step">Result Processing</div>
                    <div class="flow-step">Context Integration</div>
                </div>
            </div>

            <!-- Legend -->
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);"></div>
                    <span>Core Components</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);"></div>
                    <span>Module Components</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);"></div>
                    <span>Shared Components</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);"></div>
                    <span>State Management</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Add interactive tooltips
        document.addEventListener('DOMContentLoaded', function() {
            const componentBoxes = document.querySelectorAll('.component-box');
            
            componentBoxes.forEach(box => {
                const tooltip = box.getAttribute('data-tooltip');
                if (tooltip) {
                    const tooltipElement = document.createElement('div');
                    tooltipElement.className = 'tooltip';
                    tooltipElement.textContent = tooltip;
                    box.appendChild(tooltipElement);
                }

                // Add click interaction for detailed view
                box.addEventListener('click', function() {
                    const title = this.querySelector('.component-title').textContent;
                    const description = this.querySelector('.component-description').textContent;
                    const features = Array.from(this.querySelectorAll('.component-features li'))
                        .map(li => li.textContent.replace('▸ ', '• '))
                        .join('\n');
                    
                    alert(`${title}\n\n${description}\n\nFeatures:\n${features}`);
                });
            });

            // Add smooth scrolling for better UX
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Add animation on scroll
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // Observe all component boxes for scroll animations
            componentBoxes.forEach(box => {
                box.style.opacity = '0';
                box.style.transform = 'translateY(20px)';
                box.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(box);
            });
        });
    </script>
</body>
</html>