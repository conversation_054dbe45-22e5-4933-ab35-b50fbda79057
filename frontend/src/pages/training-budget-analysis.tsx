import React, { useState } from 'react';
import {
  Container,
  Paper,
  Group,
  Button,
  Text,
  Breadcrumbs,
  Anchor,
  ActionIcon,
  Tooltip,
  Modal,
  Stack,
  TextInput,
  Select,
  NumberInput,
  Textarea,
  Alert
} from '@mantine/core';
import { motion } from 'framer-motion';
import {
  IconHome,
  IconChartBar,
  IconSettings,
  IconHelp,
  IconDownload,
  IconFileExport,
  IconPrinter,
  IconShare,
  IconInfoCircle
} from '@tabler/icons-react';
import { BudgetDashboard } from '../components/training-needs-analysis/components/budget-analysis';
import { notifications } from '@mantine/notifications';
import { useRouter } from 'next/router';

interface ExportOptions {
  format: 'pdf' | 'excel' | 'csv';
  includeCharts: boolean;
  dateRange: 'current' | 'last-quarter' | 'last-year' | 'custom';
  customStartDate?: Date;
  customEndDate?: Date;
}

const TrainingBudgetAnalysisPage: React.FC = () => {
  const router = useRouter();
  const [exportModalOpen, setExportModalOpen] = useState(false);
  const [settingsModalOpen, setSettingsModalOpen] = useState(false);
  const [helpModalOpen, setHelpModalOpen] = useState(false);
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'pdf',
    includeCharts: true,
    dateRange: 'current'
  });

  const breadcrumbItems = [
    { title: 'Home', href: '/' },
    { title: 'Training', href: '/training' },
    { title: 'Budget Analysis', href: '/training-budget-analysis' }
  ].map((item, index) => (
    <Anchor key={index} href={item.href} size="sm">
      {item.title}
    </Anchor>
  ));

  const handleExport = () => {
    setExportModalOpen(true);
  };

  const handleExportConfirm = () => {
    // Simulate export process
    notifications.show({
      title: 'Export Started',
      message: `Generating ${exportOptions.format.toUpperCase()} report...`,
      color: 'blue',
      loading: true,
      autoClose: false,
      id: 'export-notification'
    });

    // Simulate export completion
    setTimeout(() => {
      notifications.update({
        id: 'export-notification',
        title: 'Export Complete',
        message: `Your ${exportOptions.format.toUpperCase()} report has been generated successfully!`,
        color: 'green',
        loading: false,
        autoClose: 3000
      });
    }, 2000);

    setExportModalOpen(false);
  };

  const handleSave = (data: any) => {
    notifications.show({
      title: 'Data Saved',
      message: 'Budget analysis data has been saved successfully',
      color: 'green'
    });
  };

  const handlePrint = () => {
    window.print();
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: 'Training Budget Analysis',
        text: 'Check out this training budget analysis report',
        url: window.location.href
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      notifications.show({
        title: 'Link Copied',
        message: 'Page URL has been copied to clipboard',
        color: 'blue'
      });
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <Container size="xl" py="md">
        {/* Header Section */}
        <Paper p="md" mb="lg" withBorder>
          <Stack gap="sm">
            <Breadcrumbs>{breadcrumbItems}</Breadcrumbs>
            
            <Group justify="space-between" align="center">
              <div>
                <Group align="center" gap="sm">
                  <IconChartBar size={32} color="#228be6" />
                  <div>
                    <Text size="xl" fw={700}>Training Budget Analysis Dashboard</Text>
                    <Text size="sm" c="dimmed">
                      Comprehensive budget tracking and variance analysis for training programs
                    </Text>
                  </div>
                </Group>
              </div>
              
              <Group>
                <Tooltip label="Help & Documentation">
                  <ActionIcon
                    variant="subtle"
                    size="lg"
                    onClick={() => setHelpModalOpen(true)}
                  >
                    <IconHelp size={20} />
                  </ActionIcon>
                </Tooltip>
                
                <Tooltip label="Print Report">
                  <ActionIcon
                    variant="subtle"
                    size="lg"
                    onClick={handlePrint}
                  >
                    <IconPrinter size={20} />
                  </ActionIcon>
                </Tooltip>
                
                <Tooltip label="Share Dashboard">
                  <ActionIcon
                    variant="subtle"
                    size="lg"
                    onClick={handleShare}
                  >
                    <IconShare size={20} />
                  </ActionIcon>
                </Tooltip>
                
                <Tooltip label="Dashboard Settings">
                  <ActionIcon
                    variant="subtle"
                    size="lg"
                    onClick={() => setSettingsModalOpen(true)}
                  >
                    <IconSettings size={20} />
                  </ActionIcon>
                </Tooltip>
                
                <Button
                  leftSection={<IconFileExport size={16} />}
                  onClick={handleExport}
                >
                  Export Report
                </Button>
              </Group>
            </Group>
          </Stack>
        </Paper>

        {/* Quick Info Alert */}
        <Alert
          icon={<IconInfoCircle size={16} />}
          title="Budget Analysis Overview"
          color="blue"
          mb="lg"
          variant="light"
        >
          This dashboard provides real-time insights into your training budget performance, 
          including planned vs actual spending, variance analysis, and trend forecasting. 
          Use the tabs below to explore different views and analysis options.
        </Alert>

        {/* Main Budget Analysis Component */}
        <BudgetDashboard
          onExport={handleExport}
          onSettings={() => setSettingsModalOpen(true)}
        />

        {/* Export Modal */}
        <Modal
          opened={exportModalOpen}
          onClose={() => setExportModalOpen(false)}
          title="Export Budget Report"
          size="md"
        >
          <Stack gap="md">
            <Select
              label="Export Format"
              data={[
                { value: 'pdf', label: 'PDF Report' },
                { value: 'excel', label: 'Excel Spreadsheet' },
                { value: 'csv', label: 'CSV Data' }
              ]}
              value={exportOptions.format}
              onChange={(value) => setExportOptions(prev => ({
                ...prev,
                format: value as any
              }))}
            />
            
            <Select
              label="Date Range"
              data={[
                { value: 'current', label: 'Current Period' },
                { value: 'last-quarter', label: 'Last Quarter' },
                { value: 'last-year', label: 'Last Year' },
                { value: 'custom', label: 'Custom Range' }
              ]}
              value={exportOptions.dateRange}
              onChange={(value) => setExportOptions(prev => ({
                ...prev,
                dateRange: value as any
              }))}
            />
            
            <Group justify="flex-end">
              <Button
                variant="outline"
                onClick={() => setExportModalOpen(false)}
              >
                Cancel
              </Button>
              <Button
                leftSection={<IconDownload size={16} />}
                onClick={handleExportConfirm}
              >
                Generate Report
              </Button>
            </Group>
          </Stack>
        </Modal>

        {/* Settings Modal */}
        <Modal
          opened={settingsModalOpen}
          onClose={() => setSettingsModalOpen(false)}
          title="Dashboard Settings"
          size="md"
        >
          <Stack gap="md">
            <Select
              label="Default Currency"
              data={[
                { value: 'usd', label: 'USD ($)' },
                { value: 'eur', label: 'EUR (€)' },
                { value: 'gbp', label: 'GBP (£)' }
              ]}
              defaultValue="usd"
            />
            
            <Select
              label="Refresh Interval"
              data={[
                { value: 'manual', label: 'Manual' },
                { value: '5min', label: 'Every 5 minutes' },
                { value: '15min', label: 'Every 15 minutes' },
                { value: '1hour', label: 'Every hour' }
              ]}
              defaultValue="manual"
            />
            
            <NumberInput
              label="Variance Alert Threshold (%)"
              description="Get notified when variance exceeds this percentage"
              defaultValue={15}
              min={1}
              max={100}
            />
            
            <Group justify="flex-end">
              <Button
                variant="outline"
                onClick={() => setSettingsModalOpen(false)}
              >
                Cancel
              </Button>
              <Button onClick={() => {
                setSettingsModalOpen(false);
                notifications.show({
                  title: 'Settings Saved',
                  message: 'Dashboard settings have been updated',
                  color: 'green'
                });
              }}>
                Save Settings
              </Button>
            </Group>
          </Stack>
        </Modal>

        {/* Help Modal */}
        <Modal
          opened={helpModalOpen}
          onClose={() => setHelpModalOpen(false)}
          title="Help & Documentation"
          size="lg"
        >
          <Stack gap="md">
            <div>
              <Text fw={600} mb="xs">Getting Started</Text>
              <Text size="sm" c="dimmed">
                The Training Budget Analysis dashboard helps you track and analyze your training budget performance. 
                Use the different tabs to view summary information, detailed breakdowns, and trend analysis.
              </Text>
            </div>
            
            <div>
              <Text fw={600} mb="xs">Key Features</Text>
              <Text size="sm" c="dimmed">
                • Real-time budget vs actual comparison<br />
                • Variance analysis with alerts<br />
                • Interactive charts and visualizations<br />
                • Export capabilities (PDF, Excel, CSV)<br />
                • Customizable date ranges and filters
              </Text>
            </div>
            
            <div>
              <Text fw={600} mb="xs">Understanding Variance</Text>
              <Text size="sm" c="dimmed">
                Variance shows the difference between planned and actual spending. 
                Positive variance (red) indicates over-budget, while negative variance (green) indicates under-budget.
              </Text>
            </div>
            
            <Group justify="flex-end">
              <Button onClick={() => setHelpModalOpen(false)}>
                Got it
              </Button>
            </Group>
          </Stack>
        </Modal>
      </Container>
    </motion.div>
  );
};

export default TrainingBudgetAnalysisPage;