/**
 * Smart Suggestions Service
 * Provides intelligent suggestions based on department, role, and industry best practices
 */

export interface SkillSuggestion {
  name: string;
  category: 'technical' | 'soft' | 'leadership' | 'compliance';
  relevanceScore: number;
  description?: string;
}

export interface TrainingModality {
  name: string;
  description: string;
  popularity: number;
  effectiveness: number;
}

export interface SmartDefaults {
  skills: SkillSuggestion[];
  trainingModalities: string[];
  learningStyle: string;
  budget: {
    suggested: number;
    range: { min: number; max: number };
  };
  timeline: string;
}

class SmartSuggestionsService {
  // Department-based skill suggestions
  private static departmentSkills: Record<string, SkillSuggestion[]> = {
    'Engineering': [
      { name: 'JavaScript/TypeScript', category: 'technical', relevanceScore: 95, description: 'Essential for modern web development' },
      { name: 'React/Vue.js', category: 'technical', relevanceScore: 90, description: 'Popular frontend frameworks' },
      { name: 'Node.js', category: 'technical', relevanceScore: 85, description: 'Backend JavaScript runtime' },
      { name: 'Git Version Control', category: 'technical', relevanceScore: 95, description: 'Essential for code collaboration' },
      { name: 'API Development', category: 'technical', relevanceScore: 80, description: 'Building and consuming APIs' },
      { name: 'Problem Solving', category: 'soft', relevanceScore: 95, description: 'Critical thinking and debugging' },
      { name: 'Code Review', category: 'soft', relevanceScore: 80, description: 'Collaborative code quality' },
      { name: 'Technical Documentation', category: 'soft', relevanceScore: 75, description: 'Clear technical communication' },
      { name: 'Agile Methodologies', category: 'soft', relevanceScore: 85, description: 'Scrum, Kanban practices' },
      { name: 'Team Leadership', category: 'leadership', relevanceScore: 60, description: 'Leading technical teams' },
      { name: 'Data Privacy & Security', category: 'compliance', relevanceScore: 85, description: 'GDPR, security best practices' }
    ],
    'Marketing': [
      { name: 'Digital Marketing', category: 'technical', relevanceScore: 95, description: 'Online marketing strategies' },
      { name: 'Google Analytics', category: 'technical', relevanceScore: 90, description: 'Web analytics and insights' },
      { name: 'Social Media Management', category: 'technical', relevanceScore: 85, description: 'Platform-specific strategies' },
      { name: 'Content Marketing', category: 'technical', relevanceScore: 80, description: 'Content strategy and creation' },
      { name: 'SEO/SEM', category: 'technical', relevanceScore: 85, description: 'Search engine optimization' },
      { name: 'Creative Thinking', category: 'soft', relevanceScore: 95, description: 'Innovation and ideation' },
      { name: 'Communication Skills', category: 'soft', relevanceScore: 90, description: 'Clear messaging and presentation' },
      { name: 'Brand Management', category: 'soft', relevanceScore: 80, description: 'Maintaining brand consistency' },
      { name: 'Customer Insights', category: 'soft', relevanceScore: 85, description: 'Understanding target audiences' },
      { name: 'Campaign Management', category: 'leadership', relevanceScore: 70, description: 'Leading marketing initiatives' },
      { name: 'Marketing Compliance', category: 'compliance', relevanceScore: 75, description: 'Advertising standards and regulations' }
    ],
    'Sales': [
      { name: 'CRM Software', category: 'technical', relevanceScore: 90, description: 'Salesforce, HubSpot proficiency' },
      { name: 'Sales Analytics', category: 'technical', relevanceScore: 80, description: 'Data-driven sales insights' },
      { name: 'Lead Generation', category: 'technical', relevanceScore: 85, description: 'Prospecting techniques' },
      { name: 'Negotiation Skills', category: 'soft', relevanceScore: 95, description: 'Closing deals effectively' },
      { name: 'Relationship Building', category: 'soft', relevanceScore: 90, description: 'Long-term customer relationships' },
      { name: 'Presentation Skills', category: 'soft', relevanceScore: 85, description: 'Compelling sales presentations' },
      { name: 'Active Listening', category: 'soft', relevanceScore: 80, description: 'Understanding customer needs' },
      { name: 'Sales Team Leadership', category: 'leadership', relevanceScore: 65, description: 'Managing sales teams' },
      { name: 'Pipeline Management', category: 'leadership', relevanceScore: 70, description: 'Sales process optimization' },
      { name: 'Sales Compliance', category: 'compliance', relevanceScore: 80, description: 'Ethical sales practices' }
    ],
    'Human Resources': [
      { name: 'HRIS Systems', category: 'technical', relevanceScore: 85, description: 'HR information systems' },
      { name: 'Recruitment Technology', category: 'technical', relevanceScore: 80, description: 'ATS and sourcing tools' },
      { name: 'HR Analytics', category: 'technical', relevanceScore: 75, description: 'People analytics and metrics' },
      { name: 'Employee Relations', category: 'soft', relevanceScore: 95, description: 'Conflict resolution and mediation' },
      { name: 'Interviewing Skills', category: 'soft', relevanceScore: 90, description: 'Effective candidate assessment' },
      { name: 'Training & Development', category: 'soft', relevanceScore: 85, description: 'Employee skill development' },
      { name: 'Change Management', category: 'soft', relevanceScore: 80, description: 'Organizational change facilitation' },
      { name: 'HR Strategy', category: 'leadership', relevanceScore: 70, description: 'Strategic HR planning' },
      { name: 'Performance Management', category: 'leadership', relevanceScore: 75, description: 'Employee performance systems' },
      { name: 'Employment Law', category: 'compliance', relevanceScore: 95, description: 'Legal compliance in HR' }
    ],
    'Finance': [
      { name: 'Financial Modeling', category: 'technical', relevanceScore: 90, description: 'Excel and financial analysis' },
      { name: 'ERP Systems', category: 'technical', relevanceScore: 85, description: 'SAP, Oracle proficiency' },
      { name: 'Data Analysis', category: 'technical', relevanceScore: 80, description: 'Statistical analysis and reporting' },
      { name: 'Financial Reporting', category: 'technical', relevanceScore: 85, description: 'GAAP and financial statements' },
      { name: 'Attention to Detail', category: 'soft', relevanceScore: 95, description: 'Accuracy in financial work' },
      { name: 'Risk Assessment', category: 'soft', relevanceScore: 85, description: 'Identifying financial risks' },
      { name: 'Business Acumen', category: 'soft', relevanceScore: 80, description: 'Understanding business operations' },
      { name: 'Financial Planning', category: 'leadership', relevanceScore: 75, description: 'Strategic financial planning' },
      { name: 'Budget Management', category: 'leadership', relevanceScore: 80, description: 'Budget planning and control' },
      { name: 'Financial Compliance', category: 'compliance', relevanceScore: 95, description: 'SOX, regulatory compliance' }
    ]
  };

  // Role-level multipliers
  private static roleLevelMultipliers: Record<string, Record<string, number>> = {
    'junior': { technical: 1.2, soft: 1.0, leadership: 0.3, compliance: 0.8 },
    'mid': { technical: 1.0, soft: 1.1, leadership: 0.7, compliance: 1.0 },
    'senior': { technical: 0.9, soft: 1.0, leadership: 1.2, compliance: 1.1 },
    'lead': { technical: 0.8, soft: 0.9, leadership: 1.4, compliance: 1.2 },
    'manager': { technical: 0.6, soft: 1.0, leadership: 1.5, compliance: 1.3 },
    'director': { technical: 0.4, soft: 0.8, leadership: 1.6, compliance: 1.4 }
  };

  // Training modalities by effectiveness
  private static trainingModalities: TrainingModality[] = [
    { name: 'Hands-on Workshops', description: 'Interactive, practical learning', popularity: 85, effectiveness: 90 },
    { name: 'Online Courses', description: 'Self-paced digital learning', popularity: 95, effectiveness: 75 },
    { name: 'Mentoring', description: 'One-on-one guidance', popularity: 70, effectiveness: 95 },
    { name: 'Conference/Seminars', description: 'Industry events and networking', popularity: 60, effectiveness: 70 },
    { name: 'Internal Training', description: 'Company-specific training', popularity: 80, effectiveness: 80 },
    { name: 'Certification Programs', description: 'Formal skill certification', popularity: 75, effectiveness: 85 },
    { name: 'Team Projects', description: 'Learning through collaboration', popularity: 70, effectiveness: 80 },
    { name: 'Job Shadowing', description: 'Learning through observation', popularity: 50, effectiveness: 75 }
  ];

  /**
   * Get skill suggestions based on department and role
   */
  static getSkillSuggestions(department: string, role: string = ''): SkillSuggestion[] {
    const departmentSkills = this.departmentSkills[department] || [];
    const roleLevel = this.extractRoleLevel(role);
    const multipliers = this.roleLevelMultipliers[roleLevel] || this.roleLevelMultipliers['mid'];

    return departmentSkills
      .map(skill => ({
        ...skill,
        relevanceScore: Math.min(100, skill.relevanceScore * multipliers[skill.category])
      }))
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, 12); // Top 12 suggestions
  }

  /**
   * Get training modality suggestions
   */
  static getTrainingModalitySuggestions(department: string, role: string = ''): string[] {
    const roleLevel = this.extractRoleLevel(role);
    
    // Adjust recommendations based on role level
    let modalities = [...this.trainingModalities];
    
    if (roleLevel === 'junior') {
      // Junior roles benefit more from structured learning
      modalities = modalities.filter(m => 
        ['Hands-on Workshops', 'Online Courses', 'Mentoring', 'Internal Training'].includes(m.name)
      );
    } else if (['manager', 'director'].includes(roleLevel)) {
      // Senior roles benefit from leadership-focused learning
      modalities = modalities.filter(m => 
        ['Conference/Seminars', 'Mentoring', 'Certification Programs', 'Internal Training'].includes(m.name)
      );
    }

    return modalities
      .sort((a, b) => (b.popularity + b.effectiveness) - (a.popularity + a.effectiveness))
      .slice(0, 4)
      .map(m => m.name);
  }

  /**
   * Get smart defaults for a given profile
   */
  static getSmartDefaults(department: string, role: string = '', yearsInRole: number = 0): SmartDefaults {
    const skills = this.getSkillSuggestions(department, role);
    const trainingModalities = this.getTrainingModalitySuggestions(department, role);
    
    // Determine learning style based on role level
    const roleLevel = this.extractRoleLevel(role);
    const learningStyle = this.suggestLearningStyle(roleLevel, yearsInRole);
    
    // Budget suggestions based on role level and department
    const budget = this.suggestBudget(department, roleLevel, yearsInRole);
    
    // Timeline suggestion
    const timeline = this.suggestTimeline(roleLevel, skills.length);

    return {
      skills,
      trainingModalities,
      learningStyle,
      budget,
      timeline
    };
  }

  /**
   * Extract role level from role title
   */
  private static extractRoleLevel(role: string): string {
    const roleLower = role.toLowerCase();
    
    if (roleLower.includes('director') || roleLower.includes('vp') || roleLower.includes('head')) {
      return 'director';
    }
    if (roleLower.includes('manager') || roleLower.includes('lead')) {
      return 'manager';
    }
    if (roleLower.includes('senior') || roleLower.includes('sr')) {
      return 'senior';
    }
    if (roleLower.includes('junior') || roleLower.includes('jr') || roleLower.includes('entry')) {
      return 'junior';
    }
    return 'mid';
  }

  /**
   * Suggest learning style based on profile
   */
  private static suggestLearningStyle(roleLevel: string, yearsInRole: number): string {
    if (['director', 'manager'].includes(roleLevel)) {
      return 'Strategic Learning';
    }
    if (roleLevel === 'senior' || yearsInRole > 3) {
      return 'Self-Directed Learning';
    }
    if (roleLevel === 'junior' || yearsInRole < 2) {
      return 'Structured Learning';
    }
    return 'Collaborative Learning';
  }

  /**
   * Suggest budget based on profile
   */
  private static suggestBudget(department: string, roleLevel: string, yearsInRole: number): {
    suggested: number;
    range: { min: number; max: number };
  } {
    // Base budget by department
    const departmentMultipliers: Record<string, number> = {
      'Engineering': 1.2,
      'Sales': 1.0,
      'Marketing': 1.0,
      'Finance': 0.9,
      'Human Resources': 0.8,
      'Operations': 0.8
    };

    // Role level multipliers
    const roleMultipliers: Record<string, number> = {
      'junior': 0.7,
      'mid': 1.0,
      'senior': 1.3,
      'manager': 1.6,
      'director': 2.0
    };

    const baseBudget = 2000;
    const deptMultiplier = departmentMultipliers[department] || 1.0;
    const roleMultiplier = roleMultipliers[roleLevel] || 1.0;
    
    const suggested = Math.round(baseBudget * deptMultiplier * roleMultiplier);
    
    return {
      suggested,
      range: {
        min: Math.round(suggested * 0.6),
        max: Math.round(suggested * 1.8)
      }
    };
  }

  /**
   * Suggest timeline based on profile
   */
  private static suggestTimeline(roleLevel: string, skillCount: number): string {
    const baseMonths = Math.max(3, Math.ceil(skillCount / 3));
    
    if (['director', 'manager'].includes(roleLevel)) {
      return `${baseMonths + 3}-${baseMonths + 6} months`; // Longer for leadership
    }
    if (roleLevel === 'senior') {
      return `${baseMonths}-${baseMonths + 3} months`;
    }
    return `${baseMonths}-${baseMonths + 2} months`;
  }

  /**
   * Get trending skills across all departments
   */
  static getTrendingSkills(): SkillSuggestion[] {
    return [
      { name: 'AI & Machine Learning', category: 'technical', relevanceScore: 95, description: 'Emerging technology trend' },
      { name: 'Cloud Computing', category: 'technical', relevanceScore: 90, description: 'AWS, Azure, GCP' },
      { name: 'Data Analysis', category: 'technical', relevanceScore: 85, description: 'Data-driven decision making' },
      { name: 'Remote Collaboration', category: 'soft', relevanceScore: 90, description: 'Virtual team effectiveness' },
      { name: 'Digital Transformation', category: 'soft', relevanceScore: 85, description: 'Leading organizational change' },
      { name: 'Cybersecurity Awareness', category: 'compliance', relevanceScore: 80, description: 'Security best practices' }
    ];
  }
}

export default SmartSuggestionsService;