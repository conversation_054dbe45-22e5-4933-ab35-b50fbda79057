import React from 'react';
import { Box, Group, Text, Progress, Alert, Stack, Tooltip, Badge } from '@mantine/core';
import { IconCheck, IconAlertTriangle, IconX, IconTrendingUp, IconTarget } from '@tabler/icons-react';
import { motion } from 'framer-motion';

export interface ValidationRule {
  field: string;
  required: boolean;
  minLength?: number;
  maxLength?: number;
  customValidation?: (value: any) => { isValid: boolean; message?: string };
  qualityBonus?: number; // Extra points for quality data
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  qualityScore: number;
  completionScore: number;
  suggestions: string[];
}

interface EnhancedValidationProps {
  data: Record<string, any>;
  rules: ValidationRule[];
  showDetails?: boolean;
  onValidationChange?: (result: ValidationResult) => void;
}

export const EnhancedValidation: React.FC<EnhancedValidationProps> = ({
  data,
  rules,
  showDetails = true,
  onValidationChange
}) => {
  // Validate data against rules
  const validateData = (): ValidationResult => {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];
    let qualityPoints = 0;
    let maxQualityPoints = 0;
    let completedFields = 0;
    let totalRequiredFields = 0;

    rules.forEach(rule => {
      const value = data[rule.field];
      
      if (rule.required) {
        totalRequiredFields++;
        
        if (!value || (typeof value === 'string' && value.trim() === '') || 
            (Array.isArray(value) && value.length === 0)) {
          errors.push(`${rule.field.charAt(0).toUpperCase() + rule.field.slice(1)} is required`);
        } else {
          completedFields++;
        }
      }

      if (value) {
        // Length validations
        if (rule.minLength && typeof value === 'string' && value.length < rule.minLength) {
          warnings.push(`${rule.field} should be at least ${rule.minLength} characters`);
        }
        
        if (rule.maxLength && typeof value === 'string' && value.length > rule.maxLength) {
          warnings.push(`${rule.field} should not exceed ${rule.maxLength} characters`);
        }

        // Custom validation
        if (rule.customValidation) {
          const customResult = rule.customValidation(value);
          if (!customResult.isValid && customResult.message) {
            warnings.push(customResult.message);
          }
        }

        // Quality scoring
        if (rule.qualityBonus) {
          maxQualityPoints += rule.qualityBonus;
          
          // Award quality points based on data richness
          if (typeof value === 'string' && value.trim().length > 10) {
            qualityPoints += rule.qualityBonus;
          } else if (Array.isArray(value) && value.length >= 3) {
            qualityPoints += rule.qualityBonus;
          } else if (typeof value === 'number' && value > 0) {
            qualityPoints += rule.qualityBonus;
          }
        }
      }
    });

    // Generate suggestions
    if (completedFields < totalRequiredFields) {
      suggestions.push('Complete all required fields to improve your score');
    }
    
    if (qualityPoints < maxQualityPoints * 0.7) {
      suggestions.push('Add more detailed information to improve data quality');
    }

    if (warnings.length > 0) {
      suggestions.push('Address warnings to enhance your submission');
    }

    const completionScore = totalRequiredFields > 0 ? (completedFields / totalRequiredFields) * 100 : 100;
    const qualityScore = maxQualityPoints > 0 ? (qualityPoints / maxQualityPoints) * 100 : 100;

    const result: ValidationResult = {
      isValid: errors.length === 0,
      errors,
      warnings,
      qualityScore: Math.round(qualityScore),
      completionScore: Math.round(completionScore),
      suggestions
    };

    onValidationChange?.(result);
    return result;
  };

  const validation = validateData();

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'green';
    if (score >= 60) return 'yellow';
    if (score >= 40) return 'orange';
    return 'red';
  };

  const getScoreLabel = (score: number) => {
    if (score >= 90) return 'Excellent';
    if (score >= 80) return 'Very Good';
    if (score >= 70) return 'Good';
    if (score >= 60) return 'Fair';
    if (score >= 40) return 'Poor';
    return 'Needs Work';
  };

  if (!showDetails && validation.isValid && validation.warnings.length === 0) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Box mt="md">
        {/* Validation Summary */}
        <Group justify="space-between" mb="sm">
          <Group gap="xs">
            {validation.isValid ? (
              <IconCheck size={16} color="green" />
            ) : (
              <IconAlertTriangle size={16} color="orange" />
            )}
            <Text size="sm" fw={600}>
              Validation Status
            </Text>
          </Group>
          
          <Group gap="md">
            <Tooltip label={`Completion: ${validation.completionScore}%`}>
              <Badge 
                color={getScoreColor(validation.completionScore)} 
                variant="light"
                leftSection={<IconTarget size={12} />}
              >
                {validation.completionScore}% Complete
              </Badge>
            </Tooltip>
            
            <Tooltip label={`Quality: ${validation.qualityScore}%`}>
              <Badge 
                color={getScoreColor(validation.qualityScore)} 
                variant="light"
                leftSection={<IconTrendingUp size={12} />}
              >
                {getScoreLabel(validation.qualityScore)}
              </Badge>
            </Tooltip>
          </Group>
        </Group>

        {/* Progress Bars */}
        <Group grow mb="md">
          <Box>
            <Text size="xs" c="dimmed" mb={4}>Completion</Text>
            <Progress 
              value={validation.completionScore} 
              color={getScoreColor(validation.completionScore)}
              size="sm"
              radius="xl"
            />
          </Box>
          <Box>
            <Text size="xs" c="dimmed" mb={4}>Quality</Text>
            <Progress 
              value={validation.qualityScore} 
              color={getScoreColor(validation.qualityScore)}
              size="sm"
              radius="xl"
            />
          </Box>
        </Group>

        {/* Errors */}
        {validation.errors.length > 0 && (
          <Alert 
            icon={<IconX size={16} />} 
            color="red" 
            variant="light" 
            mb="sm"
          >
            <Stack gap="xs">
              <Text size="sm" fw={600}>Required fields missing:</Text>
              {validation.errors.map((error, index) => (
                <Text key={index} size="sm">• {error}</Text>
              ))}
            </Stack>
          </Alert>
        )}

        {/* Warnings */}
        {validation.warnings.length > 0 && (
          <Alert 
            icon={<IconAlertTriangle size={16} />} 
            color="yellow" 
            variant="light" 
            mb="sm"
          >
            <Stack gap="xs">
              <Text size="sm" fw={600}>Suggestions for improvement:</Text>
              {validation.warnings.map((warning, index) => (
                <Text key={index} size="sm">• {warning}</Text>
              ))}
            </Stack>
          </Alert>
        )}

        {/* Suggestions */}
        {validation.suggestions.length > 0 && validation.isValid && (
          <Alert 
            icon={<IconTrendingUp size={16} />} 
            color="blue" 
            variant="light"
          >
            <Stack gap="xs">
              <Text size="sm" fw={600}>Tips to improve your submission:</Text>
              {validation.suggestions.map((suggestion, index) => (
                <Text key={index} size="sm">💡 {suggestion}</Text>
              ))}
            </Stack>
          </Alert>
        )}
      </Box>
    </motion.div>
  );
};

export default EnhancedValidation;