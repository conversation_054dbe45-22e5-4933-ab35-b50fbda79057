import React, { useState, useEffect, useMemo } from 'react';
import {
  Stack,
  Grid,
  TextInput,
  Select,
  NumberInput,
  Textarea,
  Group,
  Button,
  Card,
  Text,
  Autocomplete,
  Badge,
  Box,
  Avatar,
  ActionIcon,
  Tooltip,
  Chip,
  Alert
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import { IconSearch, IconUser, IconBuilding, IconMail, IconMapPin, IconEdit, IconSparkles, IconLightbulb, IconTrendingUp } from '@tabler/icons-react';
import { motion } from 'framer-motion';
import { useTrainingFormStore } from '../../stores/trainingFormStore';
import { EmployeeInformation } from '../../../../types/training-needs-analysis';
import SmartSuggestionsService from '../../services/smartSuggestions';

const departments = [
  'Engineering',
  'Marketing',
  'Sales',
  'Human Resources',
  'Finance',
  'Operations',
  'Customer Service',
  'Legal',
  'IT',
  'Research & Development'
];

const locations = [
  'New York',
  'Los Angeles',
  'Chicago',
  'Houston',
  'Phoenix',
  'Philadelphia',
  'San Antonio',
  'San Diego',
  'Dallas',
  'San Jose',
  'Remote'
];

export const EmployeeInformationStep: React.FC = () => {
  const { 
    formData, 
    updateFormData, 
    employeeData, 
    employeeSearchResults,
    searchEmployees, 
    loadEmployeeData 
  } = useTrainingFormStore();

  const [searchQuery, setSearchQuery] = useState('');
  const [isEditing, setIsEditing] = useState(!employeeData);
  const [localEmployeeInfo, setLocalEmployeeInfo] = useState<Partial<EmployeeInformation>>(
    formData.employeeInfo || {}
  );
  const [showSuggestions, setShowSuggestions] = useState(false);

  // Smart suggestions based on current employee data
  const smartSuggestions = useMemo(() => {
    if (localEmployeeInfo.department && localEmployeeInfo.role) {
      return SmartSuggestionsService.getSmartDefaults(
        localEmployeeInfo.department,
        localEmployeeInfo.role,
        localEmployeeInfo.yearsInRole || 0
      );
    }
    return null;
  }, [localEmployeeInfo.department, localEmployeeInfo.role, localEmployeeInfo.yearsInRole]);

  // Role suggestions based on department
  const roleSuggestions = useMemo(() => {
    const rolesByDepartment: Record<string, string[]> = {
      'Engineering': ['Software Engineer', 'Senior Developer', 'Frontend Developer', 'Backend Developer', 'DevOps Engineer', 'Engineering Manager', 'Tech Lead', 'Solution Architect'],
      'Marketing': ['Marketing Specialist', 'Digital Marketing Manager', 'Content Creator', 'SEO Specialist', 'Marketing Director', 'Brand Manager', 'Campaign Manager'],
      'Sales': ['Sales Representative', 'Account Manager', 'Sales Manager', 'Business Development', 'Sales Director', 'Customer Success Manager'],
      'Human Resources': ['HR Specialist', 'Recruiter', 'HR Manager', 'People Operations', 'HR Business Partner', 'Talent Acquisition'],
      'Finance': ['Financial Analyst', 'Accountant', 'Finance Manager', 'Controller', 'CFO', 'Financial Planner'],
      'Operations': ['Operations Manager', 'Project Manager', 'Business Analyst', 'Operations Director', 'Process Improvement Specialist']
    };
    
    return rolesByDepartment[localEmployeeInfo.department || ''] || [];
  }, [localEmployeeInfo.department]);

  // Manager suggestions (mock - in real app would come from API)
  const managerSuggestions = useMemo(() => {
    const managersByDepartment: Record<string, string[]> = {
      'Engineering': ['Sarah Chen - Engineering Director', 'Mike Johnson - Senior Engineering Manager', 'David Kim - Tech Lead'],
      'Marketing': ['Lisa Rodriguez - Marketing Director', 'Tom Wilson - Marketing Manager', 'Anna Lee - Digital Marketing Lead'],
      'Sales': ['Robert Brown - Sales Director', 'Jennifer Davis - Sales Manager', 'Mark Thompson - Senior Account Manager'],
      'Human Resources': ['Emily Parker - HR Director', 'James Miller - HR Manager', 'Michelle Wong - People Operations'],
      'Finance': ['Daniel Garcia - Finance Director', 'Susan Taylor - Finance Manager', 'Kevin Liu - Senior Financial Analyst'],
      'Operations': ['Rachel Adams - Operations Director', 'Steven Clark - Operations Manager', 'Maria Santos - Project Manager']
    };
    
    return managersByDepartment[localEmployeeInfo.department || ''] || [];
  }, [localEmployeeInfo.department]);

  // Handle employee search
  const handleEmployeeSearch = async (query: string) => {
    setSearchQuery(query);
    if (query.length > 1) {
      await searchEmployees(query);
    }
  };

  // Handle employee selection
  const handleEmployeeSelect = async (employeeId: string) => {
    await loadEmployeeData(employeeId);
    setIsEditing(false);
  };

  // Handle manual input changes
  const handleInputChange = (field: keyof EmployeeInformation, value: any) => {
    const updatedInfo = { ...localEmployeeInfo, [field]: value };
    setLocalEmployeeInfo(updatedInfo);
    updateFormData('employeeInfo', updatedInfo);
  };

  // Save employee data
  useEffect(() => {
    if (employeeData && !isEditing) {
      setLocalEmployeeInfo(employeeData);
      updateFormData('employeeInfo', employeeData);
    }
  }, [employeeData, isEditing, updateFormData]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Stack gap="lg">
        <div>
          <Text size="xl" fw={700} mb="xs">
            Employee Information
          </Text>
          <Text size="md" c="dimmed">
            Search for an existing employee or enter information manually
          </Text>
        </div>

        {/* Employee Search */}
        {isEditing && (
          <Card withBorder p="md">
            <Text size="sm" fw={600} mb="sm">
              Search Employee Database
            </Text>
            <Autocomplete
              placeholder="Search by name, department, or employee number..."
              leftSection={<IconSearch size={16} />}
              data={employeeSearchResults.map(emp => `${emp.name} - ${emp.department} - ${emp.employeeNumber || emp.id}`)}
              onChange={(value) => {
                setSearchQuery(value);
                handleEmployeeSearch(value);
              }}
              onOptionSubmit={(value) => {
                const employee = employeeSearchResults.find(emp =>
                  `${emp.name} - ${emp.department} - ${emp.employeeNumber || emp.id}` === value
                );
                if (employee) {
                  handleEmployeeSelect(employee.id);
                }
              }}
              value={searchQuery}
              maxDropdownHeight={200}
            />
            
            {employeeSearchResults.length > 0 && (
              <Stack gap="xs" mt="sm">
                <Text size="xs" c="dimmed">Search Results:</Text>
                {employeeSearchResults.slice(0, 3).map((employee) => (
                  <motion.div
                    key={employee.id}
                    whileHover={{ scale: 1.01 }}
                    whileTap={{ scale: 0.99 }}
                  >
                    <Card 
                      withBorder 
                      p="xs" 
                      style={{ cursor: 'pointer' }}
                      onClick={() => handleEmployeeSelect(employee.id)}
                    >
                      <Group>
                        <Avatar size="sm" color="blue">
                          <IconUser size={16} />
                        </Avatar>
                        <div style={{ flex: 1 }}>
                          <Text size="sm" fw={600}>{employee.name}</Text>
                          <Text size="xs" c="dimmed">
                            {employee.role} • {employee.department}
                          </Text>
                        </div>
                        <Badge size="xs" variant="light">
                          {employee.employeeNumber || employee.id}
                        </Badge>
                      </Group>
                    </Card>
                  </motion.div>
                ))}
              </Stack>
            )}
          </Card>
        )}

        {/* Employee Details */}
        <Card withBorder p="md">
          <Group justify="space-between" mb="md">
            <Text size="sm" fw={600}>
              Employee Details
            </Text>
            {!isEditing && (
              <ActionIcon
                variant="subtle"
                onClick={() => setIsEditing(true)}
                aria-label="Edit employee information"
              >
                <IconEdit size={16} />
              </ActionIcon>
            )}
          </Group>

          <Grid>
            {/* Basic Information */}
            <Grid.Col span={6}>
              <TextInput
                label="Full Name"
                placeholder="Enter employee name"
                required
                leftSection={<IconUser size={16} />}
                value={localEmployeeInfo.name || ''}
                onChange={(e) => handleInputChange('name', e.target.value)}
                disabled={!isEditing}
              />
            </Grid.Col>

            <Grid.Col span={6}>
              <TextInput
                label="Employee Number"
                placeholder="e.g., EMP001"
                leftSection={<IconUser size={16} />}
                value={localEmployeeInfo.employeeNumber || ''}
                onChange={(e) => handleInputChange('employeeNumber', e.target.value)}
                disabled={!isEditing}
              />
            </Grid.Col>

            <Grid.Col span={6}>
              <TextInput
                label="Email Address"
                placeholder="<EMAIL>"
                type="email"
                required
                leftSection={<IconMail size={16} />}
                value={localEmployeeInfo.email || ''}
                onChange={(e) => handleInputChange('email', e.target.value)}
                disabled={!isEditing}
              />
            </Grid.Col>

            <Grid.Col span={6}>
              <Select
                label="Department"
                placeholder="Select department"
                required
                leftSection={<IconBuilding size={16} />}
                data={departments}
                value={localEmployeeInfo.department || null}
                onChange={(value) => handleInputChange('department', value)}
                disabled={!isEditing}
                searchable
              />
            </Grid.Col>

            <Grid.Col span={6}>
              <Autocomplete
                label="Job Title"
                placeholder="e.g., Senior Developer"
                required
                data={roleSuggestions}
                value={localEmployeeInfo.role || ''}
                onChange={(value) => handleInputChange('role', value)}
                disabled={!isEditing}
                rightSection={isEditing && roleSuggestions.length > 0 ? (
                  <Tooltip label={`${roleSuggestions.length} suggestions available`}>
                    <IconSparkles size={16} color="blue" />
                  </Tooltip>
                ) : null}
              />
            </Grid.Col>

            <Grid.Col span={6}>
              <Autocomplete
                label="Manager"
                placeholder="Select or enter manager's name"
                required
                data={managerSuggestions}
                value={localEmployeeInfo.manager || ''}
                onChange={(value) => handleInputChange('manager', value)}
                disabled={!isEditing}
                rightSection={isEditing && managerSuggestions.length > 0 ? (
                  <Tooltip label={`${managerSuggestions.length} managers in ${localEmployeeInfo.department || 'department'}`}>
                    <IconSparkles size={16} color="blue" />
                  </Tooltip>
                ) : null}
              />
            </Grid.Col>

            <Grid.Col span={6}>
              <DatePickerInput
                label="Hire Date"
                placeholder="Select hire date"
                required
                value={localEmployeeInfo.hireDate || null}
                onChange={(date) => handleInputChange('hireDate', date)}
                disabled={!isEditing}
              />
            </Grid.Col>

            <Grid.Col span={6}>
              <Select
                label="Location"
                placeholder="Select location"
                required
                leftSection={<IconMapPin size={16} />}
                data={locations}
                value={localEmployeeInfo.location || null}
                onChange={(value) => handleInputChange('location', value)}
                disabled={!isEditing}
                searchable
              />
            </Grid.Col>

            {/* Additional Information */}
            <Grid.Col span={6}>
              <NumberInput
                label="Years in Current Role"
                placeholder="0"
                min={0}
                max={50}
                value={localEmployeeInfo.yearsInRole || 0}
                onChange={(value) => handleInputChange('yearsInRole', value)}
                disabled={!isEditing}
              />
            </Grid.Col>

            <Grid.Col span={6}>
              <NumberInput
                label="Direct Reports"
                placeholder="0"
                min={0}
                max={100}
                value={localEmployeeInfo.directReports || 0}
                onChange={(value) => handleInputChange('directReports', value)}
                disabled={!isEditing}
              />
            </Grid.Col>
          </Grid>

          {isEditing && (
            <Group justify="flex-end" mt="md">
              <Button
                variant="outline"
                onClick={() => {
                  setIsEditing(false);
                  if (employeeData) {
                    setLocalEmployeeInfo(employeeData);
                    updateFormData('employeeInfo', employeeData);
                  }
                }}
              >
                Cancel
              </Button>
              <Button onClick={() => setIsEditing(false)}>
                Save Information
              </Button>
            </Group>
          )}
        </Card>

        {/* Smart Suggestions Panel */}
        {smartSuggestions && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card withBorder p="md" bg="gradient-to-r from-blue-50 to-purple-50">
              <Group justify="space-between" mb="md">
                <Group gap="xs">
                  <IconSparkles size={20} color="blue" />
                  <Text size="md" fw={600} c="blue.8">
                    Smart Training Suggestions
                  </Text>
                </Group>
                <Button
                  size="xs"
                  variant="light"
                  onClick={() => setShowSuggestions(!showSuggestions)}
                  rightSection={<IconTrendingUp size={14} />}
                >
                  {showSuggestions ? 'Hide' : 'Show'} Details
                </Button>
              </Group>

              <Text size="sm" c="dimmed" mb="md">
                Based on {localEmployeeInfo.role} role in {localEmployeeInfo.department}, here are personalized recommendations:
              </Text>

              {/* Quick Preview */}
              <Grid mb="md">
                <Grid.Col span={4}>
                  <Box ta="center" p="sm" bg="white" style={{ borderRadius: '8px' }}>
                    <Text size="xl" fw={700} c="blue">
                      {smartSuggestions.skills.length}
                    </Text>
                    <Text size="xs" c="dimmed">Suggested Skills</Text>
                  </Box>
                </Grid.Col>
                <Grid.Col span={4}>
                  <Box ta="center" p="sm" bg="white" style={{ borderRadius: '8px' }}>
                    <Text size="xl" fw={700} c="green">
                      ${smartSuggestions.budget.suggested.toLocaleString()}
                    </Text>
                    <Text size="xs" c="dimmed">Recommended Budget</Text>
                  </Box>
                </Grid.Col>
                <Grid.Col span={4}>
                  <Box ta="center" p="sm" bg="white" style={{ borderRadius: '8px' }}>
                    <Text size="xl" fw={700} c="orange">
                      {smartSuggestions.timeline}
                    </Text>
                    <Text size="xs" c="dimmed">Timeline</Text>
                  </Box>
                </Grid.Col>
              </Grid>

              {/* Detailed Suggestions */}
              {showSuggestions && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  transition={{ duration: 0.3 }}
                >
                  <Stack gap="md">
                    {/* Top Skills */}
                    <Box>
                      <Group gap="xs" mb="xs">
                        <IconLightbulb size={16} color="blue" />
                        <Text size="sm" fw={600}>Top Recommended Skills</Text>
                      </Group>
                      <Group gap="xs">
                        {smartSuggestions.skills.slice(0, 6).map((skill, index) => (
                          <Chip
                            key={skill.name}
                            size="sm"
                            variant="light"
                            color={skill.category === 'technical' ? 'blue' : 
                                   skill.category === 'soft' ? 'green' : 
                                   skill.category === 'leadership' ? 'orange' : 'purple'}
                          >
                            {skill.name}
                          </Chip>
                        ))}
                      </Group>
                    </Box>

                    {/* Training Modalities */}
                    <Box>
                      <Group gap="xs" mb="xs">
                        <IconTrendingUp size={16} color="green" />
                        <Text size="sm" fw={600}>Recommended Learning Methods</Text>
                      </Group>
                      <Group gap="xs">
                        {smartSuggestions.trainingModalities.map((modality) => (
                          <Badge key={modality} variant="outline" color="green">
                            {modality}
                          </Badge>
                        ))}
                      </Group>
                    </Box>

                    {/* Budget Breakdown */}
                    <Box>
                      <Text size="sm" fw={600} mb="xs">Budget Guidance</Text>
                      <Text size="xs" c="dimmed">
                        Suggested: ${smartSuggestions.budget.suggested.toLocaleString()} 
                        (Range: ${smartSuggestions.budget.range.min.toLocaleString()} - ${smartSuggestions.budget.range.max.toLocaleString()})
                      </Text>
                    </Box>

                    <Alert icon={<IconSparkles size={16} />} color="blue" variant="light">
                      <Text size="sm">
                        💡 These suggestions will automatically populate in later steps. You can customize them as needed.
                      </Text>
                    </Alert>
                  </Stack>
                </motion.div>
              )}
            </Card>
          </motion.div>
        )}

        {/* Summary Card */}
        {localEmployeeInfo.name && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
          >
            <Card withBorder p="md" bg="blue.0">
              <Group>
                <Avatar size="lg" color="blue">
                  <IconUser size={24} />
                </Avatar>
                <div style={{ flex: 1 }}>
                  <Text size="lg" fw={600}>{localEmployeeInfo.name}</Text>
                  <Text size="sm" c="dimmed">
                    {localEmployeeInfo.role} • {localEmployeeInfo.department}
                  </Text>
                  <Text size="xs" c="dimmed">
                    Manager: {localEmployeeInfo.manager} • Location: {localEmployeeInfo.location}
                  </Text>
                </div>
                <div style={{ textAlign: 'right' }}>
                  <Badge color="blue" variant="light">
                    {localEmployeeInfo.yearsInRole || 0} years in role
                  </Badge>
                  {(localEmployeeInfo.directReports || 0) > 0 && (
                    <Text size="xs" c="dimmed" mt="xs">
                      {localEmployeeInfo.directReports} direct reports
                    </Text>
                  )}
                </div>
              </Group>
            </Card>
          </motion.div>
        )}
      </Stack>
    </motion.div>
  );
};

export default EmployeeInformationStep;