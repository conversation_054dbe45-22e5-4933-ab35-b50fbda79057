import React, { useMemo } from 'react';
import { Group, Text, Progress, Stepper, Box, Badge, Tooltip, ActionIcon, Stack, RingProgress } from '@mantine/core';
import { IconCheck, IconClock, IconLock, IconAlertTriangle, IconTarget, IconTrendingUp } from '@tabler/icons-react';
import { motion } from 'framer-motion';
import { useTrainingFormStore } from '../../stores/trainingFormStore';
import { FORM_STEPS } from '../../../../types/training-needs-analysis';

export const ProgressIndicator: React.FC = () => {
  const currentStep = useTrainingFormStore((state) => state.currentStep);
  const stepValidationStatus = useTrainingFormStore((state) => state.stepValidationStatus);
  const approvalStatus = useTrainingFormStore((state) => state.approvalStatus);
  const formData = useTrainingFormStore((state) => state.formData);
  const setCurrentStep = useTrainingFormStore((state) => state.setCurrentStep);
  const validateStep = useTrainingFormStore((state) => state.validateStep);

  // Calculate overall progress
  const completedSteps = stepValidationStatus.filter(Boolean).length;
  const progressPercentage = (completedSteps / FORM_STEPS.length) * 100;

  // Calculate data quality score
  const dataQualityScore = useMemo(() => {
    let totalFields = 0;
    let filledFields = 0;
    
    // Employee Information (5 required fields)
    totalFields += 5;
    if (formData.employeeInfo?.name) filledFields++;
    if (formData.employeeInfo?.department) filledFields++;
    if (formData.employeeInfo?.role) filledFields++;
    if (formData.employeeInfo?.manager) filledFields++;
    if (formData.employeeInfo?.email) filledFields++;
    
    // Skills Assessment (at least 3 skills)
    totalFields += 3;
    const skillsCount = (formData.skillsAssessment?.technicalSkills?.length || 0) + 
                       (formData.skillsAssessment?.softSkills?.length || 0);
    filledFields += Math.min(skillsCount, 3);
    
    // Performance Gaps (at least 1)
    totalFields += 1;
    if (formData.performanceGaps?.length) filledFields++;
    
    // Training Preferences (2 required)
    totalFields += 2;
    if (formData.trainingPreferences?.preferredModalities?.length) filledFields++;
    if (formData.trainingPreferences?.learningStyle) filledFields++;
    
    // Priority Ranking (at least 1)
    totalFields += 1;
    if (formData.priorityRanking?.length) filledFields++;
    
    // Budget & Timeline (2 required)
    totalFields += 2;
    if (formData.budgetTimeline?.totalBudget) filledFields++;
    if (formData.budgetTimeline?.preferredTimeline) filledFields++;
    
    // Learning Objectives (at least 1)
    totalFields += 1;
    if (formData.learningObjectives?.length) filledFields++;
    
    // Success Metrics (at least 1)
    totalFields += 1;
    if (formData.successMetrics?.length) filledFields++;
    
    return Math.round((filledFields / totalFields) * 100);
  }, [formData]);

  // Get quality color based on score
  const getQualityColor = (score: number) => {
    if (score >= 80) return 'green';
    if (score >= 60) return 'yellow';
    if (score >= 40) return 'orange';
    return 'red';
  };

  // Handle step click for navigation
  const handleStepClick = (stepIndex: number) => {
    const stepNumber = stepIndex + 1;
    
    // Allow navigation to previous steps or the next immediate step
    if (stepNumber <= currentStep || 
        (stepNumber === currentStep + 1) ||
        stepValidationStatus[stepIndex]) {
      setCurrentStep(stepNumber);
    }
  };

  // Get step status
  const getStepStatus = (stepIndex: number): 'completed' | 'active' | 'pending' | 'locked' => {
    if (approvalStatus !== 'draft' && stepIndex >= FORM_STEPS.length - 1) {
      return 'locked';
    }
    if (stepValidationStatus[stepIndex]) {
      return 'completed';
    }
    if (stepIndex === currentStep - 1) {
      return 'active';
    }
    return 'pending';
  };

  // Check if step is clickable
  const isStepClickable = (stepIndex: number): boolean => {
    const stepNumber = stepIndex + 1;
    return (stepNumber <= currentStep || 
            stepNumber === currentStep + 1 ||
            stepValidationStatus[stepIndex]) && 
           approvalStatus === 'draft';
  };

  // Get step icon
  const getStepIcon = (stepIndex: number) => {
    const status = getStepStatus(stepIndex);
    switch (status) {
      case 'completed':
        return <IconCheck size={16} />;
      case 'active':
        return <IconClock size={16} />;
      case 'locked':
        return <IconLock size={16} />;
      default:
        return null;
    }
  };

  // Get step color
  const getStepColor = (stepIndex: number): string => {
    const status = getStepStatus(stepIndex);
    switch (status) {
      case 'completed':
        return 'green';
      case 'active':
        return 'blue';
      case 'locked':
        return 'gray';
      default:
        return 'gray';
    }
  };

  return (
    <Box mb="xl">
      {/* Header with Progress and Quality Indicators */}
      <Group justify="space-between" mb="md">
        <Text size="lg" fw={600}>
          Training Needs Analysis
        </Text>
        <Group gap="md">
          {/* Data Quality Ring */}
          <Tooltip label={`Data Quality: ${dataQualityScore}%`}>
            <Group gap="xs">
              <RingProgress
                size={40}
                thickness={4}
                sections={[{ value: dataQualityScore, color: getQualityColor(dataQualityScore) }]}
                label={
                  <Text size="xs" ta="center" fw={500}>
                    {dataQualityScore}%
                  </Text>
                }
              />
              <Stack gap={0}>
                <Text size="xs" fw={500}>Quality</Text>
                <Text size="xs" c={getQualityColor(dataQualityScore)}>
                  {dataQualityScore >= 80 ? 'Excellent' : 
                   dataQualityScore >= 60 ? 'Good' : 
                   dataQualityScore >= 40 ? 'Fair' : 'Poor'}
                </Text>
              </Stack>
            </Group>
          </Tooltip>

          <Badge 
            color={approvalStatus === 'draft' ? 'blue' : 'green'} 
            variant="filled"
            size="sm"
          >
            {approvalStatus.replace('_', ' ').toUpperCase()}
          </Badge>
          <Text size="sm" c="dimmed">
            {completedSteps} of {FORM_STEPS.length} completed
          </Text>
        </Group>
      </Group>

      {/* Enhanced Progress Bar */}
      <motion.div
        initial={{ width: 0 }}
        animate={{ width: '100%' }}
        transition={{ duration: 0.5 }}
      >
        <Progress 
          value={progressPercentage} 
          size="lg" 
          radius="xl"
          mb="lg"
          animated={progressPercentage < 100}
          color={progressPercentage === 100 ? 'green' : 'blue'}
        />
      </motion.div>

      {/* Quick Stats */}
      <Group justify="center" mb="xl">
        <Group gap="lg">
          <Tooltip label="Steps completed successfully">
            <Group gap="xs">
              <IconCheck size={16} color="green" />
              <Text size="sm">{completedSteps} Complete</Text>
            </Group>
          </Tooltip>
          
          <Tooltip label="Current step in progress">
            <Group gap="xs">
              <IconClock size={16} color="blue" />
              <Text size="sm">Step {currentStep}</Text>
            </Group>
          </Tooltip>
          
          <Tooltip label="Overall form completion">
            <Group gap="xs">
              <IconTarget size={16} color="orange" />
              <Text size="sm">{Math.round(progressPercentage)}% Done</Text>
            </Group>
          </Tooltip>
        </Group>
      </Group>

      {/* Enhanced Clickable Step Indicator */}
      <Stepper 
        active={currentStep - 1} 
        orientation="horizontal"
        size="sm"
        iconSize={32}
        allowNextStepsSelect={false}
        onStepClick={handleStepClick}
      >
        {FORM_STEPS.map((step, index) => {
          const status = getStepStatus(index);
          const color = getStepColor(index);
          const clickable = isStepClickable(index);
          
          return (
            <Stepper.Step
              key={step.id}
              label={step.title}
              description={step.description}
              icon={getStepIcon(index)}
              color={color}
              loading={status === 'active'}
              completedIcon={<IconCheck size={16} />}
              allowStepSelect={clickable}
              style={{ 
                cursor: clickable ? 'pointer' : 'default',
                opacity: status === 'locked' ? 0.5 : 1 
              }}
            >
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Group gap="xs" mt="xs">
                  <Text size="xs" c="dimmed">
                    {status === 'completed' && '✓ Complete'}
                    {status === 'active' && '⚡ In Progress'}
                    {status === 'pending' && '⏳ Pending'}
                    {status === 'locked' && '🔒 Locked'}
                  </Text>
                  {clickable && status !== 'active' && (
                    <Tooltip label="Click to navigate">
                      <IconTrendingUp size={12} color="blue" />
                    </Tooltip>
                  )}
                </Group>
              </motion.div>
            </Stepper.Step>
          );
        })}
      </Stepper>

      {/* Current Step Details with Smart Suggestions */}
      <motion.div
        key={currentStep}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Box 
          mt="lg" 
          p="md" 
          bg="gray.0" 
          style={{ borderRadius: '8px', borderLeft: '4px solid var(--mantine-color-blue-6)' }}
        >
          <Group justify="space-between" mb="xs">
            <div>
              <Text fw={600} size="md">
                Step {currentStep}: {FORM_STEPS[currentStep - 1]?.title}
              </Text>
              <Text size="sm" c="dimmed">
                {FORM_STEPS[currentStep - 1]?.description}
              </Text>
            </div>
            <Badge 
              color={stepValidationStatus[currentStep - 1] ? 'green' : 'orange'}
              variant="light"
              leftSection={stepValidationStatus[currentStep - 1] ? 
                <IconCheck size={12} /> : <IconAlertTriangle size={12} />}
            >
              {stepValidationStatus[currentStep - 1] ? 'Complete' : 'In Progress'}
            </Badge>
          </Group>
          
          {/* Progress suggestions */}
          {!stepValidationStatus[currentStep - 1] && (
            <Box mt="sm" p="sm" bg="blue.0" style={{ borderRadius: '4px' }}>
              <Group gap="xs">
                <IconTrendingUp size={14} color="blue" />
                <Text size="sm" c="blue.8">
                  💡 Tip: Complete all required fields to unlock the next step
                </Text>
              </Group>
            </Box>
          )}
          
          {/* Data quality suggestion */}
          {dataQualityScore < 70 && (
            <Box mt="sm" p="sm" bg="yellow.0" style={{ borderRadius: '4px' }}>
              <Group gap="xs">
                <IconTarget size={14} color="orange" />
                <Text size="sm" c="orange.8">
                  💫 Consider adding more details to improve data quality
                </Text>
              </Group>
            </Box>
          )}
        </Box>
      </motion.div>
    </Box>
  );
};

export default ProgressIndicator;