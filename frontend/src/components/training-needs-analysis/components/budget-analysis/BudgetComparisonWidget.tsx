import React, { useState, useMemo } from 'react';
import {
  Card,
  Grid,
  Text,
  Stack,
  Group,
  Badge,
  Progress,
  Select,
  Button,
  ActionIcon,
  Tooltip,
  RingProgress,
  Center,
  Box,
  Paper,
  Divider,
  Tabs,
  ThemeIcon,
  SimpleGrid,
  Flex
} from '@mantine/core';
import { motion, AnimatePresence } from 'framer-motion';
import {
  IconTrendingUp,
  IconTrendingDown,
  IconEqual,
  IconTarget,
  IconCurrency,
  IconCalendar,
  IconChartPie,
  IconArrowUpRight,
  IconArrowDownRight,
  IconMinus,
  IconRefresh,
  IconMaximize
} from '@tabler/icons-react';
import {
  ComposedChart,
  Line,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  RadialBarChart,
  RadialBar,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area
} from 'recharts';

interface BudgetMetric {
  id: string;
  name: string;
  planned: number;
  actual: number;
  forecast: number;
  category: string;
  priority: 'high' | 'medium' | 'low';
  trend: 'up' | 'down' | 'stable';
  lastUpdated: Date;
}

interface ComparisonPeriod {
  id: string;
  name: string;
  startDate: Date;
  endDate: Date;
  metrics: BudgetMetric[];
}

const COLORS = {
  planned: '#228be6',
  actual: '#40c057',
  forecast: '#fd7e14',
  variance: '#fa5252'
};

const CHART_COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7c7c', '#8dd1e1', '#d084d0'];

const mockComparisonData: BudgetMetric[] = [
  {
    id: '1',
    name: 'Technical Training',
    planned: 50000,
    actual: 45000,
    forecast: 48000,
    category: 'Skills Development',
    priority: 'high',
    trend: 'down',
    lastUpdated: new Date()
  },
  {
    id: '2',
    name: 'Leadership Development',
    planned: 30000,
    actual: 35000,
    forecast: 33000,
    category: 'Management',
    priority: 'high',
    trend: 'up',
    lastUpdated: new Date()
  },
  {
    id: '3',
    name: 'Compliance Training',
    planned: 15000,
    actual: 14500,
    forecast: 15200,
    category: 'Mandatory',
    priority: 'medium',
    trend: 'stable',
    lastUpdated: new Date()
  },
  {
    id: '4',
    name: 'Soft Skills',
    planned: 20000,
    actual: 22000,
    forecast: 21000,
    category: 'Personal Development',
    priority: 'medium',
    trend: 'up',
    lastUpdated: new Date()
  }
];

const mockTimeSeriesData = [
  { period: 'Q1', planned: 25000, actual: 23000, forecast: 24000 },
  { period: 'Q2', planned: 30000, actual: 32000, forecast: 31000 },
  { period: 'Q3', planned: 35000, actual: 33000, forecast: 34000 },
  { period: 'Q4', planned: 40000, actual: 38000, forecast: 39000 }
];

interface BudgetComparisonWidgetProps {
  data?: BudgetMetric[];
  period?: string;
  onPeriodChange?: (period: string) => void;
  compact?: boolean;
}

export const BudgetComparisonWidget: React.FC<BudgetComparisonWidgetProps> = ({
  data = mockComparisonData,
  period = 'current',
  onPeriodChange,
  compact = false
}) => {
  const [selectedView, setSelectedView] = useState<'overview' | 'detailed' | 'trends'>('overview');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const filteredData = useMemo(() => {
    if (selectedCategory === 'all') return data;
    return data.filter(item => item.category === selectedCategory);
  }, [data, selectedCategory]);

  const totalMetrics = useMemo(() => {
    const totals = filteredData.reduce(
      (acc, item) => ({
        planned: acc.planned + item.planned,
        actual: acc.actual + item.actual,
        forecast: acc.forecast + item.forecast
      }),
      { planned: 0, actual: 0, forecast: 0 }
    );

    const variance = totals.actual - totals.planned;
    const variancePercent = (variance / totals.planned) * 100;
    const forecastVariance = totals.forecast - totals.planned;
    const forecastVariancePercent = (forecastVariance / totals.planned) * 100;

    return {
      ...totals,
      variance,
      variancePercent,
      forecastVariance,
      forecastVariancePercent
    };
  }, [filteredData]);

  const categories = useMemo(() => {
    const uniqueCategories = [...new Set(data.map(item => item.category))];
    return [{ value: 'all', label: 'All Categories' }, ...uniqueCategories.map(cat => ({ value: cat, label: cat }))];
  }, [data]);

  const getVarianceIcon = (variance: number) => {
    if (variance > 0) return <IconArrowUpRight size={16} color={COLORS.variance} />;
    if (variance < 0) return <IconArrowDownRight size={16} color={COLORS.actual} />;
    return <IconMinus size={16} color="gray" />;
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <IconTrendingUp size={16} color={COLORS.variance} />;
      case 'down': return <IconTrendingDown size={16} color={COLORS.actual} />;
      case 'stable': return <IconEqual size={16} color="gray" />;
      default: return null;
    }
  };

  const radialData = filteredData.map((item, index) => ({
    name: item.name,
    value: (item.actual / item.planned) * 100,
    fill: CHART_COLORS[index % CHART_COLORS.length]
  }));

  if (compact) {
    return (
      <Card withBorder p="md" h={300}>
        <Group justify="space-between" mb="md">
          <Text size="md" fw={600}>Budget Overview</Text>
          <ActionIcon variant="subtle" size="sm">
            <IconMaximize size={14} />
          </ActionIcon>
        </Group>
        
        <SimpleGrid cols={2} spacing="xs">
          <Paper p="xs" withBorder>
            <Text size="xs" c="dimmed">Planned</Text>
            <Text size="sm" fw={600}>${totalMetrics.planned.toLocaleString()}</Text>
          </Paper>
          <Paper p="xs" withBorder>
            <Text size="xs" c="dimmed">Actual</Text>
            <Text size="sm" fw={600}>${totalMetrics.actual.toLocaleString()}</Text>
          </Paper>
          <Paper p="xs" withBorder>
            <Text size="xs" c="dimmed">Variance</Text>
            <Group gap="xs">
              <Text 
                size="sm" 
                fw={600}
                c={totalMetrics.variance >= 0 ? 'red' : 'green'}
              >
                {totalMetrics.variance >= 0 ? '+' : ''}${totalMetrics.variance.toLocaleString()}
              </Text>
              {getVarianceIcon(totalMetrics.variance)}
            </Group>
          </Paper>
          <Paper p="xs" withBorder>
            <Text size="xs" c="dimmed">Forecast</Text>
            <Text size="sm" fw={600}>${totalMetrics.forecast.toLocaleString()}</Text>
          </Paper>
        </SimpleGrid>
        
        <Progress
          mt="md"
          value={(totalMetrics.actual / totalMetrics.planned) * 100}
          color={totalMetrics.actual > totalMetrics.planned ? 'red' : 'green'}
          size="sm"
        />
      </Card>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
    >
      <Card withBorder p="lg">
        <Group justify="space-between" mb="lg">
          <div>
            <Text size="lg" fw={700}>Budget Comparison Analysis</Text>
            <Text size="sm" c="dimmed">Comprehensive budget performance metrics</Text>
          </div>
          <Group>
            <Select
              placeholder="Select category"
              data={categories}
              value={selectedCategory}
              onChange={(value) => setSelectedCategory(value || 'all')}
              w={200}
            />
            <ActionIcon variant="subtle" onClick={() => window.location.reload()}>
              <IconRefresh size={16} />
            </ActionIcon>
          </Group>
        </Group>

        {/* Key Metrics Cards */}
        <SimpleGrid cols={4} spacing="md" mb="lg">
          <motion.div whileHover={{ scale: 1.02 }}>
            <Paper withBorder p="md" h={120}>
              <Group justify="space-between" h="100%">
                <div>
                  <Text size="xs" tt="uppercase" fw={700} c="dimmed">Total Planned</Text>
                  <Text size="xl" fw={700}>${totalMetrics.planned.toLocaleString()}</Text>
                  <Text size="xs" c="dimmed">Budget allocation</Text>
                </div>
                <ThemeIcon size={40} variant="light" color="blue">
                  <IconTarget size={20} />
                </ThemeIcon>
              </Group>
            </Paper>
          </motion.div>

          <motion.div whileHover={{ scale: 1.02 }}>
            <Paper withBorder p="md" h={120}>
              <Group justify="space-between" h="100%">
                <div>
                  <Text size="xs" tt="uppercase" fw={700} c="dimmed">Total Actual</Text>
                  <Text size="xl" fw={700}>${totalMetrics.actual.toLocaleString()}</Text>
                  <Text size="xs" c="dimmed">Current spending</Text>
                </div>
                <ThemeIcon size={40} variant="light" color="green">
                  <IconCurrency size={20} />
                </ThemeIcon>
              </Group>
            </Paper>
          </motion.div>

          <motion.div whileHover={{ scale: 1.02 }}>
            <Paper withBorder p="md" h={120}>
              <Group justify="space-between" h="100%">
                <div>
                  <Text size="xs" tt="uppercase" fw={700} c="dimmed">Variance</Text>
                  <Group gap="xs">
                    <Text 
                      size="xl" 
                      fw={700}
                      c={totalMetrics.variance >= 0 ? 'red' : 'green'}
                    >
                      {totalMetrics.variance >= 0 ? '+' : ''}${totalMetrics.variance.toLocaleString()}
                    </Text>
                    {getVarianceIcon(totalMetrics.variance)}
                  </Group>
                  <Text size="xs" c="dimmed">
                    {totalMetrics.variancePercent.toFixed(1)}% variance
                  </Text>
                </div>
                <ThemeIcon 
                  size={40} 
                  variant="light" 
                  color={totalMetrics.variance >= 0 ? 'red' : 'green'}
                >
                  {totalMetrics.variance >= 0 ? <IconTrendingUp size={20} /> : <IconTrendingDown size={20} />}
                </ThemeIcon>
              </Group>
            </Paper>
          </motion.div>

          <motion.div whileHover={{ scale: 1.02 }}>
            <Paper withBorder p="md" h={120}>
              <Group justify="space-between" h="100%">
                <div>
                  <Text size="xs" tt="uppercase" fw={700} c="dimmed">Forecast</Text>
                  <Text size="xl" fw={700}>${totalMetrics.forecast.toLocaleString()}</Text>
                  <Text size="xs" c="dimmed">
                    {totalMetrics.forecastVariancePercent.toFixed(1)}% vs planned
                  </Text>
                </div>
                <ThemeIcon size={40} variant="light" color="orange">
                  <IconChartPie size={20} />
                </ThemeIcon>
              </Group>
            </Paper>
          </motion.div>
        </SimpleGrid>

        <Tabs value={selectedView} onChange={(value) => setSelectedView(value as any)}>
          <Tabs.List>
            <Tabs.Tab value="overview">Overview</Tabs.Tab>
            <Tabs.Tab value="detailed">Detailed Analysis</Tabs.Tab>
            <Tabs.Tab value="trends">Trends</Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="overview" pt="md">
            <Grid>
              <Grid.Col span={8}>
                <Paper withBorder p="md">
                  <Text size="md" fw={600} mb="md">Budget Performance by Category</Text>
                  <ResponsiveContainer width="100%" height={300}>
                    <ComposedChart data={filteredData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" angle={-45} textAnchor="end" height={80} />
                      <YAxis />
                      <RechartsTooltip formatter={(value) => `$${value.toLocaleString()}`} />
                      <Bar dataKey="planned" fill={COLORS.planned} name="Planned" />
                      <Bar dataKey="actual" fill={COLORS.actual} name="Actual" />
                      <Line type="monotone" dataKey="forecast" stroke={COLORS.forecast} strokeWidth={3} name="Forecast" />
                    </ComposedChart>
                  </ResponsiveContainer>
                </Paper>
              </Grid.Col>
              <Grid.Col span={4}>
                <Paper withBorder p="md">
                  <Text size="md" fw={600} mb="md">Budget Utilization</Text>
                  <ResponsiveContainer width="100%" height={300}>
                    <RadialBarChart cx="50%" cy="50%" innerRadius="20%" outerRadius="80%" data={radialData}>
                      <RadialBar
                        label={{ position: 'insideStart', fill: '#fff' }}
                        background
                        clockWise
                        dataKey="value"
                      />
                      <RechartsTooltip formatter={(value) => `${Number(value).toFixed(1)}%`} />
                    </RadialBarChart>
                  </ResponsiveContainer>
                </Paper>
              </Grid.Col>
            </Grid>
          </Tabs.Panel>

          <Tabs.Panel value="detailed" pt="md">
            <Stack gap="md">
              {filteredData.map((item, index) => (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Paper withBorder p="md">
                    <Grid align="center">
                      <Grid.Col span={3}>
                        <div>
                          <Group gap="xs" mb="xs">
                            <Text fw={600}>{item.name}</Text>
                            <Badge size="xs" color={item.priority === 'high' ? 'red' : item.priority === 'medium' ? 'orange' : 'blue'}>
                              {item.priority}
                            </Badge>
                            {getTrendIcon(item.trend)}
                          </Group>
                          <Text size="xs" c="dimmed">{item.category}</Text>
                        </div>
                      </Grid.Col>
                      <Grid.Col span={2}>
                        <div>
                          <Text size="xs" c="dimmed">Planned</Text>
                          <Text fw={600}>${item.planned.toLocaleString()}</Text>
                        </div>
                      </Grid.Col>
                      <Grid.Col span={2}>
                        <div>
                          <Text size="xs" c="dimmed">Actual</Text>
                          <Text fw={600}>${item.actual.toLocaleString()}</Text>
                        </div>
                      </Grid.Col>
                      <Grid.Col span={2}>
                        <div>
                          <Text size="xs" c="dimmed">Variance</Text>
                          <Text 
                            fw={600}
                            c={item.actual > item.planned ? 'red' : 'green'}
                          >
                            {item.actual > item.planned ? '+' : ''}${(item.actual - item.planned).toLocaleString()}
                          </Text>
                        </div>
                      </Grid.Col>
                      <Grid.Col span={3}>
                        <div>
                          <Text size="xs" c="dimmed" mb="xs">Progress</Text>
                          <Progress
                            value={(item.actual / item.planned) * 100}
                            color={item.actual > item.planned ? 'red' : 'green'}
                            size="md"
                          />
                          <Text size="xs" c="dimmed" mt="xs">
                            {((item.actual / item.planned) * 100).toFixed(1)}% of planned
                          </Text>
                        </div>
                      </Grid.Col>
                    </Grid>
                  </Paper>
                </motion.div>
              ))}
            </Stack>
          </Tabs.Panel>

          <Tabs.Panel value="trends" pt="md">
            <Paper withBorder p="md">
              <Text size="md" fw={600} mb="md">Budget Trends Over Time</Text>
              <ResponsiveContainer width="100%" height={400}>
                <AreaChart data={mockTimeSeriesData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="period" />
                  <YAxis />
                  <RechartsTooltip formatter={(value) => `$${value.toLocaleString()}`} />
                  <Area
                    type="monotone"
                    dataKey="planned"
                    stackId="1"
                    stroke={COLORS.planned}
                    fill={COLORS.planned}
                    fillOpacity={0.6}
                    name="Planned"
                  />
                  <Area
                    type="monotone"
                    dataKey="actual"
                    stackId="2"
                    stroke={COLORS.actual}
                    fill={COLORS.actual}
                    fillOpacity={0.6}
                    name="Actual"
                  />
                  <Area
                    type="monotone"
                    dataKey="forecast"
                    stackId="3"
                    stroke={COLORS.forecast}
                    fill={COLORS.forecast}
                    fillOpacity={0.6}
                    name="Forecast"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </Paper>
          </Tabs.Panel>
        </Tabs>
      </Card>
    </motion.div>
  );
};

export default BudgetComparisonWidget;