import React, { useState, useMemo } from 'react';
import {
  Container,
  Grid,
  Card,
  Text,
  Group,
  Button,
  Select,
  Stack,
  Badge,
  ActionIcon,
  Tabs,
  Paper,
  SimpleGrid,
  ThemeIcon,
  Progress,
  RingProgress,
  Center,
  Divider,
  Alert,
  Timeline,
  Anchor,
  Menu,
  Modal,
  TextInput,
  NumberInput,
  Textarea
} from '@mantine/core';
import { motion, AnimatePresence } from 'framer-motion';
import {
  IconDashboard,
  IconTrendingUp,
  IconTrendingDown,
  IconAlertTriangle,
  IconTarget,
  IconCurrency,
  IconCalendar,
  IconDownload,
  IconSettings,
  IconRefresh,
  IconPlus,
  IconEdit,
  IconTrash,
  IconEye,
  IconChartBar,
  IconReportAnalytics,
  IconBell,
  IconCheck,
  IconX,
  IconInfoCircle,
  IconFilter,
  IconSearch
} from '@tabler/icons-react';
import {
  ComposedChart,
  Line,
  Bar,
  XAxis,
  YA<PERSON>s,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  AreaChart,
  Area,
  LineChart,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from 'recharts';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import { TrainingBudgetAnalysis } from './TrainingBudgetAnalysis';
import { BudgetComparisonWidget } from './BudgetComparisonWidget';

interface BudgetAlert {
  id: string;
  type: 'warning' | 'error' | 'info' | 'success';
  title: string;
  message: string;
  category: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  createdAt: Date;
  isRead: boolean;
  actionRequired: boolean;
}

interface BudgetGoal {
  id: string;
  name: string;
  target: number;
  current: number;
  deadline: Date;
  category: string;
  priority: 'high' | 'medium' | 'low';
  status: 'on-track' | 'at-risk' | 'behind' | 'completed';
}

interface DashboardFilters {
  period: string;
  category: string;
  priority: string;
  status: string;
}

const COLORS = {
  primary: '#228be6',
  success: '#40c057',
  warning: '#fd7e14',
  danger: '#fa5252',
  info: '#339af0'
};

const CHART_COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7c7c', '#8dd1e1', '#d084d0'];

const mockAlerts: BudgetAlert[] = [
  {
    id: '1',
    type: 'warning',
    title: 'Budget Variance Alert',
    message: 'Technical Training budget is 15% over planned allocation',
    category: 'Technical Training',
    severity: 'medium',
    createdAt: new Date(),
    isRead: false,
    actionRequired: true
  },
  {
    id: '2',
    type: 'info',
    title: 'Quarterly Review Due',
    message: 'Q3 budget review is scheduled for next week',
    category: 'General',
    severity: 'low',
    createdAt: new Date(),
    isRead: false,
    actionRequired: false
  },
  {
    id: '3',
    type: 'success',
    title: 'Goal Achieved',
    message: 'Compliance Training completed under budget',
    category: 'Compliance Training',
    severity: 'low',
    createdAt: new Date(),
    isRead: true,
    actionRequired: false
  }
];

const mockGoals: BudgetGoal[] = [
  {
    id: '1',
    name: 'Reduce Training Costs by 10%',
    target: 100000,
    current: 92000,
    deadline: new Date('2024-12-31'),
    category: 'Cost Optimization',
    priority: 'high',
    status: 'on-track'
  },
  {
    id: '2',
    name: 'Increase ROI to 150%',
    target: 150,
    current: 135,
    deadline: new Date('2024-11-30'),
    category: 'Performance',
    priority: 'high',
    status: 'at-risk'
  },
  {
    id: '3',
    name: 'Complete Digital Transformation Training',
    target: 50000,
    current: 45000,
    deadline: new Date('2024-10-15'),
    category: 'Digital Skills',
    priority: 'medium',
    status: 'on-track'
  }
];

const mockPerformanceData = [
  { month: 'Jan', efficiency: 85, roi: 120, satisfaction: 90, utilization: 78 },
  { month: 'Feb', efficiency: 88, roi: 125, satisfaction: 92, utilization: 82 },
  { month: 'Mar', efficiency: 92, roi: 135, satisfaction: 88, utilization: 85 },
  { month: 'Apr', efficiency: 90, roi: 140, satisfaction: 94, utilization: 88 },
  { month: 'May', efficiency: 94, roi: 145, satisfaction: 96, utilization: 90 },
  { month: 'Jun', efficiency: 96, roi: 150, satisfaction: 95, utilization: 92 }
];

interface BudgetDashboardProps {
  onExport?: () => void;
  onSettings?: () => void;
}

export const BudgetDashboard: React.FC<BudgetDashboardProps> = ({
  onExport,
  onSettings
}) => {
  const [activeTab, setActiveTab] = useState<string>('overview');
  const [filters, setFilters] = useState<DashboardFilters>({
    period: 'current',
    category: 'all',
    priority: 'all',
    status: 'all'
  });
  const [alerts, setAlerts] = useState<BudgetAlert[]>(mockAlerts);
  const [goals, setGoals] = useState<BudgetGoal[]>(mockGoals);
  const [addGoalOpened, { open: openAddGoal, close: closeAddGoal }] = useDisclosure(false);
  const [selectedAlert, setSelectedAlert] = useState<BudgetAlert | null>(null);

  const unreadAlerts = alerts.filter(alert => !alert.isRead);
  const criticalAlerts = alerts.filter(alert => alert.severity === 'critical' || alert.severity === 'high');

  const handleMarkAsRead = (alertId: string) => {
    setAlerts(prev => prev.map(alert => 
      alert.id === alertId ? { ...alert, isRead: true } : alert
    ));
  };

  const handleDismissAlert = (alertId: string) => {
    setAlerts(prev => prev.filter(alert => alert.id !== alertId));
    notifications.show({
      title: 'Alert Dismissed',
      message: 'Alert has been removed from your dashboard',
      color: 'blue'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'on-track': return 'green';
      case 'at-risk': return 'orange';
      case 'behind': return 'red';
      case 'completed': return 'blue';
      default: return 'gray';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'red';
      case 'high': return 'orange';
      case 'medium': return 'yellow';
      case 'low': return 'blue';
      default: return 'gray';
    }
  };

  return (
    <Container size="xl" p="md">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {/* Header */}
        <Group justify="space-between" mb="xl">
          <div>
            <Group gap="sm">
              <ThemeIcon size={40} variant="light" color="blue">
                <IconDashboard size={24} />
              </ThemeIcon>
              <div>
                <Text size="xl" fw={700}>Training Budget Dashboard</Text>
                <Text size="sm" c="dimmed">Comprehensive budget analysis and monitoring</Text>
              </div>
            </Group>
          </div>
          <Group>
            <Menu shadow="md" width={200}>
              <Menu.Target>
                <ActionIcon variant="light" size="lg">
                  <IconFilter size={18} />
                </ActionIcon>
              </Menu.Target>
              <Menu.Dropdown>
                <Menu.Label>Filter Options</Menu.Label>
                <Menu.Item leftSection={<IconCalendar size={14} />}>Period</Menu.Item>
                <Menu.Item leftSection={<IconTarget size={14} />}>Category</Menu.Item>
                <Menu.Item leftSection={<IconTrendingUp size={14} />}>Priority</Menu.Item>
              </Menu.Dropdown>
            </Menu>
            <Button leftSection={<IconDownload size={16} />} variant="light" onClick={onExport}>
              Export
            </Button>
            <ActionIcon variant="light" size="lg" onClick={onSettings}>
              <IconSettings size={18} />
            </ActionIcon>
          </Group>
        </Group>

        {/* Alerts Section */}
        {unreadAlerts.length > 0 && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.1 }}
          >
            <Alert
              icon={<IconBell size={16} />}
              title={`${unreadAlerts.length} Unread Alert${unreadAlerts.length > 1 ? 's' : ''}`}
              color="orange"
              mb="md"
              variant="light"
            >
              <Group justify="space-between">
                <Text size="sm">
                  {criticalAlerts.length > 0 && `${criticalAlerts.length} critical alerts require immediate attention`}
                </Text>
                <Button size="xs" variant="subtle" onClick={() => setActiveTab('alerts')}>
                  View All
                </Button>
              </Group>
            </Alert>
          </motion.div>
        )}

        <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'overview')}>
          <Tabs.List>
            <Tabs.Tab value="overview" leftSection={<IconDashboard size={16} />}>
              Overview
            </Tabs.Tab>
            <Tabs.Tab value="analysis" leftSection={<IconChartBar size={16} />}>
              Analysis
            </Tabs.Tab>
            <Tabs.Tab value="comparison" leftSection={<IconReportAnalytics size={16} />}>
              Comparison
            </Tabs.Tab>
            <Tabs.Tab value="goals" leftSection={<IconTarget size={16} />}>
              Goals
            </Tabs.Tab>
            <Tabs.Tab 
              value="alerts" 
              leftSection={<IconBell size={16} />}
              rightSection={
                unreadAlerts.length > 0 ? (
                  <Badge size="xs" color="red" variant="filled">
                    {unreadAlerts.length}
                  </Badge>
                ) : null
              }
            >
              Alerts
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="overview" pt="md">
            <SimpleGrid cols={4} spacing="md" mb="lg">
              <motion.div whileHover={{ scale: 1.02 }}>
                <Card withBorder p="md" h={120}>
                  <Group justify="space-between" h="100%">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">Total Budget</Text>
                      <Text size="xl" fw={700}>$485,000</Text>
                      <Text size="xs" c="green">+5.2% from last quarter</Text>
                    </div>
                    <RingProgress
                      size={60}
                      thickness={6}
                      sections={[{ value: 75, color: 'blue' }]}
                      label={
                        <Center>
                          <Text size="xs" fw={700}>75%</Text>
                        </Center>
                      }
                    />
                  </Group>
                </Card>
              </motion.div>

              <motion.div whileHover={{ scale: 1.02 }}>
                <Card withBorder p="md" h={120}>
                  <Group justify="space-between" h="100%">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">Utilization</Text>
                      <Text size="xl" fw={700}>92%</Text>
                      <Text size="xs" c="orange">Above target (85%)</Text>
                    </div>
                    <ThemeIcon size={40} variant="light" color="orange">
                      <IconTrendingUp size={20} />
                    </ThemeIcon>
                  </Group>
                </Card>
              </motion.div>

              <motion.div whileHover={{ scale: 1.02 }}>
                <Card withBorder p="md" h={120}>
                  <Group justify="space-between" h="100%">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">ROI</Text>
                      <Text size="xl" fw={700}>145%</Text>
                      <Text size="xs" c="green">Exceeding target</Text>
                    </div>
                    <ThemeIcon size={40} variant="light" color="green">
                      <IconTarget size={20} />
                    </ThemeIcon>
                  </Group>
                </Card>
              </motion.div>

              <motion.div whileHover={{ scale: 1.02 }}>
                <Card withBorder p="md" h={120}>
                  <Group justify="space-between" h="100%">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">Efficiency</Text>
                      <Text size="xl" fw={700}>96%</Text>
                      <Text size="xs" c="green">+8% improvement</Text>
                    </div>
                    <ThemeIcon size={40} variant="light" color="blue">
                      <IconChartBar size={20} />
                    </ThemeIcon>
                  </Group>
                </Card>
              </motion.div>
            </SimpleGrid>

            <Grid>
              <Grid.Col span={8}>
                <Card withBorder p="md">
                  <Text size="md" fw={600} mb="md">Performance Trends</Text>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={mockPerformanceData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <RechartsTooltip />
                      <Line type="monotone" dataKey="efficiency" stroke={COLORS.primary} strokeWidth={2} name="Efficiency %" />
                      <Line type="monotone" dataKey="roi" stroke={COLORS.success} strokeWidth={2} name="ROI %" />
                      <Line type="monotone" dataKey="satisfaction" stroke={COLORS.warning} strokeWidth={2} name="Satisfaction %" />
                      <Line type="monotone" dataKey="utilization" stroke={COLORS.danger} strokeWidth={2} name="Utilization %" />
                    </LineChart>
                  </ResponsiveContainer>
                </Card>
              </Grid.Col>
              <Grid.Col span={4}>
                <Card withBorder p="md">
                  <Text size="md" fw={600} mb="md">Performance Radar</Text>
                  <ResponsiveContainer width="100%" height={300}>
                    <RadarChart data={[mockPerformanceData[mockPerformanceData.length - 1]]}>
                      <PolarGrid />
                      <PolarAngleAxis dataKey="subject" />
                      <PolarRadiusAxis angle={90} domain={[0, 100]} />
                      <Radar
                        name="Current Performance"
                        dataKey="efficiency"
                        stroke={COLORS.primary}
                        fill={COLORS.primary}
                        fillOpacity={0.3}
                      />
                    </RadarChart>
                  </ResponsiveContainer>
                </Card>
              </Grid.Col>
            </Grid>
          </Tabs.Panel>

          <Tabs.Panel value="analysis" pt="md">
            <TrainingBudgetAnalysis />
          </Tabs.Panel>

          <Tabs.Panel value="comparison" pt="md">
            <BudgetComparisonWidget />
          </Tabs.Panel>

          <Tabs.Panel value="goals" pt="md">
            <Group justify="space-between" mb="md">
              <Text size="lg" fw={600}>Budget Goals & Targets</Text>
              <Button leftSection={<IconPlus size={16} />} onClick={openAddGoal}>
                Add Goal
              </Button>
            </Group>
            
            <SimpleGrid cols={1} spacing="md">
              {goals.map((goal, index) => (
                <motion.div
                  key={goal.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card withBorder p="md">
                    <Grid align="center">
                      <Grid.Col span={6}>
                        <Group gap="sm">
                          <div>
                            <Group gap="xs" mb="xs">
                              <Text fw={600}>{goal.name}</Text>
                              <Badge color={getStatusColor(goal.status)} size="sm">
                                {goal.status.replace('-', ' ')}
                              </Badge>
                            </Group>
                            <Text size="sm" c="dimmed">{goal.category}</Text>
                            <Text size="xs" c="dimmed">
                              Due: {goal.deadline.toLocaleDateString()}
                            </Text>
                          </div>
                        </Group>
                      </Grid.Col>
                      <Grid.Col span={3}>
                        <div>
                          <Text size="xs" c="dimmed">Progress</Text>
                          <Progress
                            value={(goal.current / goal.target) * 100}
                            color={getStatusColor(goal.status)}
                            size="lg"
                            mb="xs"
                          />
                          <Text size="sm" fw={600}>
                            {goal.current.toLocaleString()} / {goal.target.toLocaleString()}
                          </Text>
                        </div>
                      </Grid.Col>
                      <Grid.Col span={2}>
                        <Text size="xs" c="dimmed">Completion</Text>
                        <Text size="lg" fw={700}>
                          {((goal.current / goal.target) * 100).toFixed(1)}%
                        </Text>
                      </Grid.Col>
                      <Grid.Col span={1}>
                        <Group gap="xs">
                          <ActionIcon variant="subtle" size="sm">
                            <IconEye size={14} />
                          </ActionIcon>
                          <ActionIcon variant="subtle" size="sm">
                            <IconEdit size={14} />
                          </ActionIcon>
                        </Group>
                      </Grid.Col>
                    </Grid>
                  </Card>
                </motion.div>
              ))}
            </SimpleGrid>
          </Tabs.Panel>

          <Tabs.Panel value="alerts" pt="md">
            <Group justify="space-between" mb="md">
              <Text size="lg" fw={600}>Budget Alerts & Notifications</Text>
              <Group>
                <Button variant="subtle" size="sm" onClick={() => setAlerts(prev => prev.map(a => ({ ...a, isRead: true })))}>
                  Mark All as Read
                </Button>
                <ActionIcon variant="subtle">
                  <IconRefresh size={16} />
                </ActionIcon>
              </Group>
            </Group>

            <Stack gap="md">
              {alerts.map((alert, index) => (
                <motion.div
                  key={alert.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                >
                  <Alert
                    icon={
                      alert.type === 'warning' ? <IconAlertTriangle size={16} /> :
                      alert.type === 'error' ? <IconX size={16} /> :
                      alert.type === 'success' ? <IconCheck size={16} /> :
                      <IconInfoCircle size={16} />
                    }
                    title={
                      <Group justify="space-between">
                        <Group gap="xs">
                          <Text fw={600}>{alert.title}</Text>
                          {!alert.isRead && <Badge size="xs" color="blue">New</Badge>}
                          <Badge size="xs" color={getSeverityColor(alert.severity)}>
                            {alert.severity}
                          </Badge>
                        </Group>
                        <Group gap="xs">
                          {!alert.isRead && (
                            <ActionIcon 
                              size="sm" 
                              variant="subtle"
                              onClick={() => handleMarkAsRead(alert.id)}
                            >
                              <IconCheck size={12} />
                            </ActionIcon>
                          )}
                          <ActionIcon 
                            size="sm" 
                            variant="subtle" 
                            color="red"
                            onClick={() => handleDismissAlert(alert.id)}
                          >
                            <IconX size={12} />
                          </ActionIcon>
                        </Group>
                      </Group>
                    }
                    color={alert.type === 'warning' ? 'orange' : alert.type === 'error' ? 'red' : alert.type === 'success' ? 'green' : 'blue'}
                    variant="light"
                  >
                    <Text size="sm">{alert.message}</Text>
                    <Group justify="space-between" mt="xs">
                      <Text size="xs" c="dimmed">
                        {alert.category} • {alert.createdAt.toLocaleString()}
                      </Text>
                      {alert.actionRequired && (
                        <Button size="xs" variant="light">
                          Take Action
                        </Button>
                      )}
                    </Group>
                  </Alert>
                </motion.div>
              ))}
            </Stack>
          </Tabs.Panel>
        </Tabs>

        {/* Add Goal Modal */}
        <Modal opened={addGoalOpened} onClose={closeAddGoal} title="Add New Budget Goal">
          <Stack gap="md">
            <TextInput label="Goal Name" placeholder="Enter goal name" />
            <NumberInput label="Target Value" placeholder="Enter target value" />
            <Select
              label="Category"
              placeholder="Select category"
              data={[
                { value: 'cost-optimization', label: 'Cost Optimization' },
                { value: 'performance', label: 'Performance' },
                { value: 'digital-skills', label: 'Digital Skills' },
                { value: 'compliance', label: 'Compliance' }
              ]}
            />
            <Select
              label="Priority"
              placeholder="Select priority"
              data={[
                { value: 'high', label: 'High' },
                { value: 'medium', label: 'Medium' },
                { value: 'low', label: 'Low' }
              ]}
            />
            <Textarea label="Description" placeholder="Enter goal description" />
            <Group justify="flex-end">
              <Button variant="subtle" onClick={closeAddGoal}>Cancel</Button>
              <Button onClick={closeAddGoal}>Create Goal</Button>
            </Group>
          </Stack>
        </Modal>
      </motion.div>
    </Container>
  );
};

export default BudgetDashboard;