import React, { useState, useMemo } from 'react';
import {
  Card,
  Grid,
  Text,
  Stack,
  Group,
  Badge,
  Progress,
  NumberInput,
  Select,
  Button,
  ActionIcon,
  Tooltip,
  Alert,
  Tabs,
  Table,
  RingProgress,
  Center,
  Box,
  Paper,
  Divider,
  Switch,
  Modal,
  TextInput,
  Textarea
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import { motion, AnimatePresence } from 'framer-motion';
import {
  IconTrendingUp,
  IconTrendingDown,
  IconAlertTriangle,
  IconCheck,
  IconPlus,
  IconEdit,
  IconTrash,
  IconDownload,
  IconRefresh,
  IconTarget,
  IconCurrency,
  IconCalendar,
  IconChartBar,
  IconReportAnalytics
} from '@tabler/icons-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell, Area, AreaChart } from 'recharts';
import { notifications } from '@mantine/notifications';

interface BudgetItem {
  id: string;
  category: string;
  planned: number;
  actual: number;
  variance: number;
  variancePercent: number;
  status: 'on-track' | 'over-budget' | 'under-budget' | 'at-risk';
  lastUpdated: Date;
  notes?: string;
}

interface BudgetPeriod {
  id: string;
  name: string;
  startDate: Date;
  endDate: Date;
  totalPlanned: number;
  totalActual: number;
  status: 'active' | 'completed' | 'upcoming';
}

interface BudgetAnalysisProps {
  onExport?: () => void;
  onSave?: (data: any) => void;
  initialData?: any;
}

const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7c7c', '#8dd1e1', '#d084d0'];

const mockBudgetData: BudgetItem[] = [
  {
    id: '1',
    category: 'Training Materials',
    planned: 15000,
    actual: 12500,
    variance: -2500,
    variancePercent: -16.67,
    status: 'under-budget',
    lastUpdated: new Date()
  },
  {
    id: '2',
    category: 'Instructor Fees',
    planned: 25000,
    actual: 28000,
    variance: 3000,
    variancePercent: 12,
    status: 'over-budget',
    lastUpdated: new Date()
  },
  {
    id: '3',
    category: 'Technology & Tools',
    planned: 10000,
    actual: 9500,
    variance: -500,
    variancePercent: -5,
    status: 'on-track',
    lastUpdated: new Date()
  },
  {
    id: '4',
    category: 'Travel & Accommodation',
    planned: 8000,
    actual: 12000,
    variance: 4000,
    variancePercent: 50,
    status: 'over-budget',
    lastUpdated: new Date()
  },
  {
    id: '5',
    category: 'Certification Costs',
    planned: 5000,
    actual: 4200,
    variance: -800,
    variancePercent: -16,
    status: 'under-budget',
    lastUpdated: new Date()
  }
];

const mockTimelineData = [
  { month: 'Jan', planned: 10000, actual: 8500 },
  { month: 'Feb', planned: 15000, actual: 14200 },
  { month: 'Mar', planned: 20000, actual: 22000 },
  { month: 'Apr', planned: 18000, actual: 19500 },
  { month: 'May', planned: 22000, actual: 21000 },
  { month: 'Jun', planned: 25000, actual: 26200 }
];

export const TrainingBudgetAnalysis: React.FC<BudgetAnalysisProps> = ({
  onExport,
  onSave,
  initialData
}) => {
  const [budgetData, setBudgetData] = useState<BudgetItem[]>(mockBudgetData);
  const [selectedPeriod, setSelectedPeriod] = useState<string>('current');
  const [viewMode, setViewMode] = useState<'summary' | 'detailed' | 'trends'>('summary');
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<BudgetItem | null>(null);
  const [showVarianceOnly, setShowVarianceOnly] = useState(false);

  const totalPlanned = useMemo(() => 
    budgetData.reduce((sum, item) => sum + item.planned, 0), [budgetData]
  );

  const totalActual = useMemo(() => 
    budgetData.reduce((sum, item) => sum + item.actual, 0), [budgetData]
  );

  const totalVariance = totalActual - totalPlanned;
  const totalVariancePercent = (totalVariance / totalPlanned) * 100;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'on-track': return 'green';
      case 'over-budget': return 'red';
      case 'under-budget': return 'blue';
      case 'at-risk': return 'orange';
      default: return 'gray';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'on-track': return <IconCheck size={16} />;
      case 'over-budget': return <IconTrendingUp size={16} />;
      case 'under-budget': return <IconTrendingDown size={16} />;
      case 'at-risk': return <IconAlertTriangle size={16} />;
      default: return null;
    }
  };

  const handleEditItem = (item: BudgetItem) => {
    setEditingItem(item);
    setEditModalOpen(true);
  };

  const handleSaveEdit = () => {
    if (editingItem) {
      setBudgetData(prev => prev.map(item => 
        item.id === editingItem.id ? {
          ...editingItem,
          variance: editingItem.actual - editingItem.planned,
          variancePercent: ((editingItem.actual - editingItem.planned) / editingItem.planned) * 100,
          lastUpdated: new Date()
        } : item
      ));
      setEditModalOpen(false);
      setEditingItem(null);
      notifications.show({
        title: 'Budget Updated',
        message: 'Budget item has been successfully updated',
        color: 'green'
      });
    }
  };

  const pieChartData = budgetData.map(item => ({
    name: item.category,
    value: item.actual,
    planned: item.planned
  }));

  const filteredData = showVarianceOnly 
    ? budgetData.filter(item => Math.abs(item.variancePercent) > 10)
    : budgetData;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Stack gap="lg">
        {/* Header */}
        <Group justify="space-between" align="center">
          <div>
            <Text size="xl" fw={700}>Training Budget Analysis</Text>
            <Text size="sm" c="dimmed">Actual vs Planned Budget Performance</Text>
          </div>
          <Group>
            <Switch
              label="Show variance only"
              checked={showVarianceOnly}
              onChange={(event) => setShowVarianceOnly(event.currentTarget.checked)}
            />
            <Button
              leftSection={<IconDownload size={16} />}
              variant="outline"
              onClick={onExport}
            >
              Export
            </Button>
            <Button
              leftSection={<IconRefresh size={16} />}
              onClick={() => window.location.reload()}
            >
              Refresh
            </Button>
          </Group>
        </Group>

        {/* Summary Cards */}
        <Grid>
          <Grid.Col span={3}>
            <motion.div
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <Card withBorder p="md" h={120}>
                <Group justify="space-between">
                  <div>
                    <Text size="xs" tt="uppercase" fw={700} c="dimmed">Total Planned</Text>
                    <Text size="xl" fw={700}>${totalPlanned.toLocaleString()}</Text>
                  </div>
                  <IconTarget size={32} color="#228be6" />
                </Group>
              </Card>
            </motion.div>
          </Grid.Col>
          <Grid.Col span={3}>
            <motion.div
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <Card withBorder p="md" h={120}>
                <Group justify="space-between">
                  <div>
                    <Text size="xs" tt="uppercase" fw={700} c="dimmed">Total Actual</Text>
                    <Text size="xl" fw={700}>${totalActual.toLocaleString()}</Text>
                  </div>
                  <IconCurrency size={32} color="#40c057" />
                </Group>
              </Card>
            </motion.div>
          </Grid.Col>
          <Grid.Col span={3}>
            <motion.div
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <Card withBorder p="md" h={120}>
                <Group justify="space-between">
                  <div>
                    <Text size="xs" tt="uppercase" fw={700} c="dimmed">Variance</Text>
                    <Text 
                      size="xl" 
                      fw={700}
                      c={totalVariance >= 0 ? 'red' : 'green'}
                    >
                      {totalVariance >= 0 ? '+' : ''}${totalVariance.toLocaleString()}
                    </Text>
                  </div>
                  {totalVariance >= 0 ? 
                    <IconTrendingUp size={32} color="#fa5252" /> : 
                    <IconTrendingDown size={32} color="#51cf66" />
                  }
                </Group>
              </Card>
            </motion.div>
          </Grid.Col>
          <Grid.Col span={3}>
            <motion.div
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <Card withBorder p="md" h={120}>
                <Center h="100%">
                  <RingProgress
                    size={80}
                    thickness={8}
                    sections={[
                      { 
                        value: Math.abs(totalVariancePercent), 
                        color: totalVariancePercent >= 0 ? 'red' : 'green' 
                      }
                    ]}
                    label={
                      <Text size="xs" ta="center" fw={700}>
                        {totalVariancePercent.toFixed(1)}%
                      </Text>
                    }
                  />
                </Center>
              </Card>
            </motion.div>
          </Grid.Col>
        </Grid>

        {/* Alert for significant variances */}
        <AnimatePresence>
          {Math.abs(totalVariancePercent) > 15 && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
            >
              <Alert
                icon={<IconAlertTriangle size={16} />}
                title="Budget Variance Alert"
                color={totalVariancePercent > 0 ? 'red' : 'orange'}
              >
                Your budget variance is {Math.abs(totalVariancePercent).toFixed(1)}% 
                {totalVariancePercent > 0 ? 'over' : 'under'} the planned amount. 
                Consider reviewing your budget allocation and spending patterns.
              </Alert>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Main Content Tabs */}
        <Tabs value={viewMode} onChange={(value) => setViewMode(value as any)}>
          <Tabs.List>
            <Tabs.Tab value="summary" leftSection={<IconChartBar size={16} />}>
              Summary
            </Tabs.Tab>
            <Tabs.Tab value="detailed" leftSection={<IconReportAnalytics size={16} />}>
              Detailed View
            </Tabs.Tab>
            <Tabs.Tab value="trends" leftSection={<IconTrendingUp size={16} />}>
              Trends
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="summary" pt="md">
            <Grid>
              <Grid.Col span={8}>
                <Card withBorder p="md">
                  <Text size="lg" fw={600} mb="md">Budget vs Actual Comparison</Text>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={filteredData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="category" angle={-45} textAnchor="end" height={80} />
                      <YAxis />
                      <RechartsTooltip formatter={(value) => `$${value.toLocaleString()}`} />
                      <Bar dataKey="planned" fill="#8884d8" name="Planned" />
                      <Bar dataKey="actual" fill="#82ca9d" name="Actual" />
                    </BarChart>
                  </ResponsiveContainer>
                </Card>
              </Grid.Col>
              <Grid.Col span={4}>
                <Card withBorder p="md">
                  <Text size="lg" fw={600} mb="md">Budget Distribution</Text>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={pieChartData}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {pieChartData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <RechartsTooltip formatter={(value) => `$${value.toLocaleString()}`} />
                    </PieChart>
                  </ResponsiveContainer>
                </Card>
              </Grid.Col>
            </Grid>
          </Tabs.Panel>

          <Tabs.Panel value="detailed" pt="md">
            <Card withBorder p="md">
              <Group justify="space-between" mb="md">
                <Text size="lg" fw={600}>Detailed Budget Breakdown</Text>
                <Button
                  leftSection={<IconPlus size={16} />}
                  size="sm"
                  onClick={() => {
                    setEditingItem({
                      id: Date.now().toString(),
                      category: '',
                      planned: 0,
                      actual: 0,
                      variance: 0,
                      variancePercent: 0,
                      status: 'on-track',
                      lastUpdated: new Date()
                    });
                    setEditModalOpen(true);
                  }}
                >
                  Add Item
                </Button>
              </Group>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Category</Table.Th>
                    <Table.Th>Planned</Table.Th>
                    <Table.Th>Actual</Table.Th>
                    <Table.Th>Variance</Table.Th>
                    <Table.Th>Status</Table.Th>
                    <Table.Th>Progress</Table.Th>
                    <Table.Th>Actions</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {filteredData.map((item) => (
                    <motion.tr
                      key={item.id}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Table.Td>
                        <Text fw={500}>{item.category}</Text>
                      </Table.Td>
                      <Table.Td>${item.planned.toLocaleString()}</Table.Td>
                      <Table.Td>${item.actual.toLocaleString()}</Table.Td>
                      <Table.Td>
                        <Text c={item.variance >= 0 ? 'red' : 'green'}>
                          {item.variance >= 0 ? '+' : ''}${item.variance.toLocaleString()}
                          <br />
                          <Text size="xs">({item.variancePercent.toFixed(1)}%)</Text>
                        </Text>
                      </Table.Td>
                      <Table.Td>
                        <Badge
                          color={getStatusColor(item.status)}
                          leftSection={getStatusIcon(item.status)}
                          variant="light"
                        >
                          {item.status.replace('-', ' ')}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Progress
                          value={(item.actual / item.planned) * 100}
                          color={item.actual > item.planned ? 'red' : 'green'}
                          size="sm"
                        />
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs">
                          <Tooltip label="Edit">
                            <ActionIcon
                              variant="subtle"
                              onClick={() => handleEditItem(item)}
                            >
                              <IconEdit size={16} />
                            </ActionIcon>
                          </Tooltip>
                          <Tooltip label="Delete">
                            <ActionIcon
                              variant="subtle"
                              color="red"
                              onClick={() => {
                                setBudgetData(prev => prev.filter(i => i.id !== item.id));
                                notifications.show({
                                  title: 'Item Deleted',
                                  message: 'Budget item has been removed',
                                  color: 'red'
                                });
                              }}
                            >
                              <IconTrash size={16} />
                            </ActionIcon>
                          </Tooltip>
                        </Group>
                      </Table.Td>
                    </motion.tr>
                  ))}
                </Table.Tbody>
              </Table>
            </Card>
          </Tabs.Panel>

          <Tabs.Panel value="trends" pt="md">
            <Card withBorder p="md">
              <Text size="lg" fw={600} mb="md">Budget Trends Over Time</Text>
              <ResponsiveContainer width="100%" height={400}>
                <AreaChart data={mockTimelineData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <RechartsTooltip formatter={(value) => `$${value.toLocaleString()}`} />
                  <Area
                    type="monotone"
                    dataKey="planned"
                    stackId="1"
                    stroke="#8884d8"
                    fill="#8884d8"
                    fillOpacity={0.6}
                    name="Planned"
                  />
                  <Area
                    type="monotone"
                    dataKey="actual"
                    stackId="2"
                    stroke="#82ca9d"
                    fill="#82ca9d"
                    fillOpacity={0.6}
                    name="Actual"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </Card>
          </Tabs.Panel>
        </Tabs>

        {/* Edit Modal */}
        <Modal
          opened={editModalOpen}
          onClose={() => setEditModalOpen(false)}
          title="Edit Budget Item"
          size="md"
        >
          {editingItem && (
            <Stack gap="md">
              <TextInput
                label="Category"
                value={editingItem.category}
                onChange={(e) => setEditingItem({
                  ...editingItem,
                  category: e.target.value
                })}
              />
              <Grid>
                <Grid.Col span={6}>
                  <NumberInput
                    label="Planned Amount"
                    prefix="$"
                    thousandSeparator=","
                    value={editingItem.planned}
                    onChange={(value) => setEditingItem({
                      ...editingItem,
                      planned: value as number
                    })}
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <NumberInput
                    label="Actual Amount"
                    prefix="$"
                    thousandSeparator=","
                    value={editingItem.actual}
                    onChange={(value) => setEditingItem({
                      ...editingItem,
                      actual: value as number
                    })}
                  />
                </Grid.Col>
              </Grid>
              <Select
                label="Status"
                data={[
                  { value: 'on-track', label: 'On Track' },
                  { value: 'over-budget', label: 'Over Budget' },
                  { value: 'under-budget', label: 'Under Budget' },
                  { value: 'at-risk', label: 'At Risk' }
                ]}
                value={editingItem.status}
                onChange={(value) => setEditingItem({
                  ...editingItem,
                  status: value as any
                })}
              />
              <Textarea
                label="Notes"
                placeholder="Add any notes or comments..."
                value={editingItem.notes || ''}
                onChange={(e) => setEditingItem({
                  ...editingItem,
                  notes: e.target.value
                })}
              />
              <Group justify="flex-end">
                <Button variant="outline" onClick={() => setEditModalOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleSaveEdit}>
                  Save Changes
                </Button>
              </Group>
            </Stack>
          )}
        </Modal>
      </Stack>
    </motion.div>
  );
};

export default TrainingBudgetAnalysis;