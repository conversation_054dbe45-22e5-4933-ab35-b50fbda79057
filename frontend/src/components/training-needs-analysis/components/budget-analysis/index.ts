export { TrainingBudgetAnalysis } from './TrainingBudgetAnalysis';
export { BudgetComparisonWidget } from './BudgetComparisonWidget';
export { BudgetDashboard } from './BudgetDashboard';
export { default } from './BudgetDashboard';

// Types for budget analysis
export interface BudgetItem {
  id: string;
  category: string;
  planned: number;
  actual: number;
  variance: number;
  variancePercent: number;
  status: 'on-track' | 'over-budget' | 'under-budget';
  lastUpdated: Date;
}

export interface BudgetPeriod {
  id: string;
  name: string;
  startDate: Date;
  endDate: Date;
  totalPlanned: number;
  totalActual: number;
  items: BudgetItem[];
}

export interface BudgetAnalysisProps {
  periods?: BudgetPeriod[];
  selectedPeriod?: string;
  onPeriodChange?: (periodId: string) => void;
  showComparison?: boolean;
  showTrends?: boolean;
}

export interface BudgetMetric {
  id: string;
  name: string;
  planned: number;
  actual: number;
  forecast: number;
  category: string;
  priority: 'high' | 'medium' | 'low';
  trend: 'up' | 'down' | 'stable';
  lastUpdated: Date;
}