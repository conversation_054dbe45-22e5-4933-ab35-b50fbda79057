# Training Budget Analysis Components

A comprehensive suite of React components for training budget analysis, comparison, and monitoring. Built with Mantine UI, Recharts, and Framer Motion for stunning visualizations and smooth animations.

## Components Overview

### 🎯 BudgetDashboard (Main Component)
The primary dashboard component that provides a comprehensive overview of training budget performance.

**Features:**
- Multi-tab interface (Overview, Analysis, Comparison, Goals, Alerts)
- Real-time performance metrics
- Interactive charts and visualizations
- Budget alerts and notifications
- Goal tracking and management
- Export and settings functionality

**Usage:**
```tsx
import { BudgetDashboard } from './components/budget-analysis';

<BudgetDashboard 
  onExport={() => console.log('Export triggered')}
  onSettings={() => console.log('Settings opened')}
/>
```

### 📊 TrainingBudgetAnalysis
Detailed budget analysis component with advanced filtering and visualization options.

**Features:**
- Budget vs actual comparison
- Category-wise breakdown
- Trend analysis
- ROI calculations
- Interactive filters
- Drill-down capabilities

**Usage:**
```tsx
import { TrainingBudgetAnalysis } from './components/budget-analysis';

<TrainingBudgetAnalysis 
  periods={budgetPeriods}
  selectedPeriod="current"
  onPeriodChange={(period) => setPeriod(period)}
  showComparison={true}
  showTrends={true}
/>
```

### 🔄 BudgetComparisonWidget
Specialized widget for comparing planned vs actual budget performance.

**Features:**
- Compact and detailed view modes
- Real-time variance calculations
- Category filtering
- Multiple chart types (Bar, Radial, Area)
- Performance indicators

**Usage:**
```tsx
import { BudgetComparisonWidget } from './components/budget-analysis';

// Compact mode
<BudgetComparisonWidget 
  compact={true}
  data={budgetMetrics}
/>

// Full mode
<BudgetComparisonWidget 
  data={budgetMetrics}
  period="current"
  onPeriodChange={(period) => handlePeriodChange(period)}
/>
```

## Key Features

### 📈 Visualizations
- **Line Charts**: Trend analysis over time
- **Bar Charts**: Category comparisons
- **Area Charts**: Cumulative budget flow
- **Radial Charts**: Budget utilization
- **Pie Charts**: Distribution analysis
- **Radar Charts**: Performance metrics

### 🎨 Animations
- Smooth page transitions with Framer Motion
- Hover effects on interactive elements
- Staggered animations for list items
- Loading states and micro-interactions

### 🔔 Alert System
- Real-time budget variance alerts
- Severity-based color coding
- Action-required notifications
- Mark as read/dismiss functionality

### 🎯 Goal Tracking
- Custom budget goals and targets
- Progress visualization
- Status indicators (on-track, at-risk, behind)
- Deadline monitoring

### 📊 Data Export
- PDF reports with charts
- Excel spreadsheets
- CSV data exports
- Customizable date ranges

## Data Structures

### BudgetItem
```typescript
interface BudgetItem {
  id: string;
  category: string;
  planned: number;
  actual: number;
  variance: number;
  variancePercent: number;
  status: 'on-track' | 'over-budget' | 'under-budget';
  lastUpdated: Date;
}
```

### BudgetMetric
```typescript
interface BudgetMetric {
  id: string;
  name: string;
  planned: number;
  actual: number;
  forecast: number;
  category: string;
  priority: 'high' | 'medium' | 'low';
  trend: 'up' | 'down' | 'stable';
  lastUpdated: Date;
}
```

### BudgetPeriod
```typescript
interface BudgetPeriod {
  id: string;
  name: string;
  startDate: Date;
  endDate: Date;
  totalPlanned: number;
  totalActual: number;
  items: BudgetItem[];
}
```

## Styling & Theming

The components use Mantine's theming system and include:
- Consistent color palette
- Responsive design
- Dark/light mode support
- Custom CSS variables for easy customization

### Color Scheme
```typescript
const COLORS = {
  primary: '#228be6',    // Blue
  success: '#40c057',    // Green
  warning: '#fd7e14',    // Orange
  danger: '#fa5252',     // Red
  info: '#339af0'        // Light Blue
};
```

## Performance Optimizations

- **Memoization**: useMemo for expensive calculations
- **Lazy Loading**: Components load on demand
- **Virtual Scrolling**: For large data sets
- **Debounced Filters**: Smooth filtering experience
- **Chart Optimization**: Efficient rendering with Recharts

## Accessibility

- ARIA labels for screen readers
- Keyboard navigation support
- High contrast mode compatibility
- Focus management
- Semantic HTML structure

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Dependencies

```json
{
  "@mantine/core": "^7.x",
  "@mantine/hooks": "^7.x",
  "@mantine/notifications": "^7.x",
  "@tabler/icons-react": "^2.x",
  "framer-motion": "^10.x",
  "recharts": "^2.x",
  "zustand": "^4.x"
}
```

## File Structure

```
budget-analysis/
├── BudgetDashboard.tsx          # Main dashboard component
├── TrainingBudgetAnalysis.tsx   # Detailed analysis component
├── BudgetComparisonWidget.tsx   # Comparison widget
├── index.ts                     # Exports and types
└── README.md                    # This documentation
```

## Usage Examples

### Basic Implementation
```tsx
import React from 'react';
import { BudgetDashboard } from './components/budget-analysis';

const MyBudgetPage = () => {
  const handleExport = () => {
    // Export logic
  };

  const handleSettings = () => {
    // Settings logic
  };

  return (
    <BudgetDashboard 
      onExport={handleExport}
      onSettings={handleSettings}
    />
  );
};
```

### Advanced Configuration
```tsx
import React, { useState } from 'react';
import { 
  BudgetDashboard, 
  BudgetComparisonWidget,
  TrainingBudgetAnalysis 
} from './components/budget-analysis';

const AdvancedBudgetPage = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('current');
  const [budgetData, setBudgetData] = useState([]);

  return (
    <div>
      <BudgetComparisonWidget 
        data={budgetData}
        period={selectedPeriod}
        onPeriodChange={setSelectedPeriod}
      />
      
      <TrainingBudgetAnalysis 
        selectedPeriod={selectedPeriod}
        showComparison={true}
        showTrends={true}
      />
    </div>
  );
};
```

## Contributing

When contributing to these components:

1. Follow the existing code style
2. Add proper TypeScript types
3. Include accessibility features
4. Test on multiple screen sizes
5. Update documentation

## Future Enhancements

- [ ] Real-time data integration
- [ ] Advanced filtering options
- [ ] Custom chart configurations
- [ ] Mobile app support
- [ ] AI-powered insights
- [ ] Automated reporting
- [ ] Integration with external systems

---

*Built with ❤️ using React, TypeScript, Mantine UI, and modern web technologies.*