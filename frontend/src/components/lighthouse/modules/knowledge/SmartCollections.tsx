import React, { useState } from 'react';
import { <PERSON>rid, Stack, Group, Card as MantineCard, Text, Badge as MantineBadge, <PERSON><PERSON> as MantineButton } from '@mantine/core';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { <PERSON><PERSON> } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Progress } from '~/components/ui/progress';
import {
  Plus,
  FolderOpen,
  Sparkles,
  Users,
  Settings,
  Eye,
  Star,
  Clock,
  TrendingUp,
  ArrowRight,
  Filter,
} from 'lucide-react';
import { cn } from '~/lib/utils';
import { useLighthouseStore } from '../../shared/store/lighthouse-store';
import { CollectionType } from '../../types/knowledge.types';

interface SmartCollectionsProps {
  searchQuery: string;
}

export function SmartCollections({ searchQuery }: SmartCollectionsProps) {
  const [selectedCollection, setSelectedCollection] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const {
    knowledgeCollections,
    currentProject,
    createCollection,
    navigateToModule,
  } = useLighthouseStore();

  // Filter collections based on search
  const filteredCollections = knowledgeCollections.filter(collection =>
    collection.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    collection.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getCollectionIcon = (type: CollectionType) => {
    switch (type) {
      case 'ai':
        return <Sparkles className="h-4 w-4 text-yellow-500" />;
      case 'collaborative':
        return <Users className="h-4 w-4 text-blue-500" />;
      case 'smart':
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      default:
        return <FolderOpen className="h-4 w-4 text-gray-500" />;
    }
  };

  const getQualityColor = (quality: number) => {
    if (quality >= 0.8) return 'text-green-600';
    if (quality >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const handleCreateCollection = async (type: CollectionType) => {
    if (!currentProject) return;

    const newCollection = await createCollection({
      projectId: currentProject.id,
      name: `New ${type} Collection`,
      description: `A ${type} collection for organizing knowledge`,
      type,
      items: [],
      rules: [],
      metadata: {
        created: new Date(),
        updated: new Date(),
        owner: 'current-user',
        collaborators: [],
        visibility: 'private',
        autoUpdate: type === 'smart' || type === 'ai',
        quality: {
          completeness: 0,
          relevance: 0,
          diversity: 0,
          recency: 0,
          overall: 0,
        },
      },
    });

    setSelectedCollection(newCollection.id);
  };

  return (
    <div className="space-y-6">
      {/* Collection Creation */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <Plus className="h-5 w-5" />
              Create New Collection
            </span>
            <Button
              size="sm"
              variant="outline"
              onClick={() => navigateToModule('sources')}
              className="text-pink-500 border-pink-200 hover:bg-pink-50"
            >
              Add Sources First
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Grid gutter="sm">
            <Grid.Col span={{ base: 6, lg: 3 }}>
              <Button
                variant="outline"
                className="h-auto p-4 flex flex-col items-center gap-2 w-full"
                onClick={() => handleCreateCollection('manual')}
              >
                <FolderOpen className="h-6 w-6 text-gray-500" />
                <div className="text-center">
                  <div className="font-medium">Manual</div>
                  <div className="text-xs text-muted-foreground">
                    Curate by hand
                  </div>
                </div>
              </Button>
            </Grid.Col>

            <Grid.Col span={{ base: 6, lg: 3 }}>
              <Button
                variant="outline"
                className="h-auto p-4 flex flex-col items-center gap-2 w-full"
                onClick={() => handleCreateCollection('smart')}
              >
                <TrendingUp className="h-6 w-6 text-green-500" />
                <div className="text-center">
                  <div className="font-medium">Smart</div>
                  <div className="text-xs text-muted-foreground">
                    Rule-based auto
                  </div>
                </div>
              </Button>
            </Grid.Col>

            <Grid.Col span={{ base: 6, lg: 3 }}>
              <Button
                variant="outline"
                className="h-auto p-4 flex flex-col items-center gap-2 w-full"
                onClick={() => handleCreateCollection('ai')}
              >
                <Sparkles className="h-6 w-6 text-yellow-500" />
                <div className="text-center">
                  <div className="font-medium">AI</div>
                  <div className="text-xs text-muted-foreground">
                    AI-suggested
                  </div>
                </div>
              </Button>
            </Grid.Col>

            <Grid.Col span={{ base: 6, lg: 3 }}>
              <Button
                variant="outline"
                className="h-auto p-4 flex flex-col items-center gap-2 w-full"
                onClick={() => handleCreateCollection('collaborative')}
              >
                <Users className="h-6 w-6 text-blue-500" />
                <div className="text-center">
                  <div className="font-medium">Team</div>
                  <div className="text-xs text-muted-foreground">
                    Collaborate
                  </div>
                </div>
              </Button>
            </Grid.Col>
          </Grid>
        </CardContent>
      </Card>

      {/* Collections Grid */}
      <Grid gutter="lg">
        {filteredCollections.map((collection) => (
          <Grid.Col key={collection.id} span={{ base: 12, md: 6 }}>
            <Card
              className={cn(
                "cursor-pointer transition-all hover:shadow-md",
                selectedCollection === collection.id && "ring-2 ring-primary"
              )}
              onClick={() => setSelectedCollection(collection.id)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {getCollectionIcon(collection.type)}
                    <span className="font-medium">{collection.name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge
                      variant={collection.metadata?.visibility === 'public' ? 'default' : 'secondary'}
                      className="text-xs"
                    >
                      {collection.metadata?.visibility || 'Private'}
                    </Badge>
                    <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                      <Settings className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground line-clamp-2">
                  {collection.description}
                </p>
              </CardHeader>

              <CardContent>
                <div className="space-y-3">
                  {/* Collection Stats */}
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Items</span>
                    <span className="font-bold">{collection.items?.length || 0}</span>
                  </div>

                  {/* Quality Score */}
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Quality Score</span>
                    <span
                      className={cn(
                        "font-bold",
                        getQualityColor(collection.metadata?.quality?.overall || 0)
                      )}
                    >
                      {Math.round((collection.metadata?.quality?.overall || 0) * 100)}%
                    </span>
                  </div>

                  {/* Quality Breakdown - Compact */}
                  <div className="grid grid-cols-4 gap-1 text-xs">
                    <div className="text-center">
                      <div className="text-muted-foreground">Completeness</div>
                      <div className="font-bold text-red-500">{Math.round((collection.metadata?.quality?.completeness || 0) * 100)}%</div>
                    </div>
                    <div className="text-center">
                      <div className="text-muted-foreground">Relevance</div>
                      <div className="font-bold text-red-500">{Math.round((collection.metadata?.quality?.relevance || 0) * 100)}%</div>
                    </div>
                    <div className="text-center">
                      <div className="text-muted-foreground">Diversity</div>
                      <div className="font-bold text-red-500">{Math.round((collection.metadata?.quality?.diversity || 0) * 100)}%</div>
                    </div>
                    <div className="text-center">
                      <div className="text-muted-foreground">Recency</div>
                      <div className="font-bold text-red-500">{Math.round((collection.metadata?.quality?.recency || 0) * 100)}%</div>
                    </div>
                  </div>

                  {/* Last updated */}
                  <div className="text-xs text-muted-foreground">
                    Updated: {collection.metadata?.updated ? new Date(collection.metadata.updated).toLocaleDateString() : 'Never'}
                  </div>

                  {/* Actions */}
                  <div className="flex items-center gap-2 pt-2">
                    <Button size="sm" variant="ghost" className="flex-1">
                      <Eye className="h-3 w-3 mr-1" />
                      View
                    </Button>
                    <Button size="sm" variant="ghost">
                      <Settings className="h-3 w-3" />
                    </Button>
                    <Button size="sm" variant="ghost">
                      <Star className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </Grid.Col>
        ))}

        {/* Empty State */}
        {filteredCollections.length === 0 && (
          <Grid.Col span={12}>
            <Card>
              <CardContent className="text-center py-12">
                <FolderOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">
                  {searchQuery ? 'No collections found' : 'No collections yet'}
                </h3>
                <p className="text-muted-foreground mb-4 max-w-md mx-auto">
                  {searchQuery
                    ? `No collections match "${searchQuery}". Try adjusting your search.`
                    : 'Create your first collection to start organizing knowledge by topic or theme.'
                  }
                </p>
                {!searchQuery && (
                  <Button onClick={() => handleCreateCollection('manual')}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create First Collection
                  </Button>
                )}
              </CardContent>
            </Card>
          </Grid.Col>
        )}
      </Grid>

      {/* Collection Rules Suggestions */}
      {knowledgeCollections.some(c => c.type === 'smart') && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Smart Collection Rules
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <p className="text-sm text-muted-foreground mb-4">
                AI-suggested rules for automatic collection organization:
              </p>
              
              <div className="grid gap-3">
                <Card className="p-3 border-dashed">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-sm">
                        Group by domain expertise
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Automatically categorize by {currentProject?.domain} topics
                      </p>
                    </div>
                    <Button size="sm" variant="outline">
                      Apply Rule
                    </Button>
                  </div>
                </Card>

                <Card className="p-3 border-dashed">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-sm">
                        Recent high-quality sources
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Include sources added in the last 30 days with confidence &gt; 80%
                      </p>
                    </div>
                    <Button size="sm" variant="outline">
                      Apply Rule
                    </Button>
                  </div>
                </Card>

                <Card className="p-3 border-dashed">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-sm">
                        Connected insights
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Group insights with 3+ connections to other concepts
                      </p>
                    </div>
                    <Button size="sm" variant="outline">
                      Apply Rule
                    </Button>
                  </div>
                </Card>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}