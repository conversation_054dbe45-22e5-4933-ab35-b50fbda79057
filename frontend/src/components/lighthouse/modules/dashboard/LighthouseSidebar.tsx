import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '~/lib/utils';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { AccessibilityUtils } from '@/utils/accessibilityUtils';
import { useLighthouseStore } from '../../shared/store/lighthouse-store';
import { ModuleName, MODULE_ROUTES } from '../../types/navigation.types';

// Custom icons
import { HomeIcon } from '~/components/ui/icons/home';
import { BookTextIcon } from '~/components/ui/icons/book-text';
import { TelescopeIcon } from '~/components/ui/icons/telescope';
import { CpuIcon } from '~/components/ui/icons/cpu';
import { MessageCircleIcon } from '~/components/ui/icons/message-circle';
import { FileTextIcon } from '~/components/ui/icons/file-text';
import { ChartLineIcon } from '~/components/ui/icons/chart-line';
import { SparklesIcon } from '~/components/ui/icons/sparkles';
import { PlusIcon } from '~/components/ui/icons/plus';
import { ChevronDownIcon } from '~/components/ui/icons/chevron-down';

interface LighthouseSidebarProps {
  className?: string;
}

// Module icon mapping
const moduleIcons: Record<ModuleName, React.ComponentType<any>> = {
  dashboard: HomeIcon,
  knowledge: BookTextIcon,
  research: TelescopeIcon,
  agents: CpuIcon,
  chat: MessageCircleIcon,
  sources: FileTextIcon,
  analytics: ChartLineIcon,
  insights: SparklesIcon,
};

export function LighthouseSidebar({ className }: LighthouseSidebarProps) {
  const {
    navigation,
    currentProject,
    setCurrentModule,
    suggestions,
    activeAgents,
    knowledgeSources,
  } = useLighthouseStore();

  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  const handleModuleClick = (module: ModuleName) => {
    setCurrentModule(module);
  };

  // Get notification counts for modules
  const getModuleNotifications = (module: ModuleName): number => {
    switch (module) {
      case 'insights':
        return suggestions.filter(s => s.priority === 'high').length;
      case 'agents':
        return activeAgents.length;
      case 'sources':
        return knowledgeSources.filter(s => s.metadata.credibility < 0.5).length;
      default:
        return 0;
    }
  };

  return (
    <motion.aside
      className={cn(
        'w-64 bg-background border-r border-border/50',
        'flex flex-col h-full',
        'backdrop-blur-sm bg-background/95',
        className
      )}
      initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, x: -20 }}
      animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, x: 0 }}
      transition={{ duration: prefersReducedMotion ? 0.1 : 0.3 }}
    >
      {/* Project Selector */}
      <motion.div
        className="p-4 border-b border-border/50"
        initial={prefersReducedMotion ? {} : { opacity: 0, y: -10 }}
        animate={prefersReducedMotion ? {} : { opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        <Button
          variant="ghost"
          className="w-full justify-between p-3 h-auto"
        >
          <div className="text-left">
            <div className="font-semibold text-foreground">New Project</div>
            <div className="text-xs text-muted-foreground">
              Planning • General
            </div>
          </div>
          <ChevronDownIcon size={16} className="text-muted-foreground" />
        </Button>
      </motion.div>

      {/* Navigation Menu */}
      <nav className="flex-1 p-4 space-y-2">
        <motion.div
          initial={prefersReducedMotion ? {} : { opacity: 0 }}
          animate={prefersReducedMotion ? {} : { opacity: 1 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          {Object.entries(MODULE_ROUTES).map(([key, route], index) => {
            const module = key as ModuleName;
            const IconComponent = moduleIcons[module];
            const isActive = navigation.currentModule === module;
            const notifications = getModuleNotifications(module);

            return (
              <motion.div
                key={module}
                initial={prefersReducedMotion ? {} : { opacity: 0, x: -10 }}
                animate={prefersReducedMotion ? {} : { opacity: 1, x: 0 }}
                transition={{ duration: 0.2, delay: 0.3 + index * 0.05 }}
              >
                <Button
                  variant={isActive ? 'secondary' : 'ghost'}
                  className={cn(
                    'w-full justify-start gap-3 p-3 h-auto',
                    'transition-all duration-200',
                    isActive && [
                      'bg-purple-50 text-purple-700 border border-purple-200',
                      'dark:bg-purple-950 dark:text-purple-300 dark:border-purple-800'
                    ],
                    !isActive && 'hover:bg-accent/50'
                  )}
                  onClick={() => handleModuleClick(module)}
                >
                  <div className={cn(
                    'p-1.5 rounded-md',
                    isActive ? 'bg-purple-100 text-purple-600 dark:bg-purple-900 dark:text-purple-400' : 'bg-muted text-muted-foreground'
                  )}>
                    <IconComponent size={16} />
                  </div>
                  <span className="flex-1 text-left font-medium">
                    {route.label}
                  </span>
                  {notifications > 0 && (
                    <Badge
                      variant="secondary"
                      className={cn(
                        'h-5 px-1.5 min-w-[20px] text-xs',
                        module === 'agents' && 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300',
                        module === 'insights' && 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-300',
                        module === 'sources' && 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300'
                      )}
                    >
                      {notifications}
                    </Badge>
                  )}
                </Button>
              </motion.div>
            );
          })}
        </motion.div>
      </nav>

      {/* Quick Actions */}
      <motion.div
        className="p-4 border-t border-border/50 space-y-3"
        initial={prefersReducedMotion ? {} : { opacity: 0, y: 20 }}
        animate={prefersReducedMotion ? {} : { opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.5 }}
      >
        <Button
          variant="outline"
          size="sm"
          className="w-full gap-2 bg-purple-50 border-purple-200 text-purple-700 hover:bg-purple-100 dark:bg-purple-950 dark:border-purple-800 dark:text-purple-300"
        >
          <SparklesIcon size={16} />
          Quick Question
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          className="w-full gap-2"
        >
          <PlusIcon size={16} />
          Add Source
        </Button>
      </motion.div>
    </motion.aside>
  );
}
