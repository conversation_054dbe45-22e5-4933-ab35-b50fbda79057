import React, { Suspense } from 'react';
import { motion } from 'framer-motion';
import { Grid, Stack } from '@mantine/core';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { StatusCard } from '~/components/ui/enhanced-card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { AnimatedBorderWrapper } from '~/components/ui/AnimatedBorderWrapper';
import { AIStatusBadge } from '../../shared/AIComponents';
import { Pressable } from '../../shared/EnhancedMicrointeractions';
import { cn } from '~/lib/utils';
import { AccessibilityUtils } from '@/utils/accessibilityUtils';

// Enhanced components
import {
  DashboardIcon,
  MetricsIcon,
  IntelligenceIcon,
  AnalyticsIcon
} from '../../shared/EnhancedIconSystem';
import {
  EnhancedTooltip
} from '../../shared/EnhancedTooltipAndProgress';
import {
  Container,
  FlexLayout
} from '../../shared/EnhancedGridLayouts';

// Custom icons
import { TrendingUpIcon } from '~/components/ui/icons/trending-up';
import { FileTextIcon } from '~/components/ui/icons/file-text';
import { SparklesIcon } from '~/components/ui/icons/sparkles';
import { ActivityIcon } from '~/components/ui/icons/activity';
import { MoreHorizontalIcon } from '~/components/ui/icons/more-horizontal';
import { LighthouseIcon } from '~/components/ui/icons/lighthouse';
import { RefreshCWIcon } from '~/components/ui/icons/refresh-cw';

import { useLighthouseStore } from '../../shared/store/lighthouse-store';
import { ActivityFeed } from './ActivityFeed';
import { SmartRecommendations } from './SmartRecommendations';

export function Dashboard() {
  const {
    currentProject,
    projectContext,
    insights,
    activeAgents,
    knowledgeSources,
    learningEvents,
    navigateToModule,
  } = useLighthouseStore();

  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  if (!currentProject || !projectContext) {
    return (
      <Container size="lg" padding="lg" className="h-full">
        <div className="flex flex-col items-center justify-center h-full">
          <motion.div
            initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, scale: 0.9, y: 20 }}
            animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, scale: 1, y: 0 }}
            transition={{ duration: prefersReducedMotion ? 0.1 : 0.6, ease: "easeOut" }}
          >
            <StatusCard
              status="info"
              title="Welcome to Lighthouse"
              description="Create a project to start building contextual intelligence with AI-powered insights and autonomous agents."
              icon={<DashboardIcon size="lg" variant="primary" animated={!prefersReducedMotion} />}
              action={
                <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                  <LighthouseIcon size={16} className="mr-2" />
                  Create Your First Project
                </Button>
              }
              className="max-w-lg"
            />
          </motion.div>

          <motion.div
            className="mt-8"
            initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, y: 20 }}
            animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, y: 0 }}
            transition={{ duration: prefersReducedMotion ? 0.1 : 0.5, delay: prefersReducedMotion ? 0 : 0.3 }}
          >
            <div className="flex items-center gap-4">
              <AIStatusBadge status="idle" label="Ready to assist" animated={!prefersReducedMotion} />
              <IntelligenceIcon size="md" variant="muted" animated={!prefersReducedMotion} />
            </div>
          </motion.div>
        </div>
      </Container>
    );
  }

  // Calculate metrics
  const recentInsights = insights.filter(
    i => i.timestamp > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
  ).length;
  
  const learningVelocity = learningEvents.filter(
    e => e.timestamp > new Date(Date.now() - 24 * 60 * 60 * 1000)
  ).length;

  const knowledgeDepth = currentProject.intelligence.domainExpertise.concepts.length;

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="space-y-6">
        {/* Project Overview Metrics */}
        <motion.div
          initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, y: 20 }}
          animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, y: 0 }}
          transition={{ duration: prefersReducedMotion ? 0.1 : 0.5 }}
        >
          <Grid gutter="lg">
            {/* Learning Progress Card */}
            <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
              <Card className="p-6 border-0 shadow-sm bg-white dark:bg-card">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <TrendingUpIcon size={16} className="text-pink-500" />
                    <h3 className="font-medium text-sm text-muted-foreground">Learning Progress</h3>
                  </div>
                </div>
                <div className="text-left">
                  <div className="text-3xl font-bold text-foreground">0</div>
                  <div className="text-sm text-muted-foreground">of 100</div>
                  <div className="text-xs text-muted-foreground mt-1">+0 insights today</div>
                </div>
              </Card>
            </Grid.Col>

            {/* Knowledge Depth Card */}
            <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
              <Card className="p-6 border-0 shadow-sm bg-white dark:bg-card">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <FileTextIcon size={16} className="text-green-500" />
                    <h3 className="font-medium text-sm text-muted-foreground">Knowledge Depth</h3>
                  </div>
                </div>
                <div className="text-left">
                  <div className="text-3xl font-bold text-foreground">0</div>
                  <div className="text-sm text-muted-foreground">of 50</div>
                  <div className="text-xs text-muted-foreground mt-1">0 sources</div>
                </div>
              </Card>
            </Grid.Col>

            {/* Active Intelligence Card */}
            <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
              <Card className="p-6 border-0 shadow-sm bg-white dark:bg-card">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <SparklesIcon size={16} className="text-blue-500" />
                    <h3 className="font-medium text-sm text-muted-foreground">Active Intelligence</h3>
                  </div>
                </div>
                <div className="text-left">
                  <div className="text-3xl font-bold text-foreground">0</div>
                  <div className="text-sm text-muted-foreground">of 10</div>
                  <div className="text-xs text-muted-foreground mt-1">0 in project</div>
                </div>
              </Card>
            </Grid.Col>

            {/* Recent Insights Card */}
            <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
              <Card className="p-6 border-0 shadow-sm bg-white dark:bg-card">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <ActivityIcon size={16} className="text-gray-800 dark:text-gray-400" />
                    <h3 className="font-medium text-sm text-muted-foreground">Recent Insights</h3>
                  </div>
                </div>
                <div className="text-left">
                  <div className="text-3xl font-bold text-foreground">0</div>
                  <div className="text-sm text-muted-foreground">of 20</div>
                  <div className="text-xs text-muted-foreground mt-1">1 total insights</div>
                </div>
              </Card>
            </Grid.Col>
          </Grid>
        </motion.div>

        {/* Enhanced Main Content Grid */}
        <Grid gutter="lg">
          {/* Left Column - Project Status */}
          <Grid.Col span={{ base: 12, md: 4 }}>
            <Stack gap="lg">
            {/* Project Goal Card */}
            <motion.div
              initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, x: -20 }}
              animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, x: 0 }}
              transition={{ duration: prefersReducedMotion ? 0.1 : 0.5, delay: prefersReducedMotion ? 0 : 0.2 }}
            >
              <Card className="border-0 shadow-sm h-full bg-white dark:bg-card">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-purple-50 text-purple-600 dark:bg-purple-950 dark:text-purple-400">
                      <MetricsIcon size="lg" />
                    </div>
                    <div className="flex items-center gap-2">
                      <span>Project Goal</span>
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm font-medium">John</span>
                      </div>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-foreground mb-6">Define your project goal</p>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground text-sm">Status</span>
                      <Badge
                        variant="outline"
                        className="border-blue-200 text-blue-700 bg-blue-50 capitalize dark:border-blue-800 dark:text-blue-300 dark:bg-blue-950"
                      >
                        Planning
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground text-sm">Domain</span>
                      <span className="text-sm font-medium capitalize">General</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground text-sm">Created</span>
                      <span className="text-sm font-medium">9/21/2025</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
            </Stack>
          </Grid.Col>

          {/* Center Column - Enhanced Activity Feed */}
          <Grid.Col span={{ base: 12, md: 4 }}>
            <motion.div
              initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, y: 20 }}
              animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, y: 0 }}
              transition={{ duration: prefersReducedMotion ? 0.1 : 0.5, delay: prefersReducedMotion ? 0 : 0.3 }}
            >
              <Suspense fallback={<div className="h-96 animate-pulse bg-muted rounded-lg" />}>
                <ActivityFeed />
              </Suspense>
            </motion.div>
          </Grid.Col>

          {/* Right Column - Enhanced AI Recommendations */}
          <Grid.Col span={{ base: 12, md: 4 }}>
            <Stack gap="lg">
            <motion.div
              initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, x: 20 }}
              animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, x: 0 }}
              transition={{ duration: prefersReducedMotion ? 0.1 : 0.5, delay: prefersReducedMotion ? 0 : 0.4 }}
            >
              <Suspense fallback={<div className="h-64 animate-pulse bg-muted rounded-lg" />}>
                <SmartRecommendations />
              </Suspense>
            </motion.div>

            {/* Domain Expertise */}
            <motion.div
              initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, x: 20 }}
              animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, x: 0 }}
              transition={{ duration: prefersReducedMotion ? 0.1 : 0.5, delay: prefersReducedMotion ? 0 : 0.5 }}
            >
              <Card className="border-0 shadow-sm">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-lg bg-orange-50 text-orange-600">
                        <AnalyticsIcon size="lg" />
                      </div>
                      Domain Expertise
                    </div>
                    <div className="text-gray-400">
                      <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                        <path d="M8 4a.5.5 0 01.5.5v3h3a.5.5 0 010 1h-3v3a.5.5 0 01-1 0v-3h-3a.5.5 0 010-1h3v-3A.5.5 0 018 4z"/>
                      </svg>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">general</span>
                      <Badge variant="outline" className="bg-red-50 text-red-600 border-red-200">
                        Level 0
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
            </Stack>
          </Grid.Col>
        </Grid>
      </div>
    </div>
  );
}