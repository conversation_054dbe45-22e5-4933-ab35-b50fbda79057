import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '~/lib/utils';
import { Badge } from '~/components/ui/badge';
import { AccessibilityUtils } from '~/utils/accessibilityUtils';
import { useLighthouseStore } from '../../shared/store/lighthouse-store';

// Custom icons
import { LighthouseIcon } from '~/components/ui/icons/lighthouse';
import { BellIcon } from '~/components/ui/icons/bell';
import { TrendingUpIcon } from '~/components/ui/icons/trending-up';
import { SparklesIcon } from '~/components/ui/icons/sparkles';

interface LighthouseHeaderProps {
  className?: string;
}

export function LighthouseHeader({ className }: LighthouseHeaderProps) {
  const {
    currentProject,
    insights,
    activeAgents,
    knowledgeSources,
  } = useLighthouseStore();

  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  // Calculate metrics
  const recentInsights = insights.filter(
    i => i.timestamp > new Date(Date.now() - 24 * 60 * 60 * 1000)
  ).length;

  const learningLevel = currentProject?.intelligence.learningLevel || 0;
  const expertiseLevel = currentProject?.intelligence.domainExpertise.expertiseLevel || 0;

  return (
    <motion.header
      className={cn(
        'bg-background border-b border-border/50',
        'px-6 py-4',
        'flex items-center justify-between',
        'backdrop-blur-sm bg-background/95',
        className
      )}
      initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, y: -20 }}
      animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, y: 0 }}
      transition={{ duration: prefersReducedMotion ? 0.1 : 0.3 }}
    >
      {/* Left Section - Title and Subtitle */}
      <div className="flex items-center gap-4">
        <motion.div
          className="flex items-center gap-3"
          initial={prefersReducedMotion ? {} : { scale: 0.9 }}
          animate={prefersReducedMotion ? {} : { scale: 1 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <div className="p-2 rounded-lg bg-pink-50 text-pink-600 dark:bg-pink-950 dark:text-pink-400">
            <LighthouseIcon size={20} />
          </div>
          <div>
            <h1 className="text-2xl font-semibold text-foreground">Dashboard</h1>
            <p className="text-sm text-muted-foreground">
              Project overview and intelligent insights
            </p>
          </div>
        </motion.div>
      </div>

      {/* Right Section - Status Indicators */}
      <div className="flex items-center gap-6">
        {/* Learning Progress */}
        <motion.div
          className="flex items-center gap-2"
          initial={prefersReducedMotion ? {} : { opacity: 0, x: 20 }}
          animate={prefersReducedMotion ? {} : { opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <div className="flex items-center gap-2">
            <BellIcon size={16} className="text-muted-foreground" />
            <div className="text-center">
              <div className="text-xs text-muted-foreground">Learning</div>
              <div className="text-sm font-medium text-foreground">
                {learningLevel}%
              </div>
            </div>
          </div>
        </motion.div>

        {/* Insights Count */}
        <motion.div
          className="flex items-center gap-2"
          initial={prefersReducedMotion ? {} : { opacity: 0, x: 20 }}
          animate={prefersReducedMotion ? {} : { opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <div className="flex items-center gap-2">
            <TrendingUpIcon size={16} className="text-green-500" />
            <div className="text-center">
              <div className="text-xs text-muted-foreground">Insights (24h)</div>
              <div className="text-sm font-medium text-foreground">
                {recentInsights}
              </div>
            </div>
          </div>
        </motion.div>

        {/* Expertise Level */}
        <motion.div
          className="flex items-center gap-2"
          initial={prefersReducedMotion ? {} : { opacity: 0, x: 20 }}
          animate={prefersReducedMotion ? {} : { opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
        >
          <div className="flex items-center gap-2">
            <SparklesIcon size={16} className="text-blue-500" />
            <div className="text-center">
              <div className="text-xs text-muted-foreground">Expertise</div>
              <div className="flex items-center gap-1 mt-1">
                {[...Array(5)].map((_, i) => (
                  <div
                    key={i}
                    className={cn(
                      'h-1.5 w-3 rounded-full transition-colors duration-200',
                      i < Math.floor(expertiseLevel / 20)
                        ? 'bg-blue-500'
                        : 'bg-muted'
                    )}
                  />
                ))}
              </div>
            </div>
          </div>
        </motion.div>

        {/* User Avatar */}
        <motion.div
          className="flex items-center gap-2"
          initial={prefersReducedMotion ? {} : { opacity: 0, scale: 0.8 }}
          animate={prefersReducedMotion ? {} : { opacity: 1, scale: 1 }}
          transition={{ duration: 0.3, delay: 0.5 }}
        >
          <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
            <span className="text-white text-sm font-medium">J</span>
          </div>
        </motion.div>
      </div>
    </motion.header>
  );
}
