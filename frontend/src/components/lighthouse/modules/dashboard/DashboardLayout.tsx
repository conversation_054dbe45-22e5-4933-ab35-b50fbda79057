import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { cn } from '~/lib/utils';
import { AccessibilityUtils } from '~/utils/accessibilityUtils';
import { ResponsiveSidebar, useBreakpoint, AdaptiveLayout } from '~/components/ui/responsive-layout';

// Dashboard components
import { LighthouseHeader } from './LighthouseHeader';
import { LighthouseSidebar } from './LighthouseSidebar';
import { Dashboard } from './Dashboard';

interface DashboardLayoutProps {
  className?: string;
  children?: React.ReactNode;
}

export function DashboardLayout({ className, children }: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const breakpoint = useBreakpoint();
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  return (
    <motion.div
      className={cn(
        'flex flex-col h-screen bg-background',
        'transition-all duration-200',
        className
      )}
      initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, scale: 0.98 }}
      animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, scale: 1 }}
      transition={{ duration: prefersReducedMotion ? 0.1 : 0.3 }}
    >
      {/* Header */}
      <LighthouseHeader />
      
      {/* Main Layout - Adaptive based on screen size */}
      <AdaptiveLayout
        mobile={
          // Mobile: Full-screen with overlay navigation
          <div className="flex flex-1 overflow-hidden relative">
            <ResponsiveSidebar
              open={sidebarOpen}
              onOpenChange={setSidebarOpen}
              title="Lighthouse Navigation"
              side="left"
            >
              <LighthouseSidebar />
            </ResponsiveSidebar>
            
            <main 
              className="flex-1 overflow-hidden"
              role="main"
              aria-label="Dashboard content"
            >
              {children || <Dashboard />}
            </main>
          </div>
        }
        
        tablet={
          // Tablet: Collapsible sidebar with more space
          <div className="flex flex-1 overflow-hidden">
            <ResponsiveSidebar
              open={sidebarOpen}
              onOpenChange={setSidebarOpen}
              title="Navigation"
              side="left"
              width="280px"
            >
              <LighthouseSidebar />
            </ResponsiveSidebar>
            
            <main 
              className={cn(
                'flex-1 overflow-hidden transition-all duration-200',
                sidebarOpen ? 'ml-0' : 'ml-0'
              )}
              role="main"
              aria-label="Dashboard content"
            >
              {children || <Dashboard />}
            </main>
          </div>
        }
        
        desktop={
          // Desktop: Fixed sidebar layout
          <div className="flex flex-1 overflow-hidden">
            {/* Fixed Navigation Sidebar */}
            <LighthouseSidebar />
            
            {/* Main Content */}
            <main 
              className="flex-1 overflow-hidden"
              role="main"
              aria-label="Dashboard content"
            >
              {children || <Dashboard />}
            </main>
          </div>
        }
        
        wide={
          // Wide screen: Enhanced layout with more space
          <div className="flex flex-1 overflow-hidden">
            {/* Enhanced Navigation Sidebar */}
            <LighthouseSidebar className="w-80" />
            
            {/* Main Content with max width */}
            <main 
              className="flex-1 overflow-hidden flex justify-center"
              role="main"
              aria-label="Dashboard content"
            >
              <div className="w-full max-w-7xl">
                {children || <Dashboard />}
              </div>
            </main>
          </div>
        }
      />
    </motion.div>
  );
}

// Export individual components for flexibility
export { LighthouseHeader, LighthouseSidebar, Dashboard };

// Default export for easy importing
export default DashboardLayout;
