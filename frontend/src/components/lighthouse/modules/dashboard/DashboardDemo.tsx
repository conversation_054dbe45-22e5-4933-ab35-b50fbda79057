import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '~/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { AccessibilityUtils } from '~/utils/accessibilityUtils';

// Import the new dashboard layout
import { DashboardLayout } from './DashboardLayout';
import { LighthouseIcon } from '~/components/ui/icons/lighthouse';

/**
 * Demo component showcasing the lighthouse dashboard module
 * This demonstrates the complete dashboard implementation with
 * header, sidebar, and main content following the design specifications
 */
export function DashboardDemo() {
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  return (
    <div className="h-screen w-full bg-background">
      <DashboardLayout>
        {/* Custom demo content can be placed here */}
        <motion.div
          className="p-6"
          initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, y: 20 }}
          animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, y: 0 }}
          transition={{ duration: prefersReducedMotion ? 0.1 : 0.5 }}
        >
          <Card className="mb-6 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950 dark:to-purple-950 border-0">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400">
                  <LighthouseIcon size={20} />
                </div>
                <div>
                  <h2 className="text-xl font-semibold">Lighthouse Dashboard Demo</h2>
                  <p className="text-sm text-muted-foreground mt-1">
                    Complete dashboard implementation following the design specifications
                  </p>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <h3 className="font-medium text-sm">✅ Header Component</h3>
                  <p className="text-xs text-muted-foreground">
                    Dashboard title, lighthouse icon, and status indicators
                  </p>
                </div>
                <div className="space-y-2">
                  <h3 className="font-medium text-sm">✅ Sidebar Component</h3>
                  <p className="text-xs text-muted-foreground">
                    Project selector, navigation menu, and quick actions
                  </p>
                </div>
                <div className="space-y-2">
                  <h3 className="font-medium text-sm">✅ Dashboard Layout</h3>
                  <p className="text-xs text-muted-foreground">
                    Responsive layout with metric cards and content sections
                  </p>
                </div>
              </div>
              
              <div className="mt-6 flex flex-wrap gap-2">
                <Badge variant="secondary" className="bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300">
                  Responsive Design
                </Badge>
                <Badge variant="secondary" className="bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300">
                  Accessibility Ready
                </Badge>
                <Badge variant="secondary" className="bg-purple-100 text-purple-700 dark:bg-purple-900 dark:text-purple-300">
                  Modern UI
                </Badge>
                <Badge variant="secondary" className="bg-orange-100 text-orange-700 dark:bg-orange-900 dark:text-orange-300">
                  Smooth Animations
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Feature showcase */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="bg-white dark:bg-card border-0 shadow-sm">
              <CardHeader>
                <CardTitle className="text-lg">Design Features</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm">Exact visual design matching</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm">Custom icons from ui/icons directory</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm">Consistent color scheme and typography</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm">Subtle shadows and gradients</span>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-card border-0 shadow-sm">
              <CardHeader>
                <CardTitle className="text-lg">Technical Features</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm">React component modularity</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm">TypeScript type safety</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm">Framer Motion animations</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm">Accessibility compliance</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </motion.div>
      </DashboardLayout>
    </div>
  );
}

export default DashboardDemo;
