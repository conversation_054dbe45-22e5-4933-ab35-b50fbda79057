# Lighthouse Dashboard Module

A complete dashboard implementation following the exact design specifications with modern UI patterns, accessibility features, and responsive design.

## Overview

This module provides a comprehensive lighthouse dashboard that matches the provided design image exactly, including:

- **LighthouseHeader**: Header component with dashboard title, lighthouse icon, and status indicators
- **LighthouseSidebar**: Sidebar with project selector, navigation menu, and quick actions
- **Dashboard**: Main dashboard content with metric cards and three-column layout
- **DashboardLayout**: Layout wrapper that combines all components with responsive behavior

## Components

### LighthouseHeader

The header component displays:
- Dashboard title with lighthouse icon
- "Project overview and intelligent insights" subtitle
- Right-side status indicators:
  - Learning progress percentage
  - Insights count (24h)
  - Expertise level visualization
  - User avatar

### LighthouseSidebar

The sidebar component includes:
- Project selector showing "New Project" and "Planning • General"
- Navigation menu with icons and active states:
  - Dashboard (active)
  - Knowledge
  - Research
  - Agents
  - Chat
  - Sources
  - Analytics
  - Insights
- Notification badges on relevant items
- Quick action buttons at bottom

### Dashboard

The main dashboard content features:
- Four metric cards at the top:
  - Learning Progress (0 of 100)
  - Knowledge Depth (0 of 50)
  - Active Intelligence (0 of 10)
  - Recent Insights (0 of 20)
- Three-column layout:
  - Left: Project Goal card with status information
  - Center: Activity Feed with timeline
  - Right: Smart Recommendations and Domain Expertise

### DashboardLayout

The layout wrapper provides:
- Responsive behavior across different screen sizes
- Proper integration of header, sidebar, and main content
- Accessibility features and ARIA labels
- Smooth animations with reduced motion support

## Usage

### Basic Usage

```tsx
import { DashboardLayout } from '~/components/lighthouse/modules/dashboard';

function MyDashboard() {
  return <DashboardLayout />;
}
```

### Custom Content

```tsx
import { DashboardLayout } from '~/components/lighthouse/modules/dashboard';

function CustomDashboard() {
  return (
    <DashboardLayout>
      <div className="p-6">
        {/* Your custom dashboard content */}
      </div>
    </DashboardLayout>
  );
}
```

### Individual Components

```tsx
import { 
  LighthouseHeader, 
  LighthouseSidebar, 
  Dashboard 
} from '~/components/lighthouse/modules/dashboard';

function CustomLayout() {
  return (
    <div className="flex flex-col h-screen">
      <LighthouseHeader />
      <div className="flex flex-1">
        <LighthouseSidebar />
        <main className="flex-1">
          <Dashboard />
        </main>
      </div>
    </div>
  );
}
```

## Features

### Design System Compliance
- Exact visual matching of the provided design
- Consistent color scheme and typography
- Proper spacing and component positioning
- Subtle shadows and gradients

### Accessibility
- ARIA labels and roles
- Keyboard navigation support
- Screen reader compatibility
- Reduced motion preferences

### Responsive Design
- Mobile-first approach
- Adaptive layouts for different screen sizes
- Touch-friendly interactions
- Collapsible sidebar on smaller screens

### Performance
- Optimized animations with Framer Motion
- Lazy loading support
- Efficient re-rendering
- Memory leak prevention

## Styling

The components use:
- Tailwind CSS for styling
- CSS custom properties for theming
- Dark mode support
- Consistent design tokens

## Dependencies

- React 18+
- Framer Motion (animations)
- Tailwind CSS (styling)
- Custom UI components from the design system
- Custom icons from `~/components/ui/icons/`

## File Structure

```
dashboard/
├── LighthouseHeader.tsx     # Header component
├── LighthouseSidebar.tsx    # Sidebar component
├── Dashboard.tsx            # Main dashboard content
├── DashboardLayout.tsx      # Layout wrapper
├── DashboardDemo.tsx        # Demo component
├── ActivityFeed.tsx         # Activity feed component
├── SmartRecommendations.tsx # Recommendations component
├── QuickActions.tsx         # Quick actions component
├── index.ts                 # Module exports
└── README.md               # This file
```

## Integration

The dashboard module integrates with:
- Lighthouse store for state management
- Navigation system for routing
- Theme system for styling
- Icon system for consistent iconography

## Demo

To see the dashboard in action, use the `DashboardDemo` component:

```tsx
import { DashboardDemo } from '~/components/lighthouse/modules/dashboard/DashboardDemo';

function App() {
  return <DashboardDemo />;
}
```
