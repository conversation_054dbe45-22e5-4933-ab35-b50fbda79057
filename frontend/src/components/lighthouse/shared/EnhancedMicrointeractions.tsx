import React from 'react';
import { motion, useAnimation, useMotionValue, useTransform } from 'framer-motion';
import { cn } from '~/lib/ui-utils';
import { AccessibilityUtils } from '~/utils/accessibilityUtils';

interface HoverLiftProps {
  children: React.ReactNode;
  liftHeight?: number;
  scale?: number;
  duration?: number;
  className?: string;
  disabled?: boolean;
}

interface PressableProps {
  children: React.ReactNode;
  onPress?: () => void;
  pressScale?: number;
  duration?: number;
  className?: string;
  disabled?: boolean;
}

interface MagneticProps {
  children: React.ReactNode;
  strength?: number;
  className?: string;
  disabled?: boolean;
}

interface FloatingProps {
  children: React.ReactNode;
  amplitude?: number;
  duration?: number;
  delay?: number;
  className?: string;
  disabled?: boolean;
}

// Enhanced hover lift effect with accessibility support
export function HoverLift({
  children,
  liftHeight = 8,
  scale = 1.02,
  duration = 0.2,
  className,
  disabled = false
}: HoverLiftProps) {
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  if (disabled || prefersReducedMotion) {
    return <div className={className}>{children}</div>;
  }

  return (
    <motion.div
      className={cn('cursor-pointer', className)}
      whileHover={{
        y: -liftHeight,
        scale,
        transition: { duration, ease: 'easeOut' }
      }}
      whileTap={{
        scale: scale * 0.98,
        transition: { duration: 0.1 }
      }}
    >
      {children}
    </motion.div>
  );
}

// Enhanced pressable component with tactile feedback
export function Pressable({
  children,
  onPress,
  pressScale = 0.95,
  duration = 0.1,
  className,
  disabled = false
}: PressableProps) {
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  const handlePress = () => {
    if (!disabled && onPress) {
      onPress();
    }
  };

  if (disabled) {
    return (
      <div className={cn('opacity-50 cursor-not-allowed', className)}>
        {children}
      </div>
    );
  }

  if (prefersReducedMotion) {
    return (
      <div 
        className={cn('cursor-pointer', className)}
        onClick={handlePress}
      >
        {children}
      </div>
    );
  }

  return (
    <motion.div
      className={cn('cursor-pointer', className)}
      whileTap={{
        scale: pressScale,
        transition: { duration, ease: 'easeInOut' }
      }}
      onClick={handlePress}
    >
      {children}
    </motion.div>
  );
}

// Magnetic attraction effect for interactive elements
export function Magnetic({
  children,
  strength = 0.3,
  className,
  disabled = false
}: MagneticProps) {
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();
  const x = useMotionValue(0);
  const y = useMotionValue(0);
  const ref = React.useRef<HTMLDivElement>(null);

  const handleMouseMove = (e: React.MouseEvent) => {
    if (disabled || prefersReducedMotion || !ref.current) return;

    const rect = ref.current.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    const deltaX = (e.clientX - centerX) * strength;
    const deltaY = (e.clientY - centerY) * strength;
    
    x.set(deltaX);
    y.set(deltaY);
  };

  const handleMouseLeave = () => {
    if (disabled || prefersReducedMotion) return;
    x.set(0);
    y.set(0);
  };

  if (disabled || prefersReducedMotion) {
    return <div className={className}>{children}</div>;
  }

  return (
    <motion.div
      ref={ref}
      className={className}
      style={{ x, y }}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      transition={{ type: 'spring', stiffness: 300, damping: 30 }}
    >
      {children}
    </motion.div>
  );
}

// Floating animation for ambient movement
export function Floating({
  children,
  amplitude = 10,
  duration = 3,
  delay = 0,
  className,
  disabled = false
}: FloatingProps) {
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  if (disabled || prefersReducedMotion) {
    return <div className={className}>{children}</div>;
  }

  return (
    <motion.div
      className={className}
      animate={{
        y: [-amplitude, amplitude, -amplitude],
        transition: {
          duration,
          repeat: Infinity,
          ease: 'easeInOut',
          delay
        }
      }}
    >
      {children}
    </motion.div>
  );
}

// Enhanced focus ring with animation
export function FocusRing({
  children,
  className,
  ringColor = 'rgb(59, 130, 246)',
  disabled = false
}: {
  children: React.ReactNode;
  className?: string;
  ringColor?: string;
  disabled?: boolean;
}) {
  const [isFocused, setIsFocused] = React.useState(false);
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  if (disabled) {
    return <div className={className}>{children}</div>;
  }

  return (
    <motion.div
      className={cn('relative', className)}
      onFocus={() => setIsFocused(true)}
      onBlur={() => setIsFocused(false)}
      tabIndex={0}
    >
      {isFocused && !prefersReducedMotion && (
        <motion.div
          className="absolute inset-0 rounded-lg pointer-events-none"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          style={{
            boxShadow: `0 0 0 2px ${ringColor}`,
          }}
          transition={{ duration: 0.2 }}
        />
      )}
      {children}
    </motion.div>
  );
}

// Stagger animation container for lists
export function StaggerContainer({
  children,
  staggerDelay = 0.1,
  className,
  disabled = false
}: {
  children: React.ReactNode;
  staggerDelay?: number;
  className?: string;
  disabled?: boolean;
}) {
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  if (disabled || prefersReducedMotion) {
    return <div className={className}>{children}</div>;
  }

  return (
    <motion.div
      className={className}
      initial="hidden"
      animate="visible"
      variants={{
        hidden: { opacity: 0 },
        visible: {
          opacity: 1,
          transition: {
            staggerChildren: staggerDelay
          }
        }
      }}
    >
      {children}
    </motion.div>
  );
}

// Individual stagger item
export function StaggerItem({
  children,
  className,
  disabled = false
}: {
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
}) {
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  if (disabled || prefersReducedMotion) {
    return <div className={className}>{children}</div>;
  }

  return (
    <motion.div
      className={className}
      variants={{
        hidden: { opacity: 0, y: 20 },
        visible: {
          opacity: 1,
          y: 0,
          transition: { duration: 0.4, ease: 'easeOut' }
        }
      }}
    >
      {children}
    </motion.div>
  );
}
