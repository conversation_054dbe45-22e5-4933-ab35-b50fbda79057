import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn, typography, layout, visualEffects, animations } from '~/lib/ui-utils';
import { Card, CardContent } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { CheckCircleIcon, ClockIcon, AlertTriangleIcon, SparklesIcon } from './icons';
import { AccessibilityUtils } from '~/utils/accessibilityUtils';

interface ProgressStepProps {
  title: string;
  description?: string;
  status: 'pending' | 'active' | 'completed' | 'error';
  index: number;
  isLast?: boolean;
  onClick?: () => void;
}

interface TooltipProps {
  content: string;
  children: React.ReactNode;
  position?: 'top' | 'bottom' | 'left' | 'right';
  delay?: number;
  className?: string;
}

interface ProgressIndicatorProps {
  steps: Array<{
    title: string;
    description?: string;
    status: 'pending' | 'active' | 'completed' | 'error';
  }>;
  currentStep: number;
  onStepClick?: (index: number) => void;
  className?: string;
}

interface StatusBadgeProps {
  status: 'success' | 'warning' | 'error' | 'info' | 'processing';
  label: string;
  animated?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

// Enhanced Tooltip Component
export function Tooltip({
  content,
  children,
  position = 'top',
  delay = 500,
  className
}: TooltipProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [timeoutId, setTimeoutId] = useState<NodeJS.Timeout | null>(null);
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  const showTooltip = () => {
    const id = setTimeout(() => setIsVisible(true), delay);
    setTimeoutId(id);
  };

  const hideTooltip = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      setTimeoutId(null);
    }
    setIsVisible(false);
  };

  const getPositionClasses = () => {
    switch (position) {
      case 'bottom':
        return 'top-full left-1/2 transform -translate-x-1/2 mt-2';
      case 'left':
        return 'right-full top-1/2 transform -translate-y-1/2 mr-2';
      case 'right':
        return 'left-full top-1/2 transform -translate-y-1/2 ml-2';
      default:
        return 'bottom-full left-1/2 transform -translate-x-1/2 mb-2';
    }
  };

  return (
    <div
      className={cn('relative inline-block', className)}
      onMouseEnter={showTooltip}
      onMouseLeave={hideTooltip}
      onFocus={showTooltip}
      onBlur={hideTooltip}
    >
      {children}
      <AnimatePresence>
        {isVisible && (
          <motion.div
            className={cn(
              'absolute z-50 px-3 py-2 text-sm rounded-lg pointer-events-none',
              'bg-gray-900 text-white dark:bg-gray-100 dark:text-gray-900',
              'shadow-lg border border-gray-200 dark:border-gray-700',
              'whitespace-nowrap max-w-xs',
              getPositionClasses()
            )}
            initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, scale: 0.8, y: position === 'top' ? 10 : -10 }}
            animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, scale: 1, y: 0 }}
            exit={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, scale: 0.8, y: position === 'top' ? 10 : -10 }}
            transition={{ duration: 0.2 }}
          >
            {content}
            {/* Arrow */}
            <div
              className={cn(
                'absolute w-2 h-2 bg-gray-900 dark:bg-gray-100 transform rotate-45',
                position === 'top' && 'top-full left-1/2 -translate-x-1/2 -mt-1',
                position === 'bottom' && 'bottom-full left-1/2 -translate-x-1/2 -mb-1',
                position === 'left' && 'left-full top-1/2 -translate-y-1/2 -ml-1',
                position === 'right' && 'right-full top-1/2 -translate-y-1/2 -mr-1'
              )}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

// Progress Step Component
export function ProgressStep({
  title,
  description,
  status,
  index,
  isLast = false,
  onClick
}: ProgressStepProps) {
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  const getStatusIcon = () => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon size={20} className="text-green-500" />;
      case 'error':
        return <AlertTriangleIcon size={20} className="text-red-500" />;
      case 'active':
        return <SparklesIcon size={20} className="text-primary" />;
      default:
        return <ClockIcon size={20} className="text-muted-foreground" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'completed':
        return 'border-green-500 bg-green-50 dark:bg-green-950';
      case 'error':
        return 'border-red-500 bg-red-50 dark:bg-red-950';
      case 'active':
        return 'border-primary bg-primary/10';
      default:
        return 'border-muted bg-muted/20';
    }
  };

  return (
    <div className="flex items-start gap-4">
      <div className="flex flex-col items-center">
        <motion.div
          className={cn(
            'w-10 h-10 rounded-full border-2 flex items-center justify-center',
            'transition-all duration-300',
            getStatusColor(),
            onClick && 'cursor-pointer hover:scale-110'
          )}
          onClick={onClick}
          whileHover={onClick && !prefersReducedMotion ? { scale: 1.1 } : {}}
          whileTap={onClick && !prefersReducedMotion ? { scale: 0.95 } : {}}
          animate={status === 'active' && !prefersReducedMotion ? {
            scale: [1, 1.05, 1],
            transition: { duration: 2, repeat: Infinity }
          } : {}}
        >
          {getStatusIcon()}
        </motion.div>
        {!isLast && (
          <div className={cn(
            'w-0.5 h-8 mt-2 transition-colors duration-300',
            status === 'completed' ? 'bg-green-500' : 'bg-muted'
          )} />
        )}
      </div>
      <div className="flex-1 pb-8">
        <motion.h4
          className={cn(
            typography.label,
            'transition-colors duration-300',
            status === 'active' && 'text-primary font-semibold',
            status === 'completed' && 'text-green-700 dark:text-green-300',
            status === 'error' && 'text-red-700 dark:text-red-300'
          )}
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: index * 0.1 }}
        >
          {title}
        </motion.h4>
        {description && (
          <motion.p
            className={cn(typography.bodySmall, 'text-muted-foreground mt-1')}
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 + 0.1 }}
          >
            {description}
          </motion.p>
        )}
      </div>
    </div>
  );
}

// Progress Indicator Component
export function ProgressIndicator({
  steps,
  currentStep,
  onStepClick,
  className
}: ProgressIndicatorProps) {
  return (
    <Card className={cn(visualEffects.cards.elevated, className)}>
      <CardContent className="p-6">
        <div className="space-y-0">
          {steps.map((step, index) => (
            <ProgressStep
              key={index}
              title={step.title}
              description={step.description}
              status={
                index < currentStep ? 'completed' :
                index === currentStep ? 'active' :
                'pending'
              }
              index={index}
              isLast={index === steps.length - 1}
              onClick={onStepClick ? () => onStepClick(index) : undefined}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

// Enhanced Status Badge
export function StatusBadge({
  status,
  label,
  animated = false,
  size = 'md',
  className
}: StatusBadgeProps) {
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  const getStatusConfig = () => {
    switch (status) {
      case 'success':
        return {
          bg: 'bg-green-100 dark:bg-green-900',
          text: 'text-green-800 dark:text-green-200',
          border: 'border-green-200 dark:border-green-700',
          icon: <CheckCircleIcon size={16} />
        };
      case 'warning':
        return {
          bg: 'bg-yellow-100 dark:bg-yellow-900',
          text: 'text-yellow-800 dark:text-yellow-200',
          border: 'border-yellow-200 dark:border-yellow-700',
          icon: <AlertTriangleIcon size={16} />
        };
      case 'error':
        return {
          bg: 'bg-red-100 dark:bg-red-900',
          text: 'text-red-800 dark:text-red-200',
          border: 'border-red-200 dark:border-red-700',
          icon: <AlertTriangleIcon size={16} />
        };
      case 'processing':
        return {
          bg: 'bg-blue-100 dark:bg-blue-900',
          text: 'text-blue-800 dark:text-blue-200',
          border: 'border-blue-200 dark:border-blue-700',
          icon: <SparklesIcon size={16} />
        };
      default:
        return {
          bg: 'bg-gray-100 dark:bg-gray-800',
          text: 'text-gray-800 dark:text-gray-200',
          border: 'border-gray-200 dark:border-gray-700',
          icon: <ClockIcon size={16} />
        };
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'px-2 py-1 text-xs';
      case 'lg':
        return 'px-4 py-2 text-base';
      default:
        return 'px-3 py-1.5 text-sm';
    }
  };

  const config = getStatusConfig();

  return (
    <motion.div
      className={cn(
        'inline-flex items-center gap-2 rounded-full border font-medium',
        'transition-all duration-300',
        config.bg,
        config.text,
        config.border,
        getSizeClasses(),
        className
      )}
      animate={animated && status === 'processing' && !prefersReducedMotion ? {
        scale: [1, 1.05, 1],
        transition: { duration: 2, repeat: Infinity }
      } : {}}
    >
      <motion.div
        animate={animated && status === 'processing' && !prefersReducedMotion ? {
          rotate: 360,
          transition: { duration: 2, repeat: Infinity, ease: 'linear' }
        } : {}}
      >
        {config.icon}
      </motion.div>
      {label}
    </motion.div>
  );
}
