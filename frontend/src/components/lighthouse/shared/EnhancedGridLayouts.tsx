import React from 'react';
import { motion } from 'framer-motion';
import { cn, layout, responsive, animations } from '~/lib/ui-utils';
import { AccessibilityUtils } from '~/utils/accessibilityUtils';

/**
 * @deprecated These grid components are being replaced with Mantine Grid components
 * for consistency with vendor management components. Use Mantine Grid instead:
 *
 * import { Grid } from '@mantine/core';
 *
 * Replace ResponsiveGrid with:
 * <Grid gutter="lg">
 *   <Grid.Col span={{ base: 12, sm: 6, md: 4 }}>...</Grid.Col>
 * </Grid>
 *
 * Replace MetricsGrid with:
 * <Grid gutter="lg">
 *   <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>...</Grid.Col>
 * </Grid>
 */

// Enhanced Grid Container
interface ResponsiveGridProps {
  children: React.ReactNode;
  columns?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
    wide?: number;
  };
  gap?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  animated?: boolean;
  staggerDelay?: number;
}

export function ResponsiveGrid({
  children,
  columns = {
    mobile: 1,
    tablet: 2,
    desktop: 3,
    wide: 4,
  },
  gap = 'md',
  className,
  animated = true,
  staggerDelay = 0.1,
}: ResponsiveGridProps) {
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  const gapClasses = {
    sm: 'gap-3',
    md: 'gap-6',
    lg: 'gap-8',
    xl: 'gap-12',
  };

  const gridClasses = cn(
    'grid w-full',
    `grid-cols-${columns.mobile}`,
    `md:grid-cols-${columns.tablet}`,
    `lg:grid-cols-${columns.desktop}`,
    `xl:grid-cols-${columns.wide}`,
    gapClasses[gap],
    className
  );

  if (!animated || prefersReducedMotion) {
    return <div className={gridClasses}>{children}</div>;
  }

  return (
    <motion.div
      className={gridClasses}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      {React.Children.map(children, (child, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            duration: 0.4,
            delay: index * staggerDelay,
            ease: 'easeOut',
          }}
        >
          {child}
        </motion.div>
      ))}
    </motion.div>
  );
}

// Masonry Grid Layout
interface MasonryGridProps {
  children: React.ReactNode;
  columns?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  };
  gap?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function MasonryGrid({
  children,
  columns = {
    mobile: 1,
    tablet: 2,
    desktop: 3,
  },
  gap = 'md',
  className,
}: MasonryGridProps) {
  const gapClasses = {
    sm: 'gap-3',
    md: 'gap-6',
    lg: 'gap-8',
  };

  return (
    <div
      className={cn(
        'columns-1 md:columns-2 lg:columns-3',
        gapClasses[gap],
        className
      )}
      style={{
        columnCount: `${columns.mobile}`,
        columnGap: gap === 'sm' ? '12px' : gap === 'md' ? '24px' : '32px',
      }}
    >
      {React.Children.map(children, (child, index) => (
        <div
          key={index}
          className="break-inside-avoid mb-6"
          style={{ display: 'inline-block', width: '100%' }}
        >
          {child}
        </div>
      ))}
    </div>
  );
}

// Adaptive Card Grid
interface AdaptiveCardGridProps {
  children: React.ReactNode;
  minCardWidth?: number;
  maxCardWidth?: number;
  gap?: 'sm' | 'md' | 'lg';
  className?: string;
  animated?: boolean;
}

export function AdaptiveCardGrid({
  children,
  minCardWidth = 280,
  maxCardWidth = 400,
  gap = 'md',
  className,
  animated = true,
}: AdaptiveCardGridProps) {
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  const gapClasses = {
    sm: 'gap-3',
    md: 'gap-6',
    lg: 'gap-8',
  };

  const gridStyle = {
    display: 'grid',
    gridTemplateColumns: `repeat(auto-fit, minmax(${minCardWidth}px, ${maxCardWidth}px))`,
    justifyContent: 'center',
  };

  if (!animated || prefersReducedMotion) {
    return (
      <div
        className={cn('w-full', gapClasses[gap], className)}
        style={gridStyle}
      >
        {children}
      </div>
    );
  }

  return (
    <motion.div
      className={cn('w-full', gapClasses[gap], className)}
      style={gridStyle}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      {React.Children.map(children, (child, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{
            duration: 0.4,
            delay: index * 0.1,
            ease: 'easeOut',
          }}
          whileHover={{
            y: -4,
            transition: { duration: 0.2 },
          }}
        >
          {child}
        </motion.div>
      ))}
    </motion.div>
  );
}

// Dashboard Metrics Grid
interface MetricsGridProps {
  children: React.ReactNode;
  layout?: 'compact' | 'comfortable' | 'spacious';
  className?: string;
  animated?: boolean;
}

export function MetricsGrid({
  children,
  layout = 'comfortable',
  className,
  animated = true,
}: MetricsGridProps) {
  const layoutConfigs = {
    compact: {
      columns: 'grid-cols-2 md:grid-cols-4 lg:grid-cols-6',
      gap: 'gap-3',
    },
    comfortable: {
      columns: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
      gap: 'gap-6',
    },
    spacious: {
      columns: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
      gap: 'gap-8',
    },
  };

  const config = layoutConfigs[layout];

  return (
    <ResponsiveGrid
      columns={{
        mobile: layout === 'compact' ? 2 : 1,
        tablet: layout === 'compact' ? 4 : 2,
        desktop: layout === 'compact' ? 6 : layout === 'comfortable' ? 4 : 3,
      }}
      gap={layout === 'compact' ? 'sm' : layout === 'comfortable' ? 'md' : 'lg'}
      className={className}
      animated={animated}
    >
      {children}
    </ResponsiveGrid>
  );
}

// Sidebar Layout with Responsive Behavior
interface SidebarLayoutProps {
  sidebar: React.ReactNode;
  main: React.ReactNode;
  sidebarWidth?: 'sm' | 'md' | 'lg';
  collapsible?: boolean;
  defaultCollapsed?: boolean;
  className?: string;
}

export function SidebarLayout({
  sidebar,
  main,
  sidebarWidth = 'md',
  collapsible = false,
  defaultCollapsed = false,
  className,
}: SidebarLayoutProps) {
  const [isCollapsed, setIsCollapsed] = React.useState(defaultCollapsed);

  const widthClasses = {
    sm: isCollapsed ? 'w-16' : 'w-48',
    md: isCollapsed ? 'w-16' : 'w-64',
    lg: isCollapsed ? 'w-16' : 'w-80',
  };

  return (
    <div className={cn('flex h-full', className)}>
      {/* Sidebar */}
      <motion.aside
        className={cn(
          'border-r bg-card transition-all duration-300',
          widthClasses[sidebarWidth],
          'hidden md:block'
        )}
        animate={{ width: isCollapsed ? 64 : sidebarWidth === 'sm' ? 192 : sidebarWidth === 'md' ? 256 : 320 }}
        transition={{ duration: 0.3, ease: 'easeInOut' }}
      >
        {collapsible && (
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="absolute top-4 right-4 p-1 rounded hover:bg-accent"
            aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          >
            <motion.div
              animate={{ rotate: isCollapsed ? 180 : 0 }}
              transition={{ duration: 0.2 }}
            >
              ←
            </motion.div>
          </button>
        )}
        <div className={cn('h-full', isCollapsed && 'overflow-hidden')}>
          {sidebar}
        </div>
      </motion.aside>

      {/* Main Content */}
      <main className="flex-1 overflow-hidden">
        {main}
      </main>
    </div>
  );
}

// Container with Max Width and Responsive Padding
interface ContainerProps {
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  className?: string;
}

export function Container({
  children,
  size = 'xl',
  padding = 'md',
  className,
}: ContainerProps) {
  const sizeClasses = {
    sm: 'max-w-2xl',
    md: 'max-w-4xl',
    lg: 'max-w-6xl',
    xl: 'max-w-7xl',
    full: 'max-w-full',
  };

  const paddingClasses = {
    none: '',
    sm: 'px-4 py-2',
    md: 'px-6 py-4',
    lg: 'px-8 py-6',
  };

  return (
    <div className={cn(
      'mx-auto w-full',
      sizeClasses[size],
      paddingClasses[padding],
      className
    )}>
      {children}
    </div>
  );
}

// Flex Layout Utilities
interface FlexLayoutProps {
  children: React.ReactNode;
  direction?: 'row' | 'col';
  align?: 'start' | 'center' | 'end' | 'stretch';
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';
  gap?: 'sm' | 'md' | 'lg';
  wrap?: boolean;
  className?: string;
}

export function FlexLayout({
  children,
  direction = 'row',
  align = 'start',
  justify = 'start',
  gap = 'md',
  wrap = false,
  className,
}: FlexLayoutProps) {
  const directionClasses = {
    row: 'flex-row',
    col: 'flex-col',
  };

  const alignClasses = {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end',
    stretch: 'items-stretch',
  };

  const justifyClasses = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
    between: 'justify-between',
    around: 'justify-around',
    evenly: 'justify-evenly',
  };

  const gapClasses = {
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
  };

  return (
    <div className={cn(
      'flex',
      directionClasses[direction],
      alignClasses[align],
      justifyClasses[justify],
      gapClasses[gap],
      wrap && 'flex-wrap',
      className
    )}>
      {children}
    </div>
  );
}
