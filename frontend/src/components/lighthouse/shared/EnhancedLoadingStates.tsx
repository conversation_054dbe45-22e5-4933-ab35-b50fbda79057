import React from 'react';
import { motion } from 'framer-motion';
import { cn, animations, layout } from '~/lib/ui-utils';
import { AnimatedBorderWrapper } from '~/components/ui/AnimatedBorderWrapper';
import { SuccessFlipWrapper } from '~/components/ui/SuccessFlipWrapper';
import { Skeleton } from '~/components/ui/loading-states';
import { Card, CardContent, CardHeader } from '~/components/ui/card';
import { ModuleName } from '../types/navigation.types';
import { AccessibilityUtils } from '~/utils/accessibilityUtils';

interface ModuleSkeletonProps {
  module: ModuleName;
  className?: string;
}

interface EnhancedLoadingCardProps {
  title?: string;
  description?: string;
  variant?: 'default' | 'success' | 'error' | 'warning';
  isLoading?: boolean;
  showProgress?: boolean;
  progress?: number;
  className?: string;
  children?: React.ReactNode;
}

interface LoadingStateIndicatorProps {
  message?: string;
  variant?: 'pulse' | 'rotate' | 'gradient' | 'dash';
  size?: 'sm' | 'md' | 'lg';
  color?: string;
  className?: string;
}

export function ModuleSkeleton({ module, className }: ModuleSkeletonProps) {
  const getModuleSkeleton = () => {
    switch (module) {
      case 'dashboard':
        return <DashboardSkeleton />;
      case 'chat':
        return <ChatSkeleton />;
      case 'knowledge':
        return <KnowledgeSkeleton />;
      case 'research':
        return <ResearchSkeleton />;
      case 'agents':
        return <AgentsSkeleton />;
      case 'sources':
        return <SourcesSkeleton />;
      case 'analytics':
        return <AnalyticsSkeleton />;
      case 'insights':
        return <InsightsSkeleton />;
      default:
        return <GenericSkeleton />;
    }
  };

  return (
    <motion.div
      className={cn('h-full p-6', className)}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      {getModuleSkeleton()}
    </motion.div>
  );
}

// Enhanced Loading Card with animated borders and visual feedback
export function EnhancedLoadingCard({
  title = "Loading...",
  description,
  variant = 'default',
  isLoading = true,
  showProgress = false,
  progress = 0,
  className,
  children
}: EnhancedLoadingCardProps) {
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  const getVariantColors = () => {
    switch (variant) {
      case 'success':
        return { border: '#10b981', bg: 'bg-green-50 dark:bg-green-950', text: 'text-green-700 dark:text-green-300' };
      case 'error':
        return { border: '#ef4444', bg: 'bg-red-50 dark:bg-red-950', text: 'text-red-700 dark:text-red-300' };
      case 'warning':
        return { border: '#f59e0b', bg: 'bg-yellow-50 dark:bg-yellow-950', text: 'text-yellow-700 dark:text-yellow-300' };
      default:
        return { border: '#3b82f6', bg: 'bg-blue-50 dark:bg-blue-950', text: 'text-blue-700 dark:text-blue-300' };
    }
  };

  const colors = getVariantColors();

  return (
    <motion.div
      className={cn('relative', className)}
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
    >
      <AnimatedBorderWrapper
        isLoading={isLoading}
        variant={variant === 'error' ? 'pulse' : 'gradient'}
        borderColor={colors.border}
        className="rounded-lg"
        disabled={prefersReducedMotion && !isLoading}
      >
        <Card className={cn(
          'transition-all duration-300',
          isLoading && colors.bg,
          'hover:shadow-lg hover:shadow-primary/10'
        )}>
          <CardHeader className="pb-3">
            <div className="flex items-center gap-3">
              {isLoading && (
                <motion.div
                  className={cn(
                    'h-3 w-3 rounded-full',
                    variant === 'error' ? 'bg-red-500' : 'bg-primary'
                  )}
                  animate={prefersReducedMotion ? {} : {
                    scale: [1, 1.2, 1],
                    opacity: [0.7, 1, 0.7]
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
              )}
              <div className="flex-1">
                <h3 className={cn('font-semibold', colors.text)}>{title}</h3>
                {description && (
                  <p className="text-sm text-muted-foreground mt-1">{description}</p>
                )}
              </div>
            </div>
          </CardHeader>
          {(showProgress || children) && (
            <CardContent>
              {showProgress && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Progress</span>
                    <span className={cn('font-medium', colors.text)}>{progress}%</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2 overflow-hidden">
                    <motion.div
                      className={cn(
                        'h-full rounded-full',
                        variant === 'error' ? 'bg-red-500' : 'bg-primary'
                      )}
                      initial={{ width: 0 }}
                      animate={{ width: `${progress}%` }}
                      transition={{ duration: 0.5, ease: "easeOut" }}
                    />
                  </div>
                </div>
              )}
              {children}
            </CardContent>
          )}
        </Card>
      </AnimatedBorderWrapper>
    </motion.div>
  );
}

// Loading State Indicator with animated borders (user preference)
export function LoadingStateIndicator({
  message = "Loading...",
  variant = 'gradient',
  size = 'md',
  color = '#3b82f6',
  className
}: LoadingStateIndicatorProps) {
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return { container: 'h-8 w-8', text: 'text-xs' };
      case 'lg':
        return { container: 'h-16 w-16', text: 'text-base' };
      default:
        return { container: 'h-12 w-12', text: 'text-sm' };
    }
  };

  const sizeClasses = getSizeClasses();

  return (
    <div className={cn('flex flex-col items-center gap-3', className)}>
      <div className="relative">
        <AnimatedBorderWrapper
          isLoading={true}
          variant={variant}
          borderColor={color}
          className="rounded-full"
          disabled={prefersReducedMotion}
        >
          <div className={cn(
            'rounded-full bg-background flex items-center justify-center',
            sizeClasses.container
          )}>
            <motion.div
              className="h-2 w-2 bg-primary rounded-full"
              animate={prefersReducedMotion ? {} : {
                scale: [1, 1.5, 1],
                opacity: [0.5, 1, 0.5]
              }}
              transition={{
                duration: 1,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          </div>
        </AnimatedBorderWrapper>
      </div>
      {message && (
        <motion.p
          className={cn('text-muted-foreground text-center', sizeClasses.text)}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          {message}
        </motion.p>
      )}
    </div>
  );
}

function DashboardSkeleton() {
  return (
    <div className={cn(layout.spaceY.lg)}>
      {/* Metrics Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <AnimatedBorderWrapper
            key={i}
            isLoading={true}
            variant="pulse"
            borderColor="#3b82f6"
            className="rounded-lg"
          >
            <Card>
              <CardHeader className="pb-3">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-8 w-16" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-2 w-full" />
              </CardContent>
            </Card>
          </AnimatedBorderWrapper>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid gap-6 lg:grid-cols-3">
        <div className={cn(layout.spaceY.md)}>
          <AnimatedBorderWrapper isLoading={true} variant="gradient" className="rounded-lg">
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
              </CardHeader>
              <CardContent className={cn(layout.spaceY.sm)}>
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </CardContent>
            </Card>
          </AnimatedBorderWrapper>
        </div>

        <div className={cn(layout.spaceY.md)}>
          <AnimatedBorderWrapper isLoading={true} variant="rotate" className="rounded-lg">
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-28" />
              </CardHeader>
              <CardContent>
                <div className={cn(layout.spaceY.sm)}>
                  {Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className="flex items-center gap-3">
                      <Skeleton variant="circular" className="h-8 w-8" />
                      <div className="flex-1 space-y-1">
                        <Skeleton className="h-3 w-full" />
                        <Skeleton className="h-2 w-2/3" />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </AnimatedBorderWrapper>
        </div>

        <div className={cn(layout.spaceY.md)}>
          <AnimatedBorderWrapper isLoading={true} variant="pulse" className="rounded-lg">
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-36" />
              </CardHeader>
              <CardContent className={cn(layout.spaceY.md)}>
                <Skeleton className="h-20 w-full" />
                <div className={cn(layout.spaceY.sm)}>
                  <Skeleton className="h-3 w-full" />
                  <Skeleton className="h-3 w-4/5" />
                </div>
              </CardContent>
            </Card>
          </AnimatedBorderWrapper>
        </div>
      </div>
    </div>
  );
}

function ChatSkeleton() {
  return (
    <div className="flex h-full">
      {/* Sidebar */}
      <div className="w-80 border-r bg-muted/50 p-6 space-y-6">
        <AnimatedBorderWrapper isLoading={true} variant="pulse" className="rounded-lg">
          <div className="space-y-4">
            <Skeleton className="h-6 w-32" />
            <div className="space-y-3">
              {Array.from({ length: 3 }).map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-4">
                    <Skeleton className="h-4 w-24 mb-2" />
                    <Skeleton className="h-3 w-full" />
                    <Skeleton className="h-3 w-2/3" />
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </AnimatedBorderWrapper>
      </div>

      {/* Chat Area */}
      <div className="flex-1 flex flex-col">
        <div className="border-b p-4">
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-3 w-32 mt-1" />
        </div>
        
        <div className="flex-1 p-4 space-y-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <div key={i} className={cn('flex gap-3', i % 2 === 0 ? 'justify-start' : 'justify-end')}>
              {i % 2 === 0 && <Skeleton variant="circular" className="h-8 w-8" />}
              <div className={cn('max-w-xs', i % 2 === 0 ? 'bg-muted' : 'bg-primary/10', 'rounded-lg p-3')}>
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-3/4" />
              </div>
              {i % 2 === 1 && <Skeleton variant="circular" className="h-8 w-8" />}
            </div>
          ))}
        </div>

        <div className="border-t p-4">
          <div className="flex gap-2">
            <Skeleton className="h-10 flex-1" />
            <Skeleton className="h-10 w-10" />
          </div>
        </div>
      </div>
    </div>
  );
}

function KnowledgeSkeleton() {
  return (
    <div className={cn(layout.spaceY.lg)}>
      <div className="flex items-center justify-between">
        <Skeleton className="h-8 w-48" />
        <Skeleton className="h-10 w-32" />
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {Array.from({ length: 6 }).map((_, i) => (
          <AnimatedBorderWrapper
            key={i}
            isLoading={true}
            variant="gradient"
            className="rounded-lg"
          >
            <Card>
              <CardHeader>
                <div className="flex items-center gap-3">
                  <Skeleton variant="circular" className="h-10 w-10" />
                  <div className="flex-1">
                    <Skeleton className="h-5 w-32" />
                    <Skeleton className="h-3 w-24 mt-1" />
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-4/5 mb-2" />
                <Skeleton className="h-4 w-2/3" />
                <div className="flex gap-2 mt-4">
                  <Skeleton className="h-6 w-16" />
                  <Skeleton className="h-6 w-20" />
                </div>
              </CardContent>
            </Card>
          </AnimatedBorderWrapper>
        ))}
      </div>
    </div>
  );
}

function ResearchSkeleton() {
  return (
    <div className="grid gap-6 lg:grid-cols-2">
      <div className={cn(layout.spaceY.md)}>
        <Skeleton className="h-6 w-32" />
        <AnimatedBorderWrapper isLoading={true} variant="pulse" className="rounded-lg">
          <Card>
            <CardContent className="p-6">
              <div className={cn(layout.spaceY.md)}>
                {Array.from({ length: 4 }).map((_, i) => (
                  <div key={i} className="border-l-2 border-muted pl-4">
                    <Skeleton className="h-4 w-full mb-2" />
                    <Skeleton className="h-3 w-3/4 mb-1" />
                    <Skeleton className="h-3 w-1/2" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </AnimatedBorderWrapper>
      </div>

      <div className={cn(layout.spaceY.md)}>
        <Skeleton className="h-6 w-28" />
        <AnimatedBorderWrapper isLoading={true} variant="rotate" className="rounded-lg">
          <Card>
            <CardContent className="p-6">
              <Skeleton className="h-40 w-full mb-4" />
              <div className={cn(layout.spaceY.sm)}>
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-5/6" />
                <Skeleton className="h-4 w-2/3" />
              </div>
            </CardContent>
          </Card>
        </AnimatedBorderWrapper>
      </div>
    </div>
  );
}

function AgentsSkeleton() {
  return (
    <div className={cn(layout.spaceY.lg)}>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {Array.from({ length: 3 }).map((_, i) => (
          <AnimatedBorderWrapper
            key={i}
            isLoading={true}
            variant="pulse"
            borderColor="#10b981"
            className="rounded-lg"
          >
            <Card>
              <CardHeader>
                <div className="flex items-center gap-3">
                  <Skeleton variant="circular" className="h-12 w-12" />
                  <div className="flex-1">
                    <Skeleton className="h-5 w-24" />
                    <Skeleton className="h-3 w-16 mt-1" />
                  </div>
                  <Skeleton className="h-6 w-16" />
                </div>
              </CardHeader>
              <CardContent>
                <Skeleton className="h-2 w-full mb-2" />
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-3/4" />
              </CardContent>
            </Card>
          </AnimatedBorderWrapper>
        ))}
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
          </CardHeader>
          <CardContent className={cn(layout.spaceY.sm)}>
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-center justify-between p-3 border rounded">
                <div className="flex items-center gap-3">
                  <Skeleton variant="circular" className="h-8 w-8" />
                  <div>
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-3 w-16 mt-1" />
                  </div>
                </div>
                <Skeleton className="h-6 w-12" />
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-28" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-32 w-full" />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

function SourcesSkeleton() {
  return (
    <div className={cn(layout.spaceY.lg)}>
      <div className="flex items-center justify-between">
        <Skeleton className="h-8 w-40" />
        <Skeleton className="h-10 w-28" />
      </div>

      <div className="grid gap-4">
        {Array.from({ length: 5 }).map((_, i) => (
          <AnimatedBorderWrapper
            key={i}
            isLoading={true}
            variant="gradient"
            className="rounded-lg"
          >
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-4">
                  <Skeleton variant="circular" className="h-12 w-12" />
                  <div className="flex-1">
                    <Skeleton className="h-5 w-48 mb-1" />
                    <Skeleton className="h-3 w-32 mb-2" />
                    <Skeleton className="h-4 w-full" />
                  </div>
                  <div className="flex gap-2">
                    <Skeleton className="h-8 w-8" />
                    <Skeleton className="h-8 w-8" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </AnimatedBorderWrapper>
        ))}
      </div>
    </div>
  );
}

function AnalyticsSkeleton() {
  return (
    <div className={cn(layout.spaceY.lg)}>
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <Skeleton className="h-8 w-16 mb-2" />
              <Skeleton className="h-4 w-24" />
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-64 w-full" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-28" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-64 w-full" />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

function InsightsSkeleton() {
  return (
    <div className={cn(layout.spaceY.lg)}>
      <div className="grid gap-6 md:grid-cols-2">
        {Array.from({ length: 4 }).map((_, i) => (
          <AnimatedBorderWrapper
            key={i}
            isLoading={true}
            variant="pulse"
            borderColor="#f59e0b"
            className="rounded-lg"
          >
            <Card>
              <CardHeader>
                <div className="flex items-center gap-3">
                  <Skeleton variant="circular" className="h-10 w-10" />
                  <div className="flex-1">
                    <Skeleton className="h-5 w-32" />
                    <Skeleton className="h-3 w-20 mt-1" />
                  </div>
                  <Skeleton className="h-6 w-16" />
                </div>
              </CardHeader>
              <CardContent>
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-4/5 mb-2" />
                <Skeleton className="h-4 w-2/3" />
                <div className="flex gap-2 mt-4">
                  <Skeleton className="h-8 w-20" />
                  <Skeleton className="h-8 w-16" />
                </div>
              </CardContent>
            </Card>
          </AnimatedBorderWrapper>
        ))}
      </div>
    </div>
  );
}

function GenericSkeleton() {
  return (
    <div className={cn(layout.spaceY.lg)}>
      <Skeleton className="h-8 w-48" />
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-3/4" />
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
