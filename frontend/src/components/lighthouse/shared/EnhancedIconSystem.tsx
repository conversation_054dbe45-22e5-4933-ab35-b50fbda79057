import React from 'react';
import { motion, useAnimation, AnimatePresence } from 'framer-motion';
import { cn, animations, visualEffects, accessibility } from '~/lib/ui-utils';
import { AccessibilityUtils } from '~/utils/accessibilityUtils';

// Import existing custom icons
import { SparklesIcon } from '~/components/ui/icons/sparkles';
import { HomeIcon } from '~/components/ui/icons/home';
import { ActivityIcon } from '~/components/ui/icons/activity';
import { CpuIcon } from '~/components/ui/icons/cpu';
import { ChartLineIcon } from '~/components/ui/icons/chart-line';
import { FileTextIcon } from '~/components/ui/icons/file-text';
import { MessageCircleIcon } from '~/components/ui/icons/message-circle';
import { BookTextIcon } from '~/components/ui/icons/book-text';
import { TelescopeIcon } from '~/components/ui/icons/telescope';
import { ClockIcon } from '~/components/ui/icons/clock';
import { SearchIcon } from '~/components/ui/icons/search';
import { RocketIcon } from '~/components/ui/icons/rocket';
import { UploadIcon } from '~/components/ui/icons/upload';

export type IconName = 
  | 'sparkles' 
  | 'home' 
  | 'activity' 
  | 'cpu' 
  | 'chart-line' 
  | 'file-text' 
  | 'message-circle' 
  | 'book-text' 
  | 'telescope' 
  | 'clock' 
  | 'search' 
  | 'rocket' 
  | 'upload';

export type IconSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
export type IconVariant = 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'muted';
export type IconState = 'idle' | 'loading' | 'success' | 'error' | 'active';

interface EnhancedIconProps {
  name: IconName;
  size?: IconSize;
  variant?: IconVariant;
  state?: IconState;
  className?: string;
  animated?: boolean;
  loading?: boolean;
  disabled?: boolean;
  tooltip?: string;
  onClick?: () => void;
  'aria-label'?: string;
}

// Icon component mapping
const iconComponents = {
  'sparkles': SparklesIcon,
  'home': HomeIcon,
  'activity': ActivityIcon,
  'cpu': CpuIcon,
  'chart-line': ChartLineIcon,
  'file-text': FileTextIcon,
  'message-circle': MessageCircleIcon,
  'book-text': BookTextIcon,
  'telescope': TelescopeIcon,
  'clock': ClockIcon,
  'search': SearchIcon,
  'rocket': RocketIcon,
  'upload': UploadIcon,
} as const;

// Size mappings
const sizeMap: Record<IconSize, number> = {
  xs: 12,
  sm: 16,
  md: 20,
  lg: 24,
  xl: 28,
  '2xl': 32,
};

// Variant color classes
const variantClasses: Record<IconVariant, string> = {
  default: 'text-foreground',
  primary: 'text-primary',
  secondary: 'text-secondary',
  success: 'text-green-500',
  warning: 'text-yellow-500',
  error: 'text-red-500',
  muted: 'text-muted-foreground',
};

// State-specific animations
const stateAnimations = {
  idle: {},
  loading: {
    rotate: [0, 360],
    transition: {
      duration: 2,
      repeat: Infinity,
      ease: 'linear',
    },
  },
  success: {
    scale: [1, 1.2, 1],
    transition: {
      duration: 0.6,
      ease: 'easeOut',
    },
  },
  error: {
    x: [-2, 2, -2, 2, 0],
    transition: {
      duration: 0.4,
      ease: 'easeInOut',
    },
  },
  active: {
    scale: [1, 1.1, 1],
    transition: {
      duration: 0.3,
      ease: 'easeOut',
    },
  },
};

export function EnhancedIcon({
  name,
  size = 'md',
  variant = 'default',
  state = 'idle',
  className,
  animated = true,
  loading = false,
  disabled = false,
  tooltip,
  onClick,
  'aria-label': ariaLabel,
}: EnhancedIconProps) {
  const controls = useAnimation();
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();
  const IconComponent = iconComponents[name];
  const iconSize = sizeMap[size];

  // Handle loading state
  React.useEffect(() => {
    if (loading && animated && !prefersReducedMotion) {
      controls.start(stateAnimations.loading);
    } else if (state !== 'idle' && animated && !prefersReducedMotion) {
      controls.start(stateAnimations[state]);
    } else {
      controls.start(stateAnimations.idle);
    }
  }, [loading, state, animated, prefersReducedMotion, controls]);

  const handleClick = () => {
    if (disabled) return;
    
    if (onClick) {
      onClick();
      
      // Trigger click animation
      if (animated && !prefersReducedMotion) {
        controls.start({
          scale: [1, 0.95, 1],
          transition: { duration: 0.15 },
        });
      }
    }
  };

  const handleMouseEnter = () => {
    if (disabled || !animated || prefersReducedMotion) return;
    
    controls.start({
      scale: 1.1,
      transition: { duration: 0.2, ease: 'easeOut' },
    });
  };

  const handleMouseLeave = () => {
    if (disabled || !animated || prefersReducedMotion) return;
    
    controls.start({
      scale: 1,
      transition: { duration: 0.2, ease: 'easeOut' },
    });
  };

  const iconElement = (
    <motion.div
      className={cn(
        'inline-flex items-center justify-center',
        variantClasses[variant],
        onClick && 'cursor-pointer',
        disabled && 'opacity-50 cursor-not-allowed',
        className
      )}
      animate={controls}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={handleClick}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick && !disabled ? 0 : undefined}
      aria-label={ariaLabel || `${name} icon`}
      onKeyDown={(e) => {
        if ((e.key === 'Enter' || e.key === ' ') && onClick && !disabled) {
          e.preventDefault();
          handleClick();
        }
      }}
    >
      <IconComponent size={iconSize} />
      
      {/* Loading overlay */}
      <AnimatePresence>
        {loading && (
          <motion.div
            className="absolute inset-0 flex items-center justify-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="h-1 w-1 bg-current rounded-full"
              animate={{
                scale: [1, 1.5, 1],
                opacity: [0.5, 1, 0.5],
              }}
              transition={{
                duration: 1,
                repeat: Infinity,
                ease: 'easeInOut',
              }}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );

  // Wrap with tooltip if provided
  if (tooltip) {
    return (
      <div className="relative group">
        {iconElement}
        <div className={cn(
          'absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2',
          'px-2 py-1 text-xs bg-popover text-popover-foreground rounded shadow-lg',
          'opacity-0 group-hover:opacity-100 transition-opacity duration-200',
          'pointer-events-none z-50',
          'whitespace-nowrap'
        )}>
          {tooltip}
        </div>
      </div>
    );
  }

  return iconElement;
}

// Convenience components for common lighthouse icons
export const LighthouseIcon = (props: Omit<EnhancedIconProps, 'name'>) => (
  <EnhancedIcon name="sparkles" {...props} />
);

export const DashboardIcon = (props: Omit<EnhancedIconProps, 'name'>) => (
  <EnhancedIcon name="home" {...props} />
);

export const MetricsIcon = (props: Omit<EnhancedIconProps, 'name'>) => (
  <EnhancedIcon name="activity" {...props} />
);

export const IntelligenceIcon = (props: Omit<EnhancedIconProps, 'name'>) => (
  <EnhancedIcon name="cpu" {...props} />
);

export const AnalyticsIcon = (props: Omit<EnhancedIconProps, 'name'>) => (
  <EnhancedIcon name="chart-line" {...props} />
);

export const KnowledgeIcon = (props: Omit<EnhancedIconProps, 'name'>) => (
  <EnhancedIcon name="book-text" {...props} />
);

export const ResearchIcon = (props: Omit<EnhancedIconProps, 'name'>) => (
  <EnhancedIcon name="telescope" {...props} />
);

export const ChatIcon = (props: Omit<EnhancedIconProps, 'name'>) => (
  <EnhancedIcon name="message-circle" {...props} />
);

export const SourcesIcon = (props: Omit<EnhancedIconProps, 'name'>) => (
  <EnhancedIcon name="file-text" {...props} />
);
