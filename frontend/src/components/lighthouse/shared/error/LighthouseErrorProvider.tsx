import React, { createContext, useContext, useCallback, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ErrorStateCard, useErrorState } from '../ErrorStateCard';
import { ComponentErrorBoundary, useError<PERSON>andler } from '~/components/ui/error-boundary';
import { cn } from '~/lib/utils';
import { AccessibilityUtils } from '~/utils/accessibilityUtils';
import { useLighthouseStore } from '../store/lighthouse-store';

// Error types specific to Lighthouse
export type LighthouseErrorType = 
  | 'ai_service_error'
  | 'knowledge_processing_error'
  | 'file_upload_error'
  | 'network_error'
  | 'validation_error'
  | 'permission_error'
  | 'rate_limit_error'
  | 'unknown_error';

export interface LighthouseError {
  id: string;
  type: LighthouseErrorType;
  title: string;
  message: string;
  severity: 'critical' | 'warning' | 'info';
  timestamp: Date;
  context?: {
    component?: string;
    action?: string;
    userId?: string;
    projectId?: string;
    metadata?: Record<string, any>;
  };
  recoverable: boolean;
  retryAction?: () => Promise<void> | void;
  dismissible: boolean;
}

interface LighthouseErrorContextType {
  errors: LighthouseError[];
  showError: (error: Omit<LighthouseError, 'id' | 'timestamp'>) => string;
  dismissError: (errorId: string) => void;
  retryError: (errorId: string) => Promise<void>;
  clearAllErrors: () => void;
  hasErrors: boolean;
  hasCriticalErrors: boolean;
}

const LighthouseErrorContext = createContext<LighthouseErrorContextType | null>(null);

interface LighthouseErrorProviderProps {
  children: React.ReactNode;
  maxErrors?: number;
  autoRetryAttempts?: number;
  autoRetryDelay?: number;
}

export function LighthouseErrorProvider({
  children,
  maxErrors = 5,
  autoRetryAttempts = 3,
  autoRetryDelay = 1000,
}: LighthouseErrorProviderProps) {
  const [errors, setErrors] = useState<LighthouseError[]>([]);
  const [retryAttempts, setRetryAttempts] = useState<Record<string, number>>({});
  const { recordLearning } = useLighthouseStore();
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  const showError = useCallback((errorData: Omit<LighthouseError, 'id' | 'timestamp'>) => {
    const errorId = `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const error: LighthouseError = {
      ...errorData,
      id: errorId,
      timestamp: new Date(),
    };

    setErrors(prev => {
      const newErrors = [error, ...prev].slice(0, maxErrors);
      return newErrors;
    });

    // Record learning event for error tracking
    recordLearning({
      type: 'error',
      trigger: 'error_occurred',
      before: {
        confidence: 0.8,
        understanding: 'System functioning normally',
        relatedNodes: [],
        supportingEvidence: []
      },
      after: {
        confidence: 0.3,
        understanding: `Error occurred: ${error.type}`,
        relatedNodes: [],
        supportingEvidence: [error.message]
      }
    });

    // Auto-retry for recoverable errors
    if (error.recoverable && error.retryAction && autoRetryAttempts > 0) {
      setTimeout(() => {
        const currentAttempts = retryAttempts[errorId] || 0;
        if (currentAttempts < autoRetryAttempts) {
          retryError(errorId);
        }
      }, autoRetryDelay);
    }

    return errorId;
  }, [maxErrors, autoRetryAttempts, autoRetryDelay, retryAttempts, recordLearning]);

  const dismissError = useCallback((errorId: string) => {
    setErrors(prev => prev.filter(error => error.id !== errorId));
    setRetryAttempts(prev => {
      const { [errorId]: _, ...rest } = prev;
      return rest;
    });
  }, []);

  const retryError = useCallback(async (errorId: string) => {
    const error = errors.find(e => e.id === errorId);
    if (!error || !error.retryAction) return;

    const currentAttempts = retryAttempts[errorId] || 0;
    setRetryAttempts(prev => ({ ...prev, [errorId]: currentAttempts + 1 }));

    try {
      await error.retryAction();
      dismissError(errorId);
      
      // Record successful recovery
      recordLearning({
        type: 'recovery',
        trigger: 'error_retry_success',
        before: {
          confidence: 0.3,
          understanding: `Error: ${error.type}`,
          relatedNodes: [],
          supportingEvidence: [error.message]
        },
        after: {
          confidence: 0.9,
          understanding: 'Error resolved through retry',
          relatedNodes: [],
          supportingEvidence: ['Retry successful']
        }
      });
    } catch (retryError) {
      console.error('Retry failed:', retryError);
      
      // Update error with retry failure info
      setErrors(prev => prev.map(e => 
        e.id === errorId 
          ? { 
              ...e, 
              message: `${e.message} (Retry ${currentAttempts + 1} failed)`,
              severity: currentAttempts >= autoRetryAttempts - 1 ? 'critical' : e.severity
            }
          : e
      ));
    }
  }, [errors, retryAttempts, dismissError, autoRetryAttempts, recordLearning]);

  const clearAllErrors = useCallback(() => {
    setErrors([]);
    setRetryAttempts({});
  }, []);

  const hasErrors = errors.length > 0;
  const hasCriticalErrors = errors.some(error => error.severity === 'critical');

  const contextValue: LighthouseErrorContextType = {
    errors,
    showError,
    dismissError,
    retryError,
    clearAllErrors,
    hasErrors,
    hasCriticalErrors,
  };

  return (
    <LighthouseErrorContext.Provider value={contextValue}>
      <ComponentErrorBoundary
        onError={(error, errorInfo) => {
          showError({
            type: 'unknown_error',
            title: 'Component Error',
            message: error.message,
            severity: 'critical',
            context: {
              component: errorInfo.componentStack.split('\n')[1]?.trim(),
              metadata: { stack: error.stack }
            },
            recoverable: true,
            retryAction: () => window.location.reload(),
            dismissible: true,
          });
        }}
      >
        {children}
        
        {/* Error Display Overlay */}
        <ErrorDisplayOverlay 
          errors={errors}
          onDismiss={dismissError}
          onRetry={retryError}
          prefersReducedMotion={prefersReducedMotion}
        />
      </ComponentErrorBoundary>
    </LighthouseErrorContext.Provider>
  );
}

// Error Display Overlay Component
interface ErrorDisplayOverlayProps {
  errors: LighthouseError[];
  onDismiss: (errorId: string) => void;
  onRetry: (errorId: string) => Promise<void>;
  prefersReducedMotion: boolean;
}

function ErrorDisplayOverlay({ 
  errors, 
  onDismiss, 
  onRetry, 
  prefersReducedMotion 
}: ErrorDisplayOverlayProps) {
  if (errors.length === 0) return null;

  return (
    <div className={cn(
      'fixed top-4 right-4 z-50 space-y-2',
      'max-w-sm w-full pointer-events-none'
    )}>
      <AnimatePresence mode="popLayout">
        {errors.slice(0, 3).map((error) => (
          <motion.div
            key={error.id}
            initial={prefersReducedMotion ? {} : { opacity: 0, x: 300, scale: 0.8 }}
            animate={prefersReducedMotion ? {} : { opacity: 1, x: 0, scale: 1 }}
            exit={prefersReducedMotion ? {} : { opacity: 0, x: 300, scale: 0.8 }}
            transition={{ duration: 0.3, ease: 'easeOut' }}
            className="pointer-events-auto"
          >
            <ErrorStateCard
              title={error.title}
              message={error.message}
              severity={error.severity}
              showRetry={error.recoverable}
              showDismiss={error.dismissible}
              onRetry={() => onRetry(error.id)}
              onDismiss={() => onDismiss(error.id)}
              errorCode={error.type}
              animated={!prefersReducedMotion}
              className="shadow-lg"
            />
          </motion.div>
        ))}
      </AnimatePresence>
      
      {errors.length > 3 && (
        <motion.div
          initial={prefersReducedMotion ? {} : { opacity: 0 }}
          animate={prefersReducedMotion ? {} : { opacity: 1 }}
          className="text-center text-sm text-muted-foreground bg-background/80 backdrop-blur-sm rounded-md p-2 border"
        >
          +{errors.length - 3} more errors
        </motion.div>
      )}
    </div>
  );
}

// Hook to use the error context
export function useLighthouseError() {
  const context = useContext(LighthouseErrorContext);
  if (!context) {
    throw new Error('useLighthouseError must be used within a LighthouseErrorProvider');
  }
  return context;
}

// Utility functions for common error scenarios
export const LighthouseErrorUtils = {
  createAIServiceError: (message: string, retryAction?: () => Promise<void>): Omit<LighthouseError, 'id' | 'timestamp'> => ({
    type: 'ai_service_error',
    title: 'AI Service Error',
    message,
    severity: 'warning',
    context: { component: 'AI Service' },
    recoverable: !!retryAction,
    retryAction,
    dismissible: true,
  }),

  createFileUploadError: (filename: string, retryAction?: () => Promise<void>): Omit<LighthouseError, 'id' | 'timestamp'> => ({
    type: 'file_upload_error',
    title: 'File Upload Failed',
    message: `Failed to upload "${filename}". Please check the file format and size.`,
    severity: 'warning',
    context: { component: 'File Upload', metadata: { filename } },
    recoverable: !!retryAction,
    retryAction,
    dismissible: true,
  }),

  createNetworkError: (retryAction?: () => Promise<void>): Omit<LighthouseError, 'id' | 'timestamp'> => ({
    type: 'network_error',
    title: 'Network Error',
    message: 'Unable to connect to the server. Please check your internet connection.',
    severity: 'critical',
    context: { component: 'Network' },
    recoverable: !!retryAction,
    retryAction,
    dismissible: true,
  }),

  createValidationError: (field: string, message: string): Omit<LighthouseError, 'id' | 'timestamp'> => ({
    type: 'validation_error',
    title: 'Validation Error',
    message: `${field}: ${message}`,
    severity: 'info',
    context: { component: 'Validation', metadata: { field } },
    recoverable: false,
    dismissible: true,
  }),
};
