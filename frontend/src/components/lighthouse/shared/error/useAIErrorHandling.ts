import { useCallback } from 'react';
import { useLighthouseError, LighthouseErrorUtils } from './LighthouseErrorProvider';
import { AIServiceError, ContextError, RecommendationError } from '../ai';

// AI-specific error handling hook
export function useAIErrorHandling() {
  const { showError } = useLighthouseError();

  const handleAIError = useCallback((error: unknown, context?: {
    operation?: string;
    retryAction?: () => Promise<void>;
    component?: string;
  }) => {
    console.error('AI Error:', error);

    if (error instanceof AIServiceError) {
      return showError({
        type: 'ai_service_error',
        title: 'AI Service Error',
        message: getAIErrorMessage(error.code, error.message),
        severity: getAIErrorSeverity(error.code),
        context: {
          component: context?.component || 'AI Service',
          action: context?.operation,
          metadata: { code: error.code, details: error.details }
        },
        recoverable: isRecoverableAIError(error.code),
        retryAction: context?.retryAction,
        dismissible: true,
      });
    }

    if (error instanceof ContextError) {
      return showError({
        type: 'ai_service_error',
        title: 'Context Processing Error',
        message: 'Failed to process context for AI interaction. This may affect response quality.',
        severity: 'warning',
        context: {
          component: context?.component || 'Context Manager',
          action: context?.operation,
          metadata: { contextId: error.contextId }
        },
        recoverable: true,
        retryAction: context?.retryAction,
        dismissible: true,
      });
    }

    if (error instanceof RecommendationError) {
      return showError({
        type: 'ai_service_error',
        title: 'Recommendation Generation Failed',
        message: 'Unable to generate intelligent recommendations. Basic functionality remains available.',
        severity: 'info',
        context: {
          component: context?.component || 'Recommendation Engine',
          action: context?.operation,
          metadata: { strategy: error.strategy }
        },
        recoverable: true,
        retryAction: context?.retryAction,
        dismissible: true,
      });
    }

    // Generic error handling
    const errorMessage = error instanceof Error ? error.message : 'Unknown AI error occurred';
    return showError({
      type: 'ai_service_error',
      title: 'AI Operation Failed',
      message: errorMessage,
      severity: 'warning',
      context: {
        component: context?.component || 'AI System',
        action: context?.operation,
      },
      recoverable: !!context?.retryAction,
      retryAction: context?.retryAction,
      dismissible: true,
    });
  }, [showError]);

  const handleFileProcessingError = useCallback((error: unknown, filename: string, context?: {
    retryAction?: () => Promise<void>;
    component?: string;
  }) => {
    console.error('File Processing Error:', error);

    const errorMessage = error instanceof Error ? error.message : 'Unknown file processing error';
    
    return showError({
      type: 'file_upload_error',
      title: 'File Processing Failed',
      message: `Failed to process "${filename}": ${errorMessage}`,
      severity: 'warning',
      context: {
        component: context?.component || 'File Processor',
        action: 'file_processing',
        metadata: { filename, error: errorMessage }
      },
      recoverable: !!context?.retryAction,
      retryAction: context?.retryAction,
      dismissible: true,
    });
  }, [showError]);

  const handleNetworkError = useCallback((error: unknown, context?: {
    operation?: string;
    retryAction?: () => Promise<void>;
    component?: string;
  }) => {
    console.error('Network Error:', error);

    const isOffline = !navigator.onLine;
    const errorMessage = isOffline 
      ? 'You appear to be offline. Please check your internet connection.'
      : 'Network request failed. Please try again.';

    return showError({
      type: 'network_error',
      title: isOffline ? 'Offline' : 'Network Error',
      message: errorMessage,
      severity: isOffline ? 'critical' : 'warning',
      context: {
        component: context?.component || 'Network',
        action: context?.operation,
        metadata: { offline: isOffline }
      },
      recoverable: true,
      retryAction: context?.retryAction,
      dismissible: !isOffline,
    });
  }, [showError]);

  const handleValidationError = useCallback((field: string, message: string, context?: {
    component?: string;
  }) => {
    return showError({
      type: 'validation_error',
      title: 'Validation Error',
      message: `${field}: ${message}`,
      severity: 'info',
      context: {
        component: context?.component || 'Form Validation',
        action: 'validation',
        metadata: { field }
      },
      recoverable: false,
      dismissible: true,
    });
  }, [showError]);

  const handleRateLimitError = useCallback((retryAfter?: number, context?: {
    component?: string;
    retryAction?: () => Promise<void>;
  }) => {
    const retryMessage = retryAfter 
      ? ` Please wait ${Math.ceil(retryAfter / 1000)} seconds before trying again.`
      : ' Please wait a moment before trying again.';

    return showError({
      type: 'rate_limit_error',
      title: 'Rate Limit Exceeded',
      message: `Too many requests.${retryMessage}`,
      severity: 'warning',
      context: {
        component: context?.component || 'API',
        action: 'rate_limit',
        metadata: { retryAfter }
      },
      recoverable: true,
      retryAction: retryAfter && context?.retryAction 
        ? () => new Promise(resolve => setTimeout(() => {
            context.retryAction?.();
            resolve();
          }, retryAfter))
        : context?.retryAction,
      dismissible: true,
    });
  }, [showError]);

  return {
    handleAIError,
    handleFileProcessingError,
    handleNetworkError,
    handleValidationError,
    handleRateLimitError,
  };
}

// Helper functions for AI error classification
function getAIErrorMessage(code: string, originalMessage: string): string {
  switch (code) {
    case 'INVALID_API_KEY':
      return 'Invalid API key. Please check your AI service configuration.';
    case 'QUOTA_EXCEEDED':
      return 'AI service quota exceeded. Please try again later or upgrade your plan.';
    case 'MODEL_UNAVAILABLE':
      return 'The requested AI model is currently unavailable. Please try again later.';
    case 'CONTEXT_TOO_LONG':
      return 'The conversation context is too long. Please start a new conversation.';
    case 'CONTENT_FILTERED':
      return 'The content was filtered by the AI service. Please rephrase your request.';
    case 'TIMEOUT':
      return 'AI service request timed out. Please try again.';
    case 'NETWORK_ERROR':
      return 'Unable to connect to AI service. Please check your internet connection.';
    default:
      return originalMessage || 'An unexpected AI service error occurred.';
  }
}

function getAIErrorSeverity(code: string): 'critical' | 'warning' | 'info' {
  switch (code) {
    case 'INVALID_API_KEY':
    case 'NETWORK_ERROR':
      return 'critical';
    case 'QUOTA_EXCEEDED':
    case 'MODEL_UNAVAILABLE':
    case 'TIMEOUT':
      return 'warning';
    case 'CONTEXT_TOO_LONG':
    case 'CONTENT_FILTERED':
      return 'info';
    default:
      return 'warning';
  }
}

function isRecoverableAIError(code: string): boolean {
  switch (code) {
    case 'INVALID_API_KEY':
      return false; // Requires configuration change
    case 'QUOTA_EXCEEDED':
      return false; // Requires plan upgrade or waiting
    case 'MODEL_UNAVAILABLE':
    case 'TIMEOUT':
    case 'NETWORK_ERROR':
    case 'CONTEXT_TOO_LONG':
    case 'CONTENT_FILTERED':
      return true;
    default:
      return true;
  }
}

// Wrapper function for AI operations with automatic error handling
export function withAIErrorHandling<T extends any[], R>(
  operation: (...args: T) => Promise<R>,
  context?: {
    operationName?: string;
    component?: string;
    retryable?: boolean;
  }
) {
  return async (...args: T): Promise<R | null> => {
    const { handleAIError, handleNetworkError } = useAIErrorHandling();
    
    try {
      return await operation(...args);
    } catch (error) {
      if (error instanceof TypeError && error.message.includes('fetch')) {
        handleNetworkError(error, {
          operation: context?.operationName,
          component: context?.component,
          retryAction: context?.retryable ? () => operation(...args) : undefined,
        });
      } else {
        handleAIError(error, {
          operation: context?.operationName,
          component: context?.component,
          retryAction: context?.retryable ? () => operation(...args) : undefined,
        });
      }
      return null;
    }
  };
}
