// Error Provider and Context
export {
  LighthouseErrorProvider,
  useLighthouseError,
  LighthouseErrorUtils,
  type LighthouseError,
  type LighthouseErrorType,
} from './LighthouseErrorProvider';

// AI Error Handling
export {
  useAIErrorHandling,
  withAIErrorHandling,
} from './useAIErrorHandling';

// Re-export from shared components
export {
  ErrorStateCard,
  useErrorState,
} from '../ErrorStateCard';

// Re-export from UI components
export {
  ErrorBoundary,
  PageErrorBoundary,
  SectionErrorBoundary,
  ComponentErrorBoundary,
  AsyncErrorBoundary,
  useErrorHandler,
  withErrorBoundary,
  type ErrorBoundaryProps,
  type ErrorFallbackProps,
} from '~/components/ui/error-boundary';
