import { AIContext, AIMessage } from './AIServiceConnector';
import type { Project, KnowledgeItem, Insight } from '../../types/project.types';
import type { KnowledgeGraph } from '../../types/knowledge.types';

// Context Window Management
export interface ContextWindow {
  size: number;
  content: ContextItem[];
  summary: string;
  relevanceThreshold: number;
}

export interface ContextItem {
  id: string;
  type: 'message' | 'knowledge' | 'insight' | 'document';
  content: string;
  relevance: number;
  timestamp: Date;
  metadata?: Record<string, any>;
}

// Context Relevance Scoring
export interface RelevanceScorer {
  scoreItem(item: ContextItem, query: string, context: AIContext): number;
  scoreKnowledge(knowledge: KnowledgeItem, context: AIContext): number;
  scoreInsight(insight: Insight, context: AIContext): number;
}

// Simple TF-IDF based relevance scorer
export class TFIDFRelevanceScorer implements RelevanceScorer {
  private stopWords = new Set([
    'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were'
  ]);

  scoreItem(item: ContextItem, query: string, context: AIContext): number {
    const itemTokens = this.tokenize(item.content);
    const queryTokens = this.tokenize(query);
    
    let score = 0;
    for (const token of queryTokens) {
      if (itemTokens.includes(token)) {
        score += 1;
      }
    }
    
    // Boost score based on recency
    const ageInHours = (Date.now() - item.timestamp.getTime()) / (1000 * 60 * 60);
    const recencyBoost = Math.max(0, 1 - ageInHours / 24); // Decay over 24 hours
    
    return (score / queryTokens.length) * (1 + recencyBoost * 0.5);
  }

  scoreKnowledge(knowledge: KnowledgeItem, context: AIContext): number {
    const domainMatch = knowledge.domain === context.metadata.domain ? 0.3 : 0;
    const expertiseMatch = context.metadata.expertise.some(exp => 
      knowledge.content.toLowerCase().includes(exp.toLowerCase())
    ) ? 0.2 : 0;
    
    return domainMatch + expertiseMatch + Math.random() * 0.1; // Add small random factor
  }

  scoreInsight(insight: Insight, context: AIContext): number {
    const confidenceScore = insight.confidence * 0.4;
    const impactScore = insight.impact === 'high' ? 0.3 : insight.impact === 'medium' ? 0.2 : 0.1;
    const recencyScore = this.getRecencyScore(insight.timestamp);
    
    return confidenceScore + impactScore + recencyScore;
  }

  private tokenize(text: string): string[] {
    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(token => token.length > 2 && !this.stopWords.has(token));
  }

  private getRecencyScore(timestamp: Date): number {
    const ageInDays = (Date.now() - timestamp.getTime()) / (1000 * 60 * 60 * 24);
    return Math.max(0, 0.3 * (1 - ageInDays / 30)); // Decay over 30 days
  }
}

// Context Manager Implementation
export class ContextManager {
  private relevanceScorer: RelevanceScorer;
  private maxContextSize: number;
  private relevanceThreshold: number;

  constructor(
    relevanceScorer: RelevanceScorer = new TFIDFRelevanceScorer(),
    maxContextSize: number = 4096,
    relevanceThreshold: number = 0.3
  ) {
    this.relevanceScorer = relevanceScorer;
    this.maxContextSize = maxContextSize;
    this.relevanceThreshold = relevanceThreshold;
  }

  // Build context for AI interaction
  buildContext(
    project: Project,
    messages: AIMessage[],
    knowledgeItems: KnowledgeItem[] = [],
    insights: Insight[] = [],
    currentQuery?: string
  ): AIContext {
    const context: AIContext = {
      projectId: project.id,
      sessionId: crypto.randomUUID(),
      messages,
      metadata: {
        domain: project.domain,
        expertise: project.intelligence.domainExpertise.relatedDomains,
        currentFocus: currentQuery,
        relevantKnowledge: [],
      },
    };

    // Add relevant knowledge items
    const relevantKnowledge = this.selectRelevantKnowledge(
      knowledgeItems,
      insights,
      context,
      currentQuery
    );

    context.metadata.relevantKnowledge = relevantKnowledge.map(item => item.content);

    return context;
  }

  // Select most relevant knowledge items
  private selectRelevantKnowledge(
    knowledgeItems: KnowledgeItem[],
    insights: Insight[],
    context: AIContext,
    query?: string
  ): ContextItem[] {
    const contextItems: ContextItem[] = [];

    // Add knowledge items
    for (const item of knowledgeItems) {
      const relevance = this.relevanceScorer.scoreKnowledge(item, context);
      if (relevance >= this.relevanceThreshold) {
        contextItems.push({
          id: item.id,
          type: 'knowledge',
          content: item.content,
          relevance,
          timestamp: new Date(), // Use current time if not available
          metadata: { domain: item.domain, type: item.type },
        });
      }
    }

    // Add insights
    for (const insight of insights) {
      const relevance = this.relevanceScorer.scoreInsight(insight, context);
      if (relevance >= this.relevanceThreshold) {
        contextItems.push({
          id: insight.id,
          type: 'insight',
          content: insight.content,
          relevance,
          timestamp: insight.timestamp,
          metadata: { confidence: insight.confidence, impact: insight.impact },
        });
      }
    }

    // Sort by relevance and limit by context size
    contextItems.sort((a, b) => b.relevance - a.relevance);
    
    return this.limitByTokenCount(contextItems);
  }

  // Estimate token count and limit context
  private limitByTokenCount(items: ContextItem[]): ContextItem[] {
    let totalTokens = 0;
    const selectedItems: ContextItem[] = [];

    for (const item of items) {
      const itemTokens = this.estimateTokenCount(item.content);
      if (totalTokens + itemTokens <= this.maxContextSize) {
        selectedItems.push(item);
        totalTokens += itemTokens;
      } else {
        break;
      }
    }

    return selectedItems;
  }

  // Simple token estimation (roughly 4 characters per token)
  private estimateTokenCount(text: string): number {
    return Math.ceil(text.length / 4);
  }

  // Update context with new information
  updateContext(
    context: AIContext,
    newMessage?: AIMessage,
    newKnowledge?: KnowledgeItem[],
    newInsights?: Insight[]
  ): AIContext {
    const updatedContext = { ...context };

    if (newMessage) {
      updatedContext.messages = [...context.messages, newMessage];
    }

    if (newKnowledge || newInsights) {
      // Re-evaluate relevant knowledge with new items
      const allKnowledge = newKnowledge || [];
      const allInsights = newInsights || [];
      
      const relevantItems = this.selectRelevantKnowledge(
        allKnowledge,
        allInsights,
        updatedContext
      );

      updatedContext.metadata.relevantKnowledge = relevantItems.map(item => item.content);
    }

    return updatedContext;
  }

  // Generate context summary for AI
  generateContextSummary(context: AIContext): string {
    const { metadata } = context;
    
    return `Project: ${metadata.domain} domain
Expertise: ${metadata.expertise.join(', ')}
Current Focus: ${metadata.currentFocus || 'General exploration'}
Relevant Knowledge: ${metadata.relevantKnowledge.length} items
Recent Messages: ${context.messages.length}`;
  }

  // Extract key concepts from context
  extractKeyConcepts(context: AIContext): string[] {
    const concepts = new Set<string>();
    
    // Extract from messages
    for (const message of context.messages) {
      const tokens = message.content.toLowerCase().split(/\s+/);
      tokens.forEach(token => {
        if (token.length > 4 && !this.isCommonWord(token)) {
          concepts.add(token);
        }
      });
    }

    // Extract from relevant knowledge
    for (const knowledge of context.metadata.relevantKnowledge) {
      const tokens = knowledge.toLowerCase().split(/\s+/);
      tokens.forEach(token => {
        if (token.length > 4 && !this.isCommonWord(token)) {
          concepts.add(token);
        }
      });
    }

    return Array.from(concepts).slice(0, 20); // Limit to top 20 concepts
  }

  private isCommonWord(word: string): boolean {
    const commonWords = new Set([
      'this', 'that', 'with', 'have', 'will', 'from', 'they', 'know', 'want', 'been',
      'good', 'much', 'some', 'time', 'very', 'when', 'come', 'here', 'just', 'like',
      'long', 'make', 'many', 'over', 'such', 'take', 'than', 'them', 'well', 'were'
    ]);
    return commonWords.has(word);
  }
}

// Default context manager instance
let defaultContextManager: ContextManager | null = null;

export function getContextManager(): ContextManager {
  if (!defaultContextManager) {
    defaultContextManager = new ContextManager();
  }
  return defaultContextManager;
}

export function setContextManager(manager: ContextManager): void {
  defaultContextManager = manager;
}
