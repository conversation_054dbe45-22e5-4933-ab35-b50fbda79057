import { getAIService, AIContext } from './AIServiceConnector';
import { getContextManager } from './ContextManager';
import type { Project, KnowledgeItem, Insight } from '../../types/project.types';
import type { IntelligentSuggestion } from '../../types/intelligence.types';

// Recommendation Types
export interface Recommendation {
  id: string;
  type: 'action' | 'insight' | 'connection' | 'resource' | 'question';
  title: string;
  description: string;
  confidence: number;
  priority: 'low' | 'medium' | 'high';
  category: string;
  metadata: {
    reasoning: string;
    relatedItems: string[];
    estimatedTime?: string;
    difficulty?: 'easy' | 'medium' | 'hard';
  };
  actions?: {
    primary?: {
      label: string;
      action: string;
      params?: Record<string, any>;
    };
    secondary?: {
      label: string;
      action: string;
      params?: Record<string, any>;
    };
  };
}

// Recommendation Strategies
export interface RecommendationStrategy {
  name: string;
  generateRecommendations(
    project: Project,
    context: AIContext,
    knowledgeItems: KnowledgeItem[],
    insights: Insight[]
  ): Promise<Recommendation[]>;
}

// Knowledge Gap Analysis Strategy
export class KnowledgeGapStrategy implements RecommendationStrategy {
  name = 'Knowledge Gap Analysis';

  async generateRecommendations(
    project: Project,
    context: AIContext,
    knowledgeItems: KnowledgeItem[],
    insights: Insight[]
  ): Promise<Recommendation[]> {
    const recommendations: Recommendation[] = [];
    const aiService = getAIService();

    // Analyze domain coverage
    const domainConcepts = project.intelligence.domainExpertise.concepts;
    const knowledgeConcepts = knowledgeItems.map(item => item.content);

    try {
      const gapAnalysis = await aiService.analyzeContent(
        `Domain concepts: ${domainConcepts.map(c => c.name).join(', ')}
         Current knowledge: ${knowledgeConcepts.join('; ')}`,
        'text'
      );

      // Generate gap-filling recommendations
      for (const concept of gapAnalysis.concepts) {
        if (!knowledgeConcepts.some(k => k.toLowerCase().includes(concept.toLowerCase()))) {
          recommendations.push({
            id: `gap-${concept}-${Date.now()}`,
            type: 'resource',
            title: `Explore ${concept}`,
            description: `Add knowledge about ${concept} to strengthen your understanding of ${project.domain}`,
            confidence: 0.7,
            priority: 'medium',
            category: 'Knowledge Gap',
            metadata: {
              reasoning: `${concept} is relevant to ${project.domain} but not well covered in current knowledge`,
              relatedItems: [],
              estimatedTime: '15-30 minutes',
              difficulty: 'medium',
            },
            actions: {
              primary: {
                label: 'Search Resources',
                action: 'search_resources',
                params: { query: concept, domain: project.domain },
              },
              secondary: {
                label: 'Add to Research List',
                action: 'add_to_research',
                params: { concept },
              },
            },
          });
        }
      }
    } catch (error) {
      console.error('Error in knowledge gap analysis:', error);
    }

    return recommendations;
  }
}

// Connection Discovery Strategy
export class ConnectionDiscoveryStrategy implements RecommendationStrategy {
  name = 'Connection Discovery';

  async generateRecommendations(
    project: Project,
    context: AIContext,
    knowledgeItems: KnowledgeItem[],
    insights: Insight[]
  ): Promise<Recommendation[]> {
    const recommendations: Recommendation[] = [];
    const aiService = getAIService();

    // Find potential connections between knowledge items
    for (let i = 0; i < knowledgeItems.length - 1; i++) {
      for (let j = i + 1; j < knowledgeItems.length; j++) {
        const item1 = knowledgeItems[i];
        const item2 = knowledgeItems[j];

        try {
          const connectionAnalysis = await aiService.generateResponse([
            {
              role: 'user',
              content: `Analyze potential connections between these two knowledge items:
                Item 1: ${item1.content}
                Item 2: ${item2.content}
                
                Return a JSON object with: { hasConnection: boolean, connectionType: string, strength: number, explanation: string }`
            }
          ], context);

          const analysis = JSON.parse(connectionAnalysis.content);
          
          if (analysis.hasConnection && analysis.strength > 0.6) {
            recommendations.push({
              id: `connection-${item1.id}-${item2.id}`,
              type: 'connection',
              title: `Connect: ${item1.type} ↔ ${item2.type}`,
              description: analysis.explanation,
              confidence: analysis.strength,
              priority: analysis.strength > 0.8 ? 'high' : 'medium',
              category: 'Knowledge Connection',
              metadata: {
                reasoning: `Strong ${analysis.connectionType} connection detected`,
                relatedItems: [item1.id, item2.id],
                estimatedTime: '5-10 minutes',
                difficulty: 'easy',
              },
              actions: {
                primary: {
                  label: 'Create Connection',
                  action: 'create_connection',
                  params: { item1: item1.id, item2: item2.id, type: analysis.connectionType },
                },
              },
            });
          }
        } catch (error) {
          console.error('Error analyzing connection:', error);
        }
      }
    }

    return recommendations.slice(0, 5); // Limit to top 5 connections
  }
}

// Next Action Strategy
export class NextActionStrategy implements RecommendationStrategy {
  name = 'Next Action Suggestions';

  async generateRecommendations(
    project: Project,
    context: AIContext,
    knowledgeItems: KnowledgeItem[],
    insights: Insight[]
  ): Promise<Recommendation[]> {
    const recommendations: Recommendation[] = [];
    const aiService = getAIService();

    try {
      const suggestions = await aiService.generateSuggestions(context);
      
      suggestions.forEach((suggestion, index) => {
        recommendations.push({
          id: `action-${Date.now()}-${index}`,
          type: 'action',
          title: suggestion,
          description: `Recommended next step based on your current project context`,
          confidence: 0.8,
          priority: index < 2 ? 'high' : 'medium',
          category: 'Next Action',
          metadata: {
            reasoning: 'AI-generated suggestion based on project context and recent activity',
            relatedItems: [],
            estimatedTime: '10-20 minutes',
            difficulty: 'medium',
          },
          actions: {
            primary: {
              label: 'Start Action',
              action: 'start_action',
              params: { suggestion },
            },
          },
        });
      });
    } catch (error) {
      console.error('Error generating next actions:', error);
    }

    return recommendations;
  }
}

// Insight Generation Strategy
export class InsightGenerationStrategy implements RecommendationStrategy {
  name = 'Insight Generation';

  async generateRecommendations(
    project: Project,
    context: AIContext,
    knowledgeItems: KnowledgeItem[],
    insights: Insight[]
  ): Promise<Recommendation[]> {
    const recommendations: Recommendation[] = [];
    const aiService = getAIService();

    // Look for patterns in recent insights
    const recentInsights = insights
      .filter(insight => insight.timestamp > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000))
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

    if (recentInsights.length >= 2) {
      try {
        const patternAnalysis = await aiService.generateResponse([
          {
            role: 'user',
            content: `Analyze these recent insights for patterns and generate new insights:
              ${recentInsights.map(i => i.content).join('\n')}
              
              Return a JSON array of potential new insights with format:
              [{ content: string, confidence: number, reasoning: string }]`
          }
        ], context);

        const newInsights = JSON.parse(patternAnalysis.content);
        
        newInsights.forEach((insight: any, index: number) => {
          recommendations.push({
            id: `insight-${Date.now()}-${index}`,
            type: 'insight',
            title: 'New Insight Opportunity',
            description: insight.content,
            confidence: insight.confidence,
            priority: insight.confidence > 0.8 ? 'high' : 'medium',
            category: 'Generated Insight',
            metadata: {
              reasoning: insight.reasoning,
              relatedItems: recentInsights.map(i => i.id),
              estimatedTime: '5 minutes',
              difficulty: 'easy',
            },
            actions: {
              primary: {
                label: 'Add Insight',
                action: 'add_insight',
                params: { content: insight.content, confidence: insight.confidence },
              },
            },
          });
        });
      } catch (error) {
        console.error('Error generating insights:', error);
      }
    }

    return recommendations;
  }
}

// Main Recommendation Engine
export class RecommendationEngine {
  private strategies: RecommendationStrategy[];
  private contextManager = getContextManager();

  constructor() {
    this.strategies = [
      new NextActionStrategy(),
      new KnowledgeGapStrategy(),
      new ConnectionDiscoveryStrategy(),
      new InsightGenerationStrategy(),
    ];
  }

  async generateRecommendations(
    project: Project,
    knowledgeItems: KnowledgeItem[] = [],
    insights: Insight[] = [],
    currentQuery?: string
  ): Promise<Recommendation[]> {
    const context = this.contextManager.buildContext(
      project,
      [], // No messages for recommendations
      knowledgeItems,
      insights,
      currentQuery
    );

    const allRecommendations: Recommendation[] = [];

    // Run all strategies
    for (const strategy of this.strategies) {
      try {
        const recommendations = await strategy.generateRecommendations(
          project,
          context,
          knowledgeItems,
          insights
        );
        allRecommendations.push(...recommendations);
      } catch (error) {
        console.error(`Error in strategy ${strategy.name}:`, error);
      }
    }

    // Sort by priority and confidence
    return this.rankRecommendations(allRecommendations);
  }

  private rankRecommendations(recommendations: Recommendation[]): Recommendation[] {
    return recommendations
      .sort((a, b) => {
        // First sort by priority
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
        if (priorityDiff !== 0) return priorityDiff;

        // Then by confidence
        return b.confidence - a.confidence;
      })
      .slice(0, 10); // Limit to top 10 recommendations
  }

  // Convert to IntelligentSuggestion format for store
  convertToSuggestions(recommendations: Recommendation[]): IntelligentSuggestion[] {
    return recommendations.map(rec => ({
      id: rec.id,
      type: rec.type as any,
      content: rec.description,
      confidence: rec.confidence,
      priority: rec.priority,
      category: rec.category,
      timestamp: new Date(),
      metadata: rec.metadata,
    }));
  }
}

// Default recommendation engine instance
let defaultRecommendationEngine: RecommendationEngine | null = null;

export function getRecommendationEngine(): RecommendationEngine {
  if (!defaultRecommendationEngine) {
    defaultRecommendationEngine = new RecommendationEngine();
  }
  return defaultRecommendationEngine;
}
