import { OpenAI } from 'openai';

// AI Service Configuration
export interface AIConfig {
  provider: 'openai' | 'anthropic' | 'local';
  apiKey?: string;
  baseURL?: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
}

// AI Message Types
export interface AIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
  timestamp?: Date;
}

export interface AIResponse {
  content: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model?: string;
  finishReason?: string;
}

// Context Management
export interface AIContext {
  projectId: string;
  sessionId: string;
  messages: AIMessage[];
  metadata: {
    domain: string;
    expertise: string[];
    currentFocus?: string;
    relevantKnowledge: string[];
  };
}

// AI Service Interface
export interface IAIService {
  generateResponse(messages: AIMessage[], context?: AIContext): Promise<AIResponse>;
  generateSuggestions(context: AIContext): Promise<string[]>;
  analyzeContent(content: string, type: 'document' | 'text' | 'code'): Promise<{
    summary: string;
    keyPoints: string[];
    concepts: string[];
    sentiment?: 'positive' | 'negative' | 'neutral';
  }>;
  embedText(text: string): Promise<number[]>;
}

// OpenAI Service Implementation
export class OpenAIService implements IAIService {
  private client: OpenAI;
  private config: AIConfig;

  constructor(config: AIConfig) {
    this.config = {
      model: 'gpt-4',
      temperature: 0.7,
      maxTokens: 2000,
      ...config,
    };

    this.client = new OpenAI({
      apiKey: config.apiKey || process.env.OPENAI_API_KEY,
      baseURL: config.baseURL,
      dangerouslyAllowBrowser: true, // For client-side usage
    });
  }

  async generateResponse(messages: AIMessage[], context?: AIContext): Promise<AIResponse> {
    try {
      const systemMessage: AIMessage = {
        role: 'system',
        content: this.buildSystemPrompt(context),
      };

      const completion = await this.client.chat.completions.create({
        model: this.config.model!,
        messages: [systemMessage, ...messages].map(msg => ({
          role: msg.role,
          content: msg.content,
        })),
        temperature: this.config.temperature,
        max_tokens: this.config.maxTokens,
      });

      const choice = completion.choices[0];
      return {
        content: choice.message?.content || '',
        usage: completion.usage ? {
          promptTokens: completion.usage.prompt_tokens,
          completionTokens: completion.usage.completion_tokens,
          totalTokens: completion.usage.total_tokens,
        } : undefined,
        model: completion.model,
        finishReason: choice.finish_reason || undefined,
      };
    } catch (error) {
      console.error('OpenAI API Error:', error);
      throw new Error(`AI service error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async generateSuggestions(context: AIContext): Promise<string[]> {
    const prompt = `Based on the current project context in ${context.metadata.domain}, 
    with expertise in ${context.metadata.expertise.join(', ')}, 
    and current focus on "${context.metadata.currentFocus || 'general exploration'}", 
    provide 5 intelligent suggestions for next actions or insights.
    
    Return only a JSON array of strings, no additional text.`;

    try {
      const response = await this.generateResponse([
        { role: 'user', content: prompt }
      ], context);

      const suggestions = JSON.parse(response.content);
      return Array.isArray(suggestions) ? suggestions : [];
    } catch (error) {
      console.error('Error generating suggestions:', error);
      return [
        'Explore related concepts in your domain',
        'Add more sources to expand knowledge',
        'Review recent insights for patterns',
        'Connect with domain experts',
        'Analyze knowledge gaps'
      ];
    }
  }

  async analyzeContent(content: string, type: 'document' | 'text' | 'code'): Promise<{
    summary: string;
    keyPoints: string[];
    concepts: string[];
    sentiment?: 'positive' | 'negative' | 'neutral';
  }> {
    const prompt = `Analyze the following ${type} content and provide:
    1. A concise summary (2-3 sentences)
    2. Key points (3-5 bullet points)
    3. Main concepts/topics identified
    4. Sentiment (if applicable)
    
    Content:
    ${content}
    
    Return as JSON with fields: summary, keyPoints, concepts, sentiment`;

    try {
      const response = await this.generateResponse([
        { role: 'user', content: prompt }
      ]);

      return JSON.parse(response.content);
    } catch (error) {
      console.error('Error analyzing content:', error);
      return {
        summary: 'Content analysis unavailable',
        keyPoints: ['Analysis failed'],
        concepts: ['unknown'],
        sentiment: 'neutral'
      };
    }
  }

  async embedText(text: string): Promise<number[]> {
    try {
      const response = await this.client.embeddings.create({
        model: 'text-embedding-ada-002',
        input: text,
      });

      return response.data[0].embedding;
    } catch (error) {
      console.error('Error generating embeddings:', error);
      return [];
    }
  }

  private buildSystemPrompt(context?: AIContext): string {
    if (!context) {
      return 'You are a helpful AI assistant for the Lighthouse knowledge management system.';
    }

    return `You are an intelligent assistant for the Lighthouse knowledge management system.

Project Context:
- Domain: ${context.metadata.domain}
- Expertise Areas: ${context.metadata.expertise.join(', ')}
- Current Focus: ${context.metadata.currentFocus || 'General exploration'}
- Relevant Knowledge: ${context.metadata.relevantKnowledge.join(', ')}

Your role is to:
1. Provide contextually relevant insights and recommendations
2. Help users discover connections between concepts
3. Suggest next steps for knowledge exploration
4. Maintain focus on the project domain while being helpful
5. Be concise but thorough in your responses

Always consider the user's expertise level and project context when responding.`;
  }
}

// AI Service Factory
export class AIServiceFactory {
  static create(config: AIConfig): IAIService {
    switch (config.provider) {
      case 'openai':
        return new OpenAIService(config);
      case 'anthropic':
        // TODO: Implement Anthropic service
        throw new Error('Anthropic service not yet implemented');
      case 'local':
        // TODO: Implement local LLM service
        throw new Error('Local LLM service not yet implemented');
      default:
        throw new Error(`Unsupported AI provider: ${config.provider}`);
    }
  }
}

// Default AI service instance
let defaultAIService: IAIService | null = null;

export function getAIService(): IAIService {
  if (!defaultAIService) {
    defaultAIService = AIServiceFactory.create({
      provider: 'openai',
      model: 'gpt-4',
      temperature: 0.7,
      maxTokens: 2000,
    });
  }
  return defaultAIService;
}

export function setAIService(service: IAIService): void {
  defaultAIService = service;
}
