// AI Service Connector
export {
  type AIConfig,
  type AIMessage,
  type AIR<PERSON>ponse,
  type AIContext,
  type IAIService,
  OpenAIService,
  AIServiceFactory,
  getAIService,
  setAIService,
} from './AIServiceConnector';

// Context Manager
export {
  type ContextWindow,
  type ContextItem,
  type RelevanceScorer,
  TFIDFRelevanceScorer,
  ContextManager,
  getContextManager,
  setContextManager,
} from './ContextManager';

// Recommendation Engine
export {
  type Recommendation,
  type RecommendationStrategy,
  KnowledgeGapStrategy,
  ConnectionDiscoveryStrategy,
  NextActionStrategy,
  InsightGenerationStrategy,
  RecommendationEngine,
  getRecommendationEngine,
} from './RecommendationEngine';

// React Hooks
export {
  useAI,
  useAIChat,
  useContentAnalysis,
  useRecommendations,
} from './useAI';

// AI Configuration
export const AI_CONFIG = {
  DEFAULT_MODEL: 'gpt-4',
  DEFAULT_TEMPERATURE: 0.7,
  DEFAULT_MAX_TOKENS: 2000,
  CONTEXT_WINDOW_SIZE: 4096,
  RELEVANCE_THRESHOLD: 0.3,
  MAX_RECOMMENDATIONS: 10,
} as const;

// AI Service Status
export enum AIServiceStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ERROR = 'error',
}

// AI Error Types
export class AIServiceError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'AIServiceError';
  }
}

export class ContextError extends Error {
  constructor(
    message: string,
    public contextId?: string
  ) {
    super(message);
    this.name = 'ContextError';
  }
}

export class RecommendationError extends Error {
  constructor(
    message: string,
    public strategy?: string
  ) {
    super(message);
    this.name = 'RecommendationError';
  }
}

// Utility Functions
export function validateAIConfig(config: Partial<AIConfig>): boolean {
  if (!config.provider) return false;
  
  if (config.provider === 'openai' && !config.apiKey && !process.env.OPENAI_API_KEY) {
    return false;
  }
  
  return true;
}

export function formatAIResponse(response: AIResponse): string {
  return response.content.trim();
}

export function estimateTokenCount(text: string): number {
  // Rough estimation: ~4 characters per token
  return Math.ceil(text.length / 4);
}

export function truncateToTokenLimit(text: string, maxTokens: number): string {
  const maxChars = maxTokens * 4;
  if (text.length <= maxChars) return text;
  
  return text.substring(0, maxChars - 3) + '...';
}

// AI Service Health Check
export async function checkAIServiceHealth(): Promise<{
  status: AIServiceStatus;
  latency?: number;
  error?: string;
}> {
  try {
    const startTime = Date.now();
    const aiService = getAIService();
    
    await aiService.generateResponse([
      { role: 'user', content: 'Hello' }
    ]);
    
    const latency = Date.now() - startTime;
    
    return {
      status: AIServiceStatus.CONNECTED,
      latency,
    };
  } catch (error) {
    return {
      status: AIServiceStatus.ERROR,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// Default AI initialization
export async function initializeAI(config?: Partial<AIConfig>): Promise<void> {
  try {
    if (config && validateAIConfig(config)) {
      const aiService = AIServiceFactory.create(config as AIConfig);
      setAIService(aiService);
    }
    
    // Test the service
    const health = await checkAIServiceHealth();
    if (health.status === AIServiceStatus.ERROR) {
      throw new AIServiceError(
        'Failed to initialize AI service',
        'INIT_ERROR',
        health.error
      );
    }
    
    console.log('AI service initialized successfully');
  } catch (error) {
    console.error('Failed to initialize AI service:', error);
    throw error;
  }
}
