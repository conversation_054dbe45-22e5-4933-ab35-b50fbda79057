import { useState, useCallback, useEffect } from 'react';
import { getAIService, getContextManager, getRecommendationEngine } from './index';
import { useLighthouseStore } from '../store/lighthouse-store';
import type { AIMessage, AIResponse } from './AIServiceConnector';
import type { Recommendation } from './RecommendationEngine';

// AI Hook State
interface AIState {
  isLoading: boolean;
  error: string | null;
  lastResponse: AIResponse | null;
  recommendations: Recommendation[];
  isGeneratingRecommendations: boolean;
}

// AI Hook Return Type
interface UseAIReturn {
  // State
  state: AIState;
  
  // Chat functions
  sendMessage: (message: string) => Promise<AIResponse | null>;
  analyzeContent: (content: string, type: 'document' | 'text' | 'code') => Promise<any>;
  
  // Recommendation functions
  generateRecommendations: () => Promise<Recommendation[]>;
  refreshRecommendations: () => Promise<void>;
  
  // Utility functions
  clearError: () => void;
  reset: () => void;
}

// Main AI Hook
export function useAI(): UseAIReturn {
  const [state, setState] = useState<AIState>({
    isLoading: false,
    error: null,
    lastResponse: null,
    recommendations: [],
    isGeneratingRecommendations: false,
  });

  const {
    currentProject,
    projectContext,
    insights,
    addSuggestion,
    recordLearning,
  } = useLighthouseStore();

  const aiService = getAIService();
  const contextManager = getContextManager();
  const recommendationEngine = getRecommendationEngine();

  // Send message to AI
  const sendMessage = useCallback(async (message: string): Promise<AIResponse | null> => {
    if (!currentProject) {
      setState(prev => ({ ...prev, error: 'No active project' }));
      return null;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Build context
      const context = contextManager.buildContext(
        currentProject,
        [{ role: 'user', content: message }],
        projectContext?.activeKnowledge || [],
        insights,
        message
      );

      // Generate response
      const response = await aiService.generateResponse(
        [{ role: 'user', content: message }],
        context
      );

      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        lastResponse: response 
      }));

      // Record learning event
      recordLearning({
        type: 'interaction',
        trigger: 'user_query',
        before: {
          confidence: 0.5,
          understanding: 'User query received',
          relatedNodes: [],
          supportingEvidence: []
        },
        after: {
          confidence: 0.8,
          understanding: 'AI response generated',
          relatedNodes: [],
          supportingEvidence: [message]
        }
      });

      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: errorMessage 
      }));
      return null;
    }
  }, [currentProject, projectContext, insights, aiService, contextManager, recordLearning]);

  // Analyze content
  const analyzeContent = useCallback(async (
    content: string, 
    type: 'document' | 'text' | 'code'
  ) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const analysis = await aiService.analyzeContent(content, type);
      
      setState(prev => ({ ...prev, isLoading: false }));

      // Record learning event
      recordLearning({
        type: 'analysis',
        trigger: 'content_analysis',
        before: {
          confidence: 0.3,
          understanding: 'Content received for analysis',
          relatedNodes: [],
          supportingEvidence: []
        },
        after: {
          confidence: 0.9,
          understanding: 'Content analyzed successfully',
          relatedNodes: [],
          supportingEvidence: [content.substring(0, 100)]
        }
      });

      return analysis;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Analysis failed';
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: errorMessage 
      }));
      return null;
    }
  }, [aiService, recordLearning]);

  // Generate recommendations
  const generateRecommendations = useCallback(async (): Promise<Recommendation[]> => {
    if (!currentProject) {
      setState(prev => ({ ...prev, error: 'No active project' }));
      return [];
    }

    setState(prev => ({ ...prev, isGeneratingRecommendations: true, error: null }));

    try {
      const recommendations = await recommendationEngine.generateRecommendations(
        currentProject,
        projectContext?.activeKnowledge || [],
        insights
      );

      setState(prev => ({ 
        ...prev, 
        isGeneratingRecommendations: false,
        recommendations 
      }));

      // Convert to suggestions and add to store
      const suggestions = recommendationEngine.convertToSuggestions(recommendations);
      suggestions.forEach(suggestion => addSuggestion(suggestion));

      return recommendations;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to generate recommendations';
      setState(prev => ({ 
        ...prev, 
        isGeneratingRecommendations: false, 
        error: errorMessage 
      }));
      return [];
    }
  }, [currentProject, projectContext, insights, recommendationEngine, addSuggestion]);

  // Refresh recommendations
  const refreshRecommendations = useCallback(async () => {
    await generateRecommendations();
  }, [generateRecommendations]);

  // Clear error
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Reset state
  const reset = useCallback(() => {
    setState({
      isLoading: false,
      error: null,
      lastResponse: null,
      recommendations: [],
      isGeneratingRecommendations: false,
    });
  }, []);

  // Auto-generate recommendations when project changes
  useEffect(() => {
    if (currentProject && projectContext) {
      generateRecommendations();
    }
  }, [currentProject?.id, projectContext?.activeKnowledge?.length]);

  return {
    state,
    sendMessage,
    analyzeContent,
    generateRecommendations,
    refreshRecommendations,
    clearError,
    reset,
  };
}

// Specialized hooks for specific use cases

// Chat Hook
export function useAIChat() {
  const { sendMessage, state, clearError } = useAI();
  const [messages, setMessages] = useState<AIMessage[]>([]);

  const sendChatMessage = useCallback(async (content: string) => {
    const userMessage: AIMessage = {
      role: 'user',
      content,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);

    const response = await sendMessage(content);
    
    if (response) {
      const assistantMessage: AIMessage = {
        role: 'assistant',
        content: response.content,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, assistantMessage]);
    }

    return response;
  }, [sendMessage]);

  const clearChat = useCallback(() => {
    setMessages([]);
    clearError();
  }, [clearError]);

  return {
    messages,
    sendMessage: sendChatMessage,
    clearChat,
    isLoading: state.isLoading,
    error: state.error,
    clearError,
  };
}

// Content Analysis Hook
export function useContentAnalysis() {
  const { analyzeContent, state, clearError } = useAI();
  const [analyses, setAnalyses] = useState<Array<{
    id: string;
    content: string;
    type: string;
    result: any;
    timestamp: Date;
  }>>([]);

  const analyzeAndStore = useCallback(async (
    content: string, 
    type: 'document' | 'text' | 'code'
  ) => {
    const result = await analyzeContent(content, type);
    
    if (result) {
      const analysis = {
        id: crypto.randomUUID(),
        content: content.substring(0, 100) + '...',
        type,
        result,
        timestamp: new Date(),
      };
      
      setAnalyses(prev => [analysis, ...prev.slice(0, 9)]); // Keep last 10
    }

    return result;
  }, [analyzeContent]);

  const clearAnalyses = useCallback(() => {
    setAnalyses([]);
    clearError();
  }, [clearError]);

  return {
    analyses,
    analyzeContent: analyzeAndStore,
    clearAnalyses,
    isLoading: state.isLoading,
    error: state.error,
    clearError,
  };
}

// Recommendations Hook
export function useRecommendations() {
  const { 
    generateRecommendations, 
    refreshRecommendations, 
    state, 
    clearError 
  } = useAI();

  return {
    recommendations: state.recommendations,
    generateRecommendations,
    refreshRecommendations,
    isLoading: state.isGeneratingRecommendations,
    error: state.error,
    clearError,
  };
}
