import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { cn } from '~/lib/ui-utils';
import { AccessibilityUtils } from '~/utils/accessibilityUtils';

interface AccessibleAnimationProps {
  children: React.ReactNode;
  animation?: 'fade' | 'slide' | 'scale' | 'rotate';
  duration?: number;
  delay?: number;
  className?: string;
}

interface SkipLinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
}

interface FocusTrapProps {
  children: React.ReactNode;
  active: boolean;
  className?: string;
}

interface ScreenReaderOnlyProps {
  children: React.ReactNode;
  className?: string;
}

interface AccessibleButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  ariaLabel?: string;
  ariaDescribedBy?: string;
  className?: string;
  variant?: 'primary' | 'secondary' | 'ghost';
}

// Accessible Animation Wrapper
export function AccessibleAnimation({
  children,
  animation = 'fade',
  duration = 0.3,
  delay = 0,
  className
}: AccessibleAnimationProps) {
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  const getAnimationVariants = () => {
    if (prefersReducedMotion) {
      return {
        initial: { opacity: 0 },
        animate: { opacity: 1 },
        exit: { opacity: 0 }
      };
    }

    switch (animation) {
      case 'slide':
        return {
          initial: { opacity: 0, y: 20 },
          animate: { opacity: 1, y: 0 },
          exit: { opacity: 0, y: -20 }
        };
      case 'scale':
        return {
          initial: { opacity: 0, scale: 0.9 },
          animate: { opacity: 1, scale: 1 },
          exit: { opacity: 0, scale: 0.9 }
        };
      case 'rotate':
        return {
          initial: { opacity: 0, rotate: -10 },
          animate: { opacity: 1, rotate: 0 },
          exit: { opacity: 0, rotate: 10 }
        };
      default:
        return {
          initial: { opacity: 0 },
          animate: { opacity: 1 },
          exit: { opacity: 0 }
        };
    }
  };

  const variants = getAnimationVariants();

  return (
    <motion.div
      className={className}
      variants={variants}
      initial="initial"
      animate="animate"
      exit="exit"
      transition={{
        duration: prefersReducedMotion ? 0.1 : duration,
        delay: prefersReducedMotion ? 0 : delay,
        ease: 'easeOut'
      }}
    >
      {children}
    </motion.div>
  );
}

// Skip Link for Keyboard Navigation
export function SkipLink({ href, children, className }: SkipLinkProps) {
  return (
    <a
      href={href}
      className={cn(
        'absolute left-4 top-4 z-50 rounded-md bg-primary px-4 py-2 text-primary-foreground',
        'transform -translate-y-16 transition-transform focus:translate-y-0',
        'focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2',
        'font-medium text-sm',
        className
      )}
    >
      {children}
    </a>
  );
}

// Focus Trap for Modals and Overlays
export function FocusTrap({ children, active, className }: FocusTrapProps) {
  const containerRef = React.useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!active || !containerRef.current) return;

    const container = containerRef.current;
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement?.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement?.focus();
        }
      }
    };

    const handleEscapeKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        // Allow parent to handle escape
        e.stopPropagation();
      }
    };

    container.addEventListener('keydown', handleTabKey);
    container.addEventListener('keydown', handleEscapeKey);
    
    // Focus first element when trap becomes active
    firstElement?.focus();

    return () => {
      container.removeEventListener('keydown', handleTabKey);
      container.removeEventListener('keydown', handleEscapeKey);
    };
  }, [active]);

  return (
    <div ref={containerRef} className={className}>
      {children}
    </div>
  );
}

// Screen Reader Only Content
export function ScreenReaderOnly({ children, className }: ScreenReaderOnlyProps) {
  return (
    <span
      className={cn(
        'sr-only absolute left-[-10000px] top-auto w-[1px] h-[1px] overflow-hidden',
        className
      )}
    >
      {children}
    </span>
  );
}

// Accessible Button with Enhanced States
export function AccessibleButton({
  children,
  onClick,
  disabled = false,
  ariaLabel,
  ariaDescribedBy,
  className,
  variant = 'primary'
}: AccessibleButtonProps) {
  const [isFocused, setIsFocused] = useState(false);
  const [isPressed, setIsPressed] = useState(false);
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  const getVariantClasses = () => {
    switch (variant) {
      case 'secondary':
        return 'bg-secondary text-secondary-foreground hover:bg-secondary/80';
      case 'ghost':
        return 'bg-transparent hover:bg-accent hover:text-accent-foreground';
      default:
        return 'bg-primary text-primary-foreground hover:bg-primary/90';
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      setIsPressed(true);
      onClick?.();
    }
  };

  const handleKeyUp = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      setIsPressed(false);
    }
  };

  return (
    <motion.button
      className={cn(
        'inline-flex items-center justify-center rounded-md px-4 py-2',
        'font-medium text-sm transition-all duration-200',
        'focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2',
        'disabled:opacity-50 disabled:pointer-events-none disabled:cursor-not-allowed',
        getVariantClasses(),
        isFocused && 'ring-2 ring-primary ring-offset-2',
        isPressed && 'scale-95',
        className
      )}
      onClick={onClick}
      onFocus={() => setIsFocused(true)}
      onBlur={() => setIsFocused(false)}
      onKeyDown={handleKeyDown}
      onKeyUp={handleKeyUp}
      onMouseDown={() => setIsPressed(true)}
      onMouseUp={() => setIsPressed(false)}
      onMouseLeave={() => setIsPressed(false)}
      disabled={disabled}
      aria-label={ariaLabel}
      aria-describedby={ariaDescribedBy}
      whileHover={!prefersReducedMotion ? { scale: 1.02 } : {}}
      whileTap={!prefersReducedMotion ? { scale: 0.98 } : {}}
      transition={{ duration: 0.1 }}
    >
      {children}
    </motion.button>
  );
}

// Accessible Loading State
export function AccessibleLoadingState({
  isLoading,
  children,
  loadingText = 'Loading...',
  className
}: {
  isLoading: boolean;
  children: React.ReactNode;
  loadingText?: string;
  className?: string;
}) {
  return (
    <div className={className}>
      {isLoading && (
        <ScreenReaderOnly>
          <div aria-live="polite" aria-atomic="true">
            {loadingText}
          </div>
        </ScreenReaderOnly>
      )}
      <div aria-hidden={isLoading}>
        {children}
      </div>
    </div>
  );
}

// Accessible Error Announcement
export function AccessibleErrorAnnouncement({
  error,
  className
}: {
  error: string | null;
  className?: string;
}) {
  return error ? (
    <div
      role="alert"
      aria-live="assertive"
      className={cn('sr-only', className)}
    >
      Error: {error}
    </div>
  ) : null;
}

// Accessible Success Announcement
export function AccessibleSuccessAnnouncement({
  message,
  className
}: {
  message: string | null;
  className?: string;
}) {
  return message ? (
    <div
      role="status"
      aria-live="polite"
      className={cn('sr-only', className)}
    >
      Success: {message}
    </div>
  ) : null;
}

// Performance Optimized Component Wrapper
export function PerformanceOptimized({
  children,
  shouldRender = true,
  fallback = null,
  className
}: {
  children: React.ReactNode;
  shouldRender?: boolean;
  fallback?: React.ReactNode;
  className?: string;
}) {
  const [isVisible, setIsVisible] = useState(false);
  const ref = React.useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!shouldRender) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [shouldRender]);

  if (!shouldRender) {
    return fallback ? <div className={className}>{fallback}</div> : null;
  }

  return (
    <div ref={ref} className={className}>
      {isVisible ? children : fallback}
    </div>
  );
}
