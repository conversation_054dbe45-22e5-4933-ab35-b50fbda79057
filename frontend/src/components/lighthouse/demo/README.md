# Lighthouse Dashboard Demo

This directory contains interactive demos for the Lighthouse Dashboard component, showcasing its features and capabilities in a live environment.

## Available Demos

### 1. DashboardDemo.tsx
A comprehensive live demo of the Dashboard component featuring:

- **Interactive Controls**: Start/stop live demo mode with customizable speed settings
- **Step-by-Step Progression**: Manual or automatic progression through different dashboard states
- **Realistic Data Simulation**: Simulates real AI project management scenarios including:
  - Learning events and knowledge acquisition
  - AI agent deployment and management
  - Intelligent insights and recommendations
  - Pattern recognition and analysis
  - Progress tracking and metrics

### 2. EnhancedUIDemo.tsx
A showcase of the enhanced UI components used throughout the Lighthouse system:

- Enhanced icons with multiple variants and states
- Progress indicators and metrics cards
- Responsive grid layouts
- State management components
- Accessibility features

## How to Access the Demo

### Option 1: Main Demos Page (Recommended)
Navigate to `/demos` in your browser and select the **"Dashboard Live"** tab to access the interactive dashboard demo integrated with other W-O-W demos.

### Option 2: Direct Route Access
Navigate to `/lighthouse-demo` in your browser to access the demo launcher with both demos.

### Option 2: Component Import
```tsx
// Import the demo launcher (recommended)
import { DemoLauncher } from '~/components/lighthouse';

function MyPage() {
  return <DemoLauncher />;
}

// Or import individual demos
import { DashboardDemo, EnhancedUIDemo } from '~/components/lighthouse';

function MyDashboardPage() {
  return <DashboardDemo />;
}

function MyUIPage() {
  return <EnhancedUIDemo />;
}
```

### Option 3: Embedded Demo
```tsx
// Embed just the Dashboard component with demo data
import { Dashboard } from '~/components/lighthouse';
import { useLighthouseStore } from '~/components/lighthouse';

function MyEmbeddedDemo() {
  const { initializeSession } = useLighthouseStore();

  React.useEffect(() => {
    initializeSession(); // This creates demo data
  }, []);

  return <Dashboard />;
}
```

## Demo Features

### Live Demo Mode
- **Auto-progression**: Automatically cycles through different dashboard states
- **Speed Control**: Adjustable timing (1s, 2s, 4s intervals)
- **Manual Control**: Click on progress indicators to jump to specific steps

### Demo Steps
1. **Initial State**: Basic project setup with empty metrics
2. **Learning Activity**: Simulates knowledge acquisition and learning events
3. **Agent Deployment**: Shows AI agents being deployed for research and analysis
4. **Smart Recommendations**: Generates intelligent suggestions based on project context
5. **Advanced Insights**: Complex pattern recognition and cross-domain insights

### Interactive Controls
- **Play/Pause Toggle**: Start or stop the live demo
- **Reset Button**: Return to initial state
- **Speed Settings**: Adjust auto-progression timing
- **Manual Steps**: Click to jump to any demo step

## Technical Implementation

### State Management
The demo uses the Lighthouse store (`useLighthouseStore`) to:
- Manage project state and context
- Simulate learning events and insights
- Handle agent deployment and status updates
- Generate intelligent recommendations

### Animation & Accessibility
- Respects `prefers-reduced-motion` settings
- Smooth transitions using Framer Motion
- Accessible controls with proper ARIA labels
- Keyboard navigation support

### Responsive Design
- Mobile-first responsive layout
- Adaptive grid systems
- Touch-friendly controls
- Optimized for various screen sizes

## Customization

### Adding New Demo Steps
To add new demo steps, extend the `demoSteps` array in `DashboardDemo.tsx`:

```tsx
const demoSteps = [
  // ... existing steps
  {
    name: 'Your New Step',
    description: 'Description of what this step demonstrates',
    action: () => {
      // Your demo logic here
      // e.g., addInsight(), recordLearning(), deployAgent()
    }
  }
];
```

### Modifying Demo Data
The demo uses realistic data structures defined in the Lighthouse store. You can modify:
- Project configurations
- Learning event types
- Agent capabilities
- Insight categories
- Recommendation algorithms

## Dependencies

The demo relies on:
- **React 18+**: For component rendering and hooks
- **Framer Motion**: For animations and transitions
- **Zustand**: For state management
- **Tailwind CSS**: For styling
- **Mantine**: For UI components
- **Custom UI Components**: From the `~/components/ui` directory

## Browser Support

The demo is optimized for modern browsers with support for:
- ES2020+ features
- CSS Grid and Flexbox
- Web Animations API
- Intersection Observer API

## Performance Considerations

- **Lazy Loading**: Components are loaded on demand
- **Animation Optimization**: Respects user motion preferences
- **Memory Management**: Proper cleanup of intervals and subscriptions
- **Efficient Rendering**: Optimized re-renders using React best practices

## Troubleshooting

### Common Issues

1. **Demo not starting**: Ensure the Lighthouse store is properly initialized
2. **Animations not working**: Check if `prefers-reduced-motion` is enabled
3. **Missing icons**: Verify all icon components are properly imported
4. **State not updating**: Check browser console for any JavaScript errors

### Debug Mode
Enable debug mode by setting `localStorage.setItem('lighthouse-debug', 'true')` in your browser console.

## Contributing

When adding new demo features:
1. Follow the existing code structure and patterns
2. Ensure accessibility compliance
3. Add appropriate TypeScript types
4. Test across different devices and browsers
5. Update this README with new features

## License

This demo is part of the Lighthouse project and follows the same licensing terms.
