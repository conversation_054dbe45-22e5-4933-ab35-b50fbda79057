import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Switch } from '~/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { Dashboard } from '../modules/dashboard/Dashboard';
import { useLighthouseStore } from '../shared/store/lighthouse-store';
import { LighthouseIcon } from '~/components/ui/icons/lighthouse';
import { PlayIcon } from '~/components/ui/icons/play';
import { PauseIcon } from '~/components/ui/icons/pause';
import { RefreshCWIcon } from '~/components/ui/icons/refresh-cw';
import { SettingsIcon } from '~/components/ui/icons/settings';

export function DashboardDemo() {
  const [isLiveDemo, setIsLiveDemo] = useState(false);
  const [demoSpeed, setDemoSpeed] = useState(2000); // milliseconds
  const [currentStep, setCurrentStep] = useState(0);
  
  const {
    currentProject,
    initializeSession,
    addInsight,
    recordLearning,
    deployAgent,
    updateAgentStatus,
    addSuggestion
  } = useLighthouseStore();

  // Demo scenarios
  const demoSteps = [
    {
      name: 'Initial State',
      description: 'Dashboard with basic project setup',
      action: () => {
        // Reset to initial state
        initializeSession();
      }
    },
    {
      name: 'Learning Activity',
      description: 'Simulate learning events and knowledge acquisition',
      action: () => {
        recordLearning({
          type: 'concept',
          trigger: 'user_interaction',
          before: { confidence: 0.5, understanding: 'Basic', relatedNodes: [], supportingEvidence: [] },
          after: { confidence: 0.8, understanding: 'Improved understanding of neural networks', relatedNodes: ['n1', 'n2'], supportingEvidence: ['research_paper_1'] }
        });
        
        addInsight({
          content: 'Neural networks show strong performance on image classification tasks',
          confidence: 0.85,
          sources: ['analysis'],
          connections: [],
          impact: 'high'
        });
      }
    },
    {
      name: 'Agent Deployment',
      description: 'Deploy AI agents for research and analysis',
      action: async () => {
        await deployAgent({
          name: 'Research Assistant',
          type: 'research',
          projectId: currentProject?.id || 'demo-project-1',
          context: {
            goal: 'Research machine learning papers and trends',
            constraints: ['academic sources only', 'recent publications'],
            knowledgeAccess: ['arxiv', 'google_scholar'],
            capabilities: ['search', 'analyze', 'summarize'],
            autonomyLevel: 7
          },
          results: undefined
        });
        
        await deployAgent({
          name: 'Data Analyzer',
          type: 'analysis',
          projectId: currentProject?.id || 'demo-project-1',
          context: {
            goal: 'Analyze patterns in knowledge graph data',
            constraints: ['pattern detection focus', 'knowledge graph scope'],
            knowledgeAccess: ['knowledge_graph'],
            capabilities: ['pattern_detection', 'data_analysis', 'visualization'],
            autonomyLevel: 6
          },
          results: undefined
        });
      }
    },
    {
      name: 'Smart Recommendations',
      description: 'Generate intelligent suggestions based on project context',
      action: () => {
        addSuggestion({
          id: crypto.randomUUID(),
          type: 'task',
          title: 'Explore Transfer Learning',
          description: 'Based on your interest in neural networks, consider exploring transfer learning techniques for faster model training.',
          rationale: 'User has shown interest in neural networks and deep learning concepts',
          confidence: 0.92,
          priority: 'high',
          evidence: ['neural_networks', 'deep_learning', 'model_training']
        });
        
        addSuggestion({
          id: crypto.randomUUID(),
          type: 'research',
          title: 'Add Computer Vision Dataset',
          description: 'Consider adding ImageNet or CIFAR-10 dataset to your knowledge base for practical examples.',
          rationale: 'Project focuses on machine learning with image classification patterns',
          confidence: 0.78,
          priority: 'medium',
          evidence: ['computer_vision', 'datasets', 'image_classification']
        });
      }
    },
    {
      name: 'Advanced Insights',
      description: 'Generate complex insights and pattern recognition',
      action: () => {
        addInsight({
          content: 'Strong correlation between attention mechanisms and model performance in NLP tasks',
          confidence: 0.91,
          sources: ['pattern_analysis'],
          connections: ['insight_1'],
          impact: 'high'
        });
        
        recordLearning({
          type: 'pattern',
          trigger: 'agent_analysis',
          before: { confidence: 0.6, understanding: 'Basic pattern recognition', relatedNodes: ['n1'], supportingEvidence: [] },
          after: { confidence: 0.88, understanding: 'Advanced pattern recognition with cross-domain insights', relatedNodes: ['n1', 'n2', 'n3'], supportingEvidence: ['analysis_1', 'research_paper_2'] }
        });
      }
    }
  ];

  // Auto-advance demo steps
  useEffect(() => {
    if (!isLiveDemo) return;
    
    const interval = setInterval(() => {
      setCurrentStep((prev) => {
        const nextStep = (prev + 1) % demoSteps.length;
        demoSteps[nextStep].action();
        return nextStep;
      });
    }, demoSpeed);

    return () => clearInterval(interval);
  }, [isLiveDemo, demoSpeed]);

  const handleManualStep = (stepIndex: number) => {
    setCurrentStep(stepIndex);
    demoSteps[stepIndex].action();
  };

  const resetDemo = () => {
    setCurrentStep(0);
    setIsLiveDemo(false);
    initializeSession();
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Demo Controls */}
      <div className="border-b bg-card/50 backdrop-blur-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <LighthouseIcon size={24} className="text-blue-600" />
                <h1 className="text-xl font-semibold">Dashboard Live Demo</h1>
              </div>
              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                Interactive Preview
              </Badge>
            </div>
            
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">Live Demo:</span>
                <Switch
                  checked={isLiveDemo}
                  onChange={setIsLiveDemo}
                />
                {isLiveDemo ? (
                  <PauseIcon size={16} className="text-orange-500" />
                ) : (
                  <PlayIcon size={16} className="text-green-500" />
                )}
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={resetDemo}
                className="gap-2"
              >
                <RefreshCWIcon size={14} />
                Reset
              </Button>
            </div>
          </div>
          
          {/* Demo Progress */}
          <div className="mt-4">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-sm font-medium">Demo Progress:</span>
              <span className="text-sm text-muted-foreground">
                Step {currentStep + 1} of {demoSteps.length}
              </span>
            </div>
            <div className="flex gap-1">
              {demoSteps.map((step, index) => (
                <button
                  key={index}
                  onClick={() => handleManualStep(index)}
                  className={`flex-1 h-2 rounded-full transition-colors ${
                    index <= currentStep 
                      ? 'bg-blue-500' 
                      : 'bg-gray-200 dark:bg-gray-700'
                  }`}
                  title={step.name}
                />
              ))}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {demoSteps[currentStep]?.description}
            </p>
          </div>
        </div>
      </div>

      {/* Demo Content */}
      <Tabs defaultValue="dashboard" className="max-w-7xl mx-auto">
        <TabsList className="grid w-full grid-cols-3 max-w-md mx-auto mt-6">
          <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
          <TabsTrigger value="controls">Controls</TabsTrigger>
          <TabsTrigger value="info">Info</TabsTrigger>
        </TabsList>
        
        <TabsContent value="dashboard" className="mt-6">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Dashboard />
          </motion.div>
        </TabsContent>
        
        <TabsContent value="controls" className="mt-6 px-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <SettingsIcon size={20} />
                Demo Controls
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h4 className="font-medium mb-3">Manual Steps</h4>
                <div className="grid gap-2">
                  {demoSteps.map((step, index) => (
                    <Button
                      key={index}
                      variant={index === currentStep ? "default" : "outline"}
                      onClick={() => handleManualStep(index)}
                      className="justify-start"
                    >
                      <span className="mr-2">{index + 1}.</span>
                      {step.name}
                    </Button>
                  ))}
                </div>
              </div>
              
              <div>
                <h4 className="font-medium mb-3">Demo Speed</h4>
                <div className="flex items-center gap-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setDemoSpeed(4000)}
                    disabled={demoSpeed === 4000}
                  >
                    Slow (4s)
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setDemoSpeed(2000)}
                    disabled={demoSpeed === 2000}
                  >
                    Normal (2s)
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setDemoSpeed(1000)}
                    disabled={demoSpeed === 1000}
                  >
                    Fast (1s)
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="info" className="mt-6 px-6">
          <Card>
            <CardHeader>
              <CardTitle>About This Demo</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-muted-foreground">
                This live demo showcases the Lighthouse Dashboard component with realistic data and interactions. 
                The demo simulates various states and activities that would occur in a real AI-powered project management environment.
              </p>
              
              <div>
                <h4 className="font-medium mb-2">Features Demonstrated:</h4>
                <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                  <li>Real-time metrics and progress tracking</li>
                  <li>AI agent deployment and management</li>
                  <li>Intelligent insights and recommendations</li>
                  <li>Learning event simulation</li>
                  <li>Responsive design and animations</li>
                  <li>Accessibility-compliant interactions</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Technologies Used:</h4>
                <div className="flex flex-wrap gap-2">
                  {['React', 'TypeScript', 'Framer Motion', 'Zustand', 'Tailwind CSS', 'Mantine'].map(tech => (
                    <Badge key={tech} variant="secondary">{tech}</Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
