import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '~/components/ui/tabs';
import { DashboardDemo } from './DashboardDemo';
import { EnhancedUIDemo } from './EnhancedUIDemo';
import { LighthouseIcon } from '~/components/ui/icons/lighthouse';
import { PlayIcon } from '~/components/ui/icons/play';
import { SparklesIcon } from '~/components/ui/icons/sparkles';
import { RocketIcon } from '~/components/ui/icons/rocket';
import { AccessibilityUtils } from '~/utils/accessibilityUtils';

export function DemoLauncher() {
  const [activeDemo, setActiveDemo] = useState<'dashboard' | 'ui' | null>(null);
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  if (activeDemo === 'dashboard') {
    return (
      <div className="min-h-screen">
        <div className="fixed top-4 left-4 z-50">
          <Button
            variant="outline"
            onClick={() => setActiveDemo(null)}
            className="bg-white/90 backdrop-blur-sm shadow-lg"
          >
            ← Back to Demos
          </Button>
        </div>
        <DashboardDemo />
      </div>
    );
  }

  if (activeDemo === 'ui') {
    return (
      <div className="min-h-screen">
        <div className="fixed top-4 left-4 z-50">
          <Button
            variant="outline"
            onClick={() => setActiveDemo(null)}
            className="bg-white/90 backdrop-blur-sm shadow-lg"
          >
            ← Back to Demos
          </Button>
        </div>
        <EnhancedUIDemo />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/20 to-purple-50/30 dark:from-gray-950 dark:via-blue-950/20 dark:to-purple-950/30">
      <div className="container mx-auto px-6 py-12">
        <motion.div
          initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, y: 20 }}
          animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, y: 0 }}
          transition={{ duration: prefersReducedMotion ? 0.1 : 0.6 }}
          className="text-center mb-12"
        >
          <div className="flex items-center justify-center gap-3 mb-4">
            <LighthouseIcon size={32} className="text-blue-600" />
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Lighthouse Demos
            </h1>
          </div>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Explore interactive demonstrations of the Lighthouse AI-powered project management platform
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {/* Dashboard Demo Card */}
          <motion.div
            initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, x: -20 }}
            animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, x: 0 }}
            transition={{ duration: prefersReducedMotion ? 0.1 : 0.6, delay: prefersReducedMotion ? 0 : 0.2 }}
          >
            <Card className="h-full border-0 shadow-lg hover:shadow-xl transition-shadow duration-300 bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <div className="flex items-center gap-3 mb-2">
                  <div className="p-2 rounded-lg bg-blue-50 text-blue-600 dark:bg-blue-950 dark:text-blue-400">
                    <RocketIcon size={24} />
                  </div>
                  <CardTitle className="text-xl">Dashboard Live Demo</CardTitle>
                </div>
                <div className="flex gap-2">
                  <Badge variant="secondary" className="bg-blue-50 text-blue-700 border-blue-200">
                    Interactive
                  </Badge>
                  <Badge variant="secondary" className="bg-green-50 text-green-700 border-green-200">
                    Real-time
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-muted-foreground">
                  Experience the full Dashboard component with live data simulation, AI agent deployment, 
                  and intelligent insights generation. Features auto-progression and manual controls.
                </p>
                
                <div className="space-y-2">
                  <h4 className="font-medium text-sm">Features:</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Live metrics and progress tracking</li>
                    <li>• AI agent simulation</li>
                    <li>• Smart recommendations</li>
                    <li>• Learning event progression</li>
                    <li>• Interactive controls</li>
                  </ul>
                </div>

                <Button 
                  onClick={() => setActiveDemo('dashboard')}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white gap-2"
                >
                  <PlayIcon size={16} />
                  Launch Dashboard Demo
                </Button>
              </CardContent>
            </Card>
          </motion.div>

          {/* UI Components Demo Card */}
          <motion.div
            initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, x: 20 }}
            animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, x: 0 }}
            transition={{ duration: prefersReducedMotion ? 0.1 : 0.6, delay: prefersReducedMotion ? 0 : 0.4 }}
          >
            <Card className="h-full border-0 shadow-lg hover:shadow-xl transition-shadow duration-300 bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <div className="flex items-center gap-3 mb-2">
                  <div className="p-2 rounded-lg bg-purple-50 text-purple-600 dark:bg-purple-950 dark:text-purple-400">
                    <SparklesIcon size={24} />
                  </div>
                  <CardTitle className="text-xl">Enhanced UI Components</CardTitle>
                </div>
                <div className="flex gap-2">
                  <Badge variant="secondary" className="bg-purple-50 text-purple-700 border-purple-200">
                    Components
                  </Badge>
                  <Badge variant="secondary" className="bg-orange-50 text-orange-700 border-orange-200">
                    Showcase
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-muted-foreground">
                  Explore the enhanced UI component library with interactive examples of icons, 
                  progress indicators, responsive grids, and state management components.
                </p>
                
                <div className="space-y-2">
                  <h4 className="font-medium text-sm">Components:</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Enhanced icons with animations</li>
                    <li>• Progress bars and indicators</li>
                    <li>• Responsive grid layouts</li>
                    <li>• State management demos</li>
                    <li>• Accessibility features</li>
                  </ul>
                </div>

                <Button 
                  onClick={() => setActiveDemo('ui')}
                  className="w-full bg-purple-600 hover:bg-purple-700 text-white gap-2"
                >
                  <SparklesIcon size={16} />
                  Explore UI Components
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Additional Info */}
        <motion.div
          initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, y: 20 }}
          animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, y: 0 }}
          transition={{ duration: prefersReducedMotion ? 0.1 : 0.6, delay: prefersReducedMotion ? 0 : 0.6 }}
          className="mt-12 text-center"
        >
          <Card className="max-w-2xl mx-auto border-0 shadow-lg bg-white/60 backdrop-blur-sm">
            <CardContent className="p-6">
              <h3 className="font-semibold mb-2">About These Demos</h3>
              <p className="text-sm text-muted-foreground">
                These interactive demos showcase the capabilities of the Lighthouse platform, 
                featuring modern UI components, accessibility compliance, and responsive design. 
                All animations respect user motion preferences and provide fallbacks for reduced motion settings.
              </p>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
