import React from 'react';

interface LighthouseIconProps {
  size?: number;
  className?: string;
}

export function LighthouseIcon({ size = 24, className }: LighthouseIconProps) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <path d="M8 2h8l-2 14H10L8 2z" />
      <path d="M6 16h12" />
      <path d="M7 20h10" />
      <path d="M12 2v4" />
      <path d="M9 6h6" />
      <circle cx="12" cy="8" r="1" />
    </svg>
  );
}

export default LighthouseIcon;
