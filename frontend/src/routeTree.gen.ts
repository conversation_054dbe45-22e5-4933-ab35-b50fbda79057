/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as WinsOfWeekImport } from './routes/wins-of-week'
import { Route as VendorManagementImport } from './routes/vendor-management'
import { Route as UnauthorizedImport } from './routes/unauthorized'
import { Route as TrainingNeedsAnalysisDemoImport } from './routes/training-needs-analysis-demo'
import { Route as TrainingNeedsAnalysisImport } from './routes/training-needs-analysis'
import { Route as TestIconsImport } from './routes/test-icons'
import { Route as TeamDashboardImport } from './routes/team-dashboard'
import { Route as RolesImport } from './routes/roles'
import { Route as RedirectImport } from './routes/redirect'
import { Route as ProfilecardFeaturesImport } from './routes/profilecard-features'
import { Route as LighthouseDemoImport } from './routes/lighthouse-demo'
import { Route as LighthouseImport } from './routes/lighthouse'
import { Route as IconsDemoImport } from './routes/icons-demo'
import { Route as DemosImport } from './routes/demos'
import { Route as DeferredImport } from './routes/deferred'
import { Route as AuthDemoImport } from './routes/auth-demo'
import { Route as AuthImport } from './routes/auth'
import { Route as AnimationDemoImport } from './routes/animation-demo'
import { Route as AccessBadgeImport } from './routes/access-badge'
import { Route as IndexImport } from './routes/index'
import { Route as RolesIndexImport } from './routes/roles/index'
import { Route as WinsOfWeekWinsImport } from './routes/wins-of-week/wins'
import { Route as WinsOfWeekAchievementListImport } from './routes/wins-of-week/achievement-list'
import { Route as RolesTrainersImport } from './routes/roles/trainers'
import { Route as RolesSeniorManagersImport } from './routes/roles/senior-managers'
import { Route as RolesManagersImport } from './routes/roles/managers'
import { Route as RolesExecutiveCoordinatorsImport } from './routes/roles/executive-coordinators'
import { Route as RolesDirectorsImport } from './routes/roles/directors'
import { Route as RolesCoordinatorsImport } from './routes/roles/coordinators'
import { Route as RolesCoachesImport } from './routes/roles/coaches'
import { Route as RolesAdminImport } from './routes/roles/admin'
import { Route as HomepageEgaAcademyImport } from './routes/homepage/ega-academy'

// Create/Update Routes

const WinsOfWeekRoute = WinsOfWeekImport.update({
  id: '/wins-of-week',
  path: '/wins-of-week',
  getParentRoute: () => rootRoute,
} as any)

const VendorManagementRoute = VendorManagementImport.update({
  id: '/vendor-management',
  path: '/vendor-management',
  getParentRoute: () => rootRoute,
} as any)

const UnauthorizedRoute = UnauthorizedImport.update({
  id: '/unauthorized',
  path: '/unauthorized',
  getParentRoute: () => rootRoute,
} as any)

const TrainingNeedsAnalysisDemoRoute = TrainingNeedsAnalysisDemoImport.update({
  id: '/training-needs-analysis-demo',
  path: '/training-needs-analysis-demo',
  getParentRoute: () => rootRoute,
} as any)

const TrainingNeedsAnalysisRoute = TrainingNeedsAnalysisImport.update({
  id: '/training-needs-analysis',
  path: '/training-needs-analysis',
  getParentRoute: () => rootRoute,
} as any)

const TestIconsRoute = TestIconsImport.update({
  id: '/test-icons',
  path: '/test-icons',
  getParentRoute: () => rootRoute,
} as any)

const TeamDashboardRoute = TeamDashboardImport.update({
  id: '/team-dashboard',
  path: '/team-dashboard',
  getParentRoute: () => rootRoute,
} as any)

const RolesRoute = RolesImport.update({
  id: '/roles',
  path: '/roles',
  getParentRoute: () => rootRoute,
} as any)

const RedirectRoute = RedirectImport.update({
  id: '/redirect',
  path: '/redirect',
  getParentRoute: () => rootRoute,
} as any)

const ProfilecardFeaturesRoute = ProfilecardFeaturesImport.update({
  id: '/profilecard-features',
  path: '/profilecard-features',
  getParentRoute: () => rootRoute,
} as any)

const LighthouseDemoRoute = LighthouseDemoImport.update({
  id: '/lighthouse-demo',
  path: '/lighthouse-demo',
  getParentRoute: () => rootRoute,
} as any)

const LighthouseRoute = LighthouseImport.update({
  id: '/lighthouse',
  path: '/lighthouse',
  getParentRoute: () => rootRoute,
} as any)

const IconsDemoRoute = IconsDemoImport.update({
  id: '/icons-demo',
  path: '/icons-demo',
  getParentRoute: () => rootRoute,
} as any)

const DemosRoute = DemosImport.update({
  id: '/demos',
  path: '/demos',
  getParentRoute: () => rootRoute,
} as any)

const DeferredRoute = DeferredImport.update({
  id: '/deferred',
  path: '/deferred',
  getParentRoute: () => rootRoute,
} as any)

const AuthDemoRoute = AuthDemoImport.update({
  id: '/auth-demo',
  path: '/auth-demo',
  getParentRoute: () => rootRoute,
} as any)

const AuthRoute = AuthImport.update({
  id: '/auth',
  path: '/auth',
  getParentRoute: () => rootRoute,
} as any)

const AnimationDemoRoute = AnimationDemoImport.update({
  id: '/animation-demo',
  path: '/animation-demo',
  getParentRoute: () => rootRoute,
} as any)

const AccessBadgeRoute = AccessBadgeImport.update({
  id: '/access-badge',
  path: '/access-badge',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const RolesIndexRoute = RolesIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => RolesRoute,
} as any)

const WinsOfWeekWinsRoute = WinsOfWeekWinsImport.update({
  id: '/wins',
  path: '/wins',
  getParentRoute: () => WinsOfWeekRoute,
} as any)

const WinsOfWeekAchievementListRoute = WinsOfWeekAchievementListImport.update({
  id: '/achievement-list',
  path: '/achievement-list',
  getParentRoute: () => WinsOfWeekRoute,
} as any)

const RolesTrainersRoute = RolesTrainersImport.update({
  id: '/trainers',
  path: '/trainers',
  getParentRoute: () => RolesRoute,
} as any)

const RolesSeniorManagersRoute = RolesSeniorManagersImport.update({
  id: '/senior-managers',
  path: '/senior-managers',
  getParentRoute: () => RolesRoute,
} as any)

const RolesManagersRoute = RolesManagersImport.update({
  id: '/managers',
  path: '/managers',
  getParentRoute: () => RolesRoute,
} as any)

const RolesExecutiveCoordinatorsRoute = RolesExecutiveCoordinatorsImport.update(
  {
    id: '/executive-coordinators',
    path: '/executive-coordinators',
    getParentRoute: () => RolesRoute,
  } as any,
)

const RolesDirectorsRoute = RolesDirectorsImport.update({
  id: '/directors',
  path: '/directors',
  getParentRoute: () => RolesRoute,
} as any)

const RolesCoordinatorsRoute = RolesCoordinatorsImport.update({
  id: '/coordinators',
  path: '/coordinators',
  getParentRoute: () => RolesRoute,
} as any)

const RolesCoachesRoute = RolesCoachesImport.update({
  id: '/coaches',
  path: '/coaches',
  getParentRoute: () => RolesRoute,
} as any)

const RolesAdminRoute = RolesAdminImport.update({
  id: '/admin',
  path: '/admin',
  getParentRoute: () => RolesRoute,
} as any)

const HomepageEgaAcademyRoute = HomepageEgaAcademyImport.update({
  id: '/homepage/ega-academy',
  path: '/homepage/ega-academy',
  getParentRoute: () => rootRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/access-badge': {
      id: '/access-badge'
      path: '/access-badge'
      fullPath: '/access-badge'
      preLoaderRoute: typeof AccessBadgeImport
      parentRoute: typeof rootRoute
    }
    '/animation-demo': {
      id: '/animation-demo'
      path: '/animation-demo'
      fullPath: '/animation-demo'
      preLoaderRoute: typeof AnimationDemoImport
      parentRoute: typeof rootRoute
    }
    '/auth': {
      id: '/auth'
      path: '/auth'
      fullPath: '/auth'
      preLoaderRoute: typeof AuthImport
      parentRoute: typeof rootRoute
    }
    '/auth-demo': {
      id: '/auth-demo'
      path: '/auth-demo'
      fullPath: '/auth-demo'
      preLoaderRoute: typeof AuthDemoImport
      parentRoute: typeof rootRoute
    }
    '/deferred': {
      id: '/deferred'
      path: '/deferred'
      fullPath: '/deferred'
      preLoaderRoute: typeof DeferredImport
      parentRoute: typeof rootRoute
    }
    '/demos': {
      id: '/demos'
      path: '/demos'
      fullPath: '/demos'
      preLoaderRoute: typeof DemosImport
      parentRoute: typeof rootRoute
    }
    '/icons-demo': {
      id: '/icons-demo'
      path: '/icons-demo'
      fullPath: '/icons-demo'
      preLoaderRoute: typeof IconsDemoImport
      parentRoute: typeof rootRoute
    }
    '/lighthouse': {
      id: '/lighthouse'
      path: '/lighthouse'
      fullPath: '/lighthouse'
      preLoaderRoute: typeof LighthouseImport
      parentRoute: typeof rootRoute
    }
    '/lighthouse-demo': {
      id: '/lighthouse-demo'
      path: '/lighthouse-demo'
      fullPath: '/lighthouse-demo'
      preLoaderRoute: typeof LighthouseDemoImport
      parentRoute: typeof rootRoute
    }
    '/profilecard-features': {
      id: '/profilecard-features'
      path: '/profilecard-features'
      fullPath: '/profilecard-features'
      preLoaderRoute: typeof ProfilecardFeaturesImport
      parentRoute: typeof rootRoute
    }
    '/redirect': {
      id: '/redirect'
      path: '/redirect'
      fullPath: '/redirect'
      preLoaderRoute: typeof RedirectImport
      parentRoute: typeof rootRoute
    }
    '/roles': {
      id: '/roles'
      path: '/roles'
      fullPath: '/roles'
      preLoaderRoute: typeof RolesImport
      parentRoute: typeof rootRoute
    }
    '/team-dashboard': {
      id: '/team-dashboard'
      path: '/team-dashboard'
      fullPath: '/team-dashboard'
      preLoaderRoute: typeof TeamDashboardImport
      parentRoute: typeof rootRoute
    }
    '/test-icons': {
      id: '/test-icons'
      path: '/test-icons'
      fullPath: '/test-icons'
      preLoaderRoute: typeof TestIconsImport
      parentRoute: typeof rootRoute
    }
    '/training-needs-analysis': {
      id: '/training-needs-analysis'
      path: '/training-needs-analysis'
      fullPath: '/training-needs-analysis'
      preLoaderRoute: typeof TrainingNeedsAnalysisImport
      parentRoute: typeof rootRoute
    }
    '/training-needs-analysis-demo': {
      id: '/training-needs-analysis-demo'
      path: '/training-needs-analysis-demo'
      fullPath: '/training-needs-analysis-demo'
      preLoaderRoute: typeof TrainingNeedsAnalysisDemoImport
      parentRoute: typeof rootRoute
    }
    '/unauthorized': {
      id: '/unauthorized'
      path: '/unauthorized'
      fullPath: '/unauthorized'
      preLoaderRoute: typeof UnauthorizedImport
      parentRoute: typeof rootRoute
    }
    '/vendor-management': {
      id: '/vendor-management'
      path: '/vendor-management'
      fullPath: '/vendor-management'
      preLoaderRoute: typeof VendorManagementImport
      parentRoute: typeof rootRoute
    }
    '/wins-of-week': {
      id: '/wins-of-week'
      path: '/wins-of-week'
      fullPath: '/wins-of-week'
      preLoaderRoute: typeof WinsOfWeekImport
      parentRoute: typeof rootRoute
    }
    '/homepage/ega-academy': {
      id: '/homepage/ega-academy'
      path: '/homepage/ega-academy'
      fullPath: '/homepage/ega-academy'
      preLoaderRoute: typeof HomepageEgaAcademyImport
      parentRoute: typeof rootRoute
    }
    '/roles/admin': {
      id: '/roles/admin'
      path: '/admin'
      fullPath: '/roles/admin'
      preLoaderRoute: typeof RolesAdminImport
      parentRoute: typeof RolesImport
    }
    '/roles/coaches': {
      id: '/roles/coaches'
      path: '/coaches'
      fullPath: '/roles/coaches'
      preLoaderRoute: typeof RolesCoachesImport
      parentRoute: typeof RolesImport
    }
    '/roles/coordinators': {
      id: '/roles/coordinators'
      path: '/coordinators'
      fullPath: '/roles/coordinators'
      preLoaderRoute: typeof RolesCoordinatorsImport
      parentRoute: typeof RolesImport
    }
    '/roles/directors': {
      id: '/roles/directors'
      path: '/directors'
      fullPath: '/roles/directors'
      preLoaderRoute: typeof RolesDirectorsImport
      parentRoute: typeof RolesImport
    }
    '/roles/executive-coordinators': {
      id: '/roles/executive-coordinators'
      path: '/executive-coordinators'
      fullPath: '/roles/executive-coordinators'
      preLoaderRoute: typeof RolesExecutiveCoordinatorsImport
      parentRoute: typeof RolesImport
    }
    '/roles/managers': {
      id: '/roles/managers'
      path: '/managers'
      fullPath: '/roles/managers'
      preLoaderRoute: typeof RolesManagersImport
      parentRoute: typeof RolesImport
    }
    '/roles/senior-managers': {
      id: '/roles/senior-managers'
      path: '/senior-managers'
      fullPath: '/roles/senior-managers'
      preLoaderRoute: typeof RolesSeniorManagersImport
      parentRoute: typeof RolesImport
    }
    '/roles/trainers': {
      id: '/roles/trainers'
      path: '/trainers'
      fullPath: '/roles/trainers'
      preLoaderRoute: typeof RolesTrainersImport
      parentRoute: typeof RolesImport
    }
    '/wins-of-week/achievement-list': {
      id: '/wins-of-week/achievement-list'
      path: '/achievement-list'
      fullPath: '/wins-of-week/achievement-list'
      preLoaderRoute: typeof WinsOfWeekAchievementListImport
      parentRoute: typeof WinsOfWeekImport
    }
    '/wins-of-week/wins': {
      id: '/wins-of-week/wins'
      path: '/wins'
      fullPath: '/wins-of-week/wins'
      preLoaderRoute: typeof WinsOfWeekWinsImport
      parentRoute: typeof WinsOfWeekImport
    }
    '/roles/': {
      id: '/roles/'
      path: '/'
      fullPath: '/roles/'
      preLoaderRoute: typeof RolesIndexImport
      parentRoute: typeof RolesImport
    }
  }
}

// Create and export the route tree

interface RolesRouteChildren {
  RolesAdminRoute: typeof RolesAdminRoute
  RolesCoachesRoute: typeof RolesCoachesRoute
  RolesCoordinatorsRoute: typeof RolesCoordinatorsRoute
  RolesDirectorsRoute: typeof RolesDirectorsRoute
  RolesExecutiveCoordinatorsRoute: typeof RolesExecutiveCoordinatorsRoute
  RolesManagersRoute: typeof RolesManagersRoute
  RolesSeniorManagersRoute: typeof RolesSeniorManagersRoute
  RolesTrainersRoute: typeof RolesTrainersRoute
  RolesIndexRoute: typeof RolesIndexRoute
}

const RolesRouteChildren: RolesRouteChildren = {
  RolesAdminRoute: RolesAdminRoute,
  RolesCoachesRoute: RolesCoachesRoute,
  RolesCoordinatorsRoute: RolesCoordinatorsRoute,
  RolesDirectorsRoute: RolesDirectorsRoute,
  RolesExecutiveCoordinatorsRoute: RolesExecutiveCoordinatorsRoute,
  RolesManagersRoute: RolesManagersRoute,
  RolesSeniorManagersRoute: RolesSeniorManagersRoute,
  RolesTrainersRoute: RolesTrainersRoute,
  RolesIndexRoute: RolesIndexRoute,
}

const RolesRouteWithChildren = RolesRoute._addFileChildren(RolesRouteChildren)

interface WinsOfWeekRouteChildren {
  WinsOfWeekAchievementListRoute: typeof WinsOfWeekAchievementListRoute
  WinsOfWeekWinsRoute: typeof WinsOfWeekWinsRoute
}

const WinsOfWeekRouteChildren: WinsOfWeekRouteChildren = {
  WinsOfWeekAchievementListRoute: WinsOfWeekAchievementListRoute,
  WinsOfWeekWinsRoute: WinsOfWeekWinsRoute,
}

const WinsOfWeekRouteWithChildren = WinsOfWeekRoute._addFileChildren(
  WinsOfWeekRouteChildren,
)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/access-badge': typeof AccessBadgeRoute
  '/animation-demo': typeof AnimationDemoRoute
  '/auth': typeof AuthRoute
  '/auth-demo': typeof AuthDemoRoute
  '/deferred': typeof DeferredRoute
  '/demos': typeof DemosRoute
  '/icons-demo': typeof IconsDemoRoute
  '/lighthouse': typeof LighthouseRoute
  '/lighthouse-demo': typeof LighthouseDemoRoute
  '/profilecard-features': typeof ProfilecardFeaturesRoute
  '/redirect': typeof RedirectRoute
  '/roles': typeof RolesRouteWithChildren
  '/team-dashboard': typeof TeamDashboardRoute
  '/test-icons': typeof TestIconsRoute
  '/training-needs-analysis': typeof TrainingNeedsAnalysisRoute
  '/training-needs-analysis-demo': typeof TrainingNeedsAnalysisDemoRoute
  '/unauthorized': typeof UnauthorizedRoute
  '/vendor-management': typeof VendorManagementRoute
  '/wins-of-week': typeof WinsOfWeekRouteWithChildren
  '/homepage/ega-academy': typeof HomepageEgaAcademyRoute
  '/roles/admin': typeof RolesAdminRoute
  '/roles/coaches': typeof RolesCoachesRoute
  '/roles/coordinators': typeof RolesCoordinatorsRoute
  '/roles/directors': typeof RolesDirectorsRoute
  '/roles/executive-coordinators': typeof RolesExecutiveCoordinatorsRoute
  '/roles/managers': typeof RolesManagersRoute
  '/roles/senior-managers': typeof RolesSeniorManagersRoute
  '/roles/trainers': typeof RolesTrainersRoute
  '/wins-of-week/achievement-list': typeof WinsOfWeekAchievementListRoute
  '/wins-of-week/wins': typeof WinsOfWeekWinsRoute
  '/roles/': typeof RolesIndexRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/access-badge': typeof AccessBadgeRoute
  '/animation-demo': typeof AnimationDemoRoute
  '/auth': typeof AuthRoute
  '/auth-demo': typeof AuthDemoRoute
  '/deferred': typeof DeferredRoute
  '/demos': typeof DemosRoute
  '/icons-demo': typeof IconsDemoRoute
  '/lighthouse': typeof LighthouseRoute
  '/lighthouse-demo': typeof LighthouseDemoRoute
  '/profilecard-features': typeof ProfilecardFeaturesRoute
  '/redirect': typeof RedirectRoute
  '/team-dashboard': typeof TeamDashboardRoute
  '/test-icons': typeof TestIconsRoute
  '/training-needs-analysis': typeof TrainingNeedsAnalysisRoute
  '/training-needs-analysis-demo': typeof TrainingNeedsAnalysisDemoRoute
  '/unauthorized': typeof UnauthorizedRoute
  '/vendor-management': typeof VendorManagementRoute
  '/wins-of-week': typeof WinsOfWeekRouteWithChildren
  '/homepage/ega-academy': typeof HomepageEgaAcademyRoute
  '/roles/admin': typeof RolesAdminRoute
  '/roles/coaches': typeof RolesCoachesRoute
  '/roles/coordinators': typeof RolesCoordinatorsRoute
  '/roles/directors': typeof RolesDirectorsRoute
  '/roles/executive-coordinators': typeof RolesExecutiveCoordinatorsRoute
  '/roles/managers': typeof RolesManagersRoute
  '/roles/senior-managers': typeof RolesSeniorManagersRoute
  '/roles/trainers': typeof RolesTrainersRoute
  '/wins-of-week/achievement-list': typeof WinsOfWeekAchievementListRoute
  '/wins-of-week/wins': typeof WinsOfWeekWinsRoute
  '/roles': typeof RolesIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/access-badge': typeof AccessBadgeRoute
  '/animation-demo': typeof AnimationDemoRoute
  '/auth': typeof AuthRoute
  '/auth-demo': typeof AuthDemoRoute
  '/deferred': typeof DeferredRoute
  '/demos': typeof DemosRoute
  '/icons-demo': typeof IconsDemoRoute
  '/lighthouse': typeof LighthouseRoute
  '/lighthouse-demo': typeof LighthouseDemoRoute
  '/profilecard-features': typeof ProfilecardFeaturesRoute
  '/redirect': typeof RedirectRoute
  '/roles': typeof RolesRouteWithChildren
  '/team-dashboard': typeof TeamDashboardRoute
  '/test-icons': typeof TestIconsRoute
  '/training-needs-analysis': typeof TrainingNeedsAnalysisRoute
  '/training-needs-analysis-demo': typeof TrainingNeedsAnalysisDemoRoute
  '/unauthorized': typeof UnauthorizedRoute
  '/vendor-management': typeof VendorManagementRoute
  '/wins-of-week': typeof WinsOfWeekRouteWithChildren
  '/homepage/ega-academy': typeof HomepageEgaAcademyRoute
  '/roles/admin': typeof RolesAdminRoute
  '/roles/coaches': typeof RolesCoachesRoute
  '/roles/coordinators': typeof RolesCoordinatorsRoute
  '/roles/directors': typeof RolesDirectorsRoute
  '/roles/executive-coordinators': typeof RolesExecutiveCoordinatorsRoute
  '/roles/managers': typeof RolesManagersRoute
  '/roles/senior-managers': typeof RolesSeniorManagersRoute
  '/roles/trainers': typeof RolesTrainersRoute
  '/wins-of-week/achievement-list': typeof WinsOfWeekAchievementListRoute
  '/wins-of-week/wins': typeof WinsOfWeekWinsRoute
  '/roles/': typeof RolesIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/access-badge'
    | '/animation-demo'
    | '/auth'
    | '/auth-demo'
    | '/deferred'
    | '/demos'
    | '/icons-demo'
    | '/lighthouse'
    | '/lighthouse-demo'
    | '/profilecard-features'
    | '/redirect'
    | '/roles'
    | '/team-dashboard'
    | '/test-icons'
    | '/training-needs-analysis'
    | '/training-needs-analysis-demo'
    | '/unauthorized'
    | '/vendor-management'
    | '/wins-of-week'
    | '/homepage/ega-academy'
    | '/roles/admin'
    | '/roles/coaches'
    | '/roles/coordinators'
    | '/roles/directors'
    | '/roles/executive-coordinators'
    | '/roles/managers'
    | '/roles/senior-managers'
    | '/roles/trainers'
    | '/wins-of-week/achievement-list'
    | '/wins-of-week/wins'
    | '/roles/'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/access-badge'
    | '/animation-demo'
    | '/auth'
    | '/auth-demo'
    | '/deferred'
    | '/demos'
    | '/icons-demo'
    | '/lighthouse'
    | '/lighthouse-demo'
    | '/profilecard-features'
    | '/redirect'
    | '/team-dashboard'
    | '/test-icons'
    | '/training-needs-analysis'
    | '/training-needs-analysis-demo'
    | '/unauthorized'
    | '/vendor-management'
    | '/wins-of-week'
    | '/homepage/ega-academy'
    | '/roles/admin'
    | '/roles/coaches'
    | '/roles/coordinators'
    | '/roles/directors'
    | '/roles/executive-coordinators'
    | '/roles/managers'
    | '/roles/senior-managers'
    | '/roles/trainers'
    | '/wins-of-week/achievement-list'
    | '/wins-of-week/wins'
    | '/roles'
  id:
    | '__root__'
    | '/'
    | '/access-badge'
    | '/animation-demo'
    | '/auth'
    | '/auth-demo'
    | '/deferred'
    | '/demos'
    | '/icons-demo'
    | '/lighthouse'
    | '/lighthouse-demo'
    | '/profilecard-features'
    | '/redirect'
    | '/roles'
    | '/team-dashboard'
    | '/test-icons'
    | '/training-needs-analysis'
    | '/training-needs-analysis-demo'
    | '/unauthorized'
    | '/vendor-management'
    | '/wins-of-week'
    | '/homepage/ega-academy'
    | '/roles/admin'
    | '/roles/coaches'
    | '/roles/coordinators'
    | '/roles/directors'
    | '/roles/executive-coordinators'
    | '/roles/managers'
    | '/roles/senior-managers'
    | '/roles/trainers'
    | '/wins-of-week/achievement-list'
    | '/wins-of-week/wins'
    | '/roles/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AccessBadgeRoute: typeof AccessBadgeRoute
  AnimationDemoRoute: typeof AnimationDemoRoute
  AuthRoute: typeof AuthRoute
  AuthDemoRoute: typeof AuthDemoRoute
  DeferredRoute: typeof DeferredRoute
  DemosRoute: typeof DemosRoute
  IconsDemoRoute: typeof IconsDemoRoute
  LighthouseRoute: typeof LighthouseRoute
  LighthouseDemoRoute: typeof LighthouseDemoRoute
  ProfilecardFeaturesRoute: typeof ProfilecardFeaturesRoute
  RedirectRoute: typeof RedirectRoute
  RolesRoute: typeof RolesRouteWithChildren
  TeamDashboardRoute: typeof TeamDashboardRoute
  TestIconsRoute: typeof TestIconsRoute
  TrainingNeedsAnalysisRoute: typeof TrainingNeedsAnalysisRoute
  TrainingNeedsAnalysisDemoRoute: typeof TrainingNeedsAnalysisDemoRoute
  UnauthorizedRoute: typeof UnauthorizedRoute
  VendorManagementRoute: typeof VendorManagementRoute
  WinsOfWeekRoute: typeof WinsOfWeekRouteWithChildren
  HomepageEgaAcademyRoute: typeof HomepageEgaAcademyRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AccessBadgeRoute: AccessBadgeRoute,
  AnimationDemoRoute: AnimationDemoRoute,
  AuthRoute: AuthRoute,
  AuthDemoRoute: AuthDemoRoute,
  DeferredRoute: DeferredRoute,
  DemosRoute: DemosRoute,
  IconsDemoRoute: IconsDemoRoute,
  LighthouseRoute: LighthouseRoute,
  LighthouseDemoRoute: LighthouseDemoRoute,
  ProfilecardFeaturesRoute: ProfilecardFeaturesRoute,
  RedirectRoute: RedirectRoute,
  RolesRoute: RolesRouteWithChildren,
  TeamDashboardRoute: TeamDashboardRoute,
  TestIconsRoute: TestIconsRoute,
  TrainingNeedsAnalysisRoute: TrainingNeedsAnalysisRoute,
  TrainingNeedsAnalysisDemoRoute: TrainingNeedsAnalysisDemoRoute,
  UnauthorizedRoute: UnauthorizedRoute,
  VendorManagementRoute: VendorManagementRoute,
  WinsOfWeekRoute: WinsOfWeekRouteWithChildren,
  HomepageEgaAcademyRoute: HomepageEgaAcademyRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/access-badge",
        "/animation-demo",
        "/auth",
        "/auth-demo",
        "/deferred",
        "/demos",
        "/icons-demo",
        "/lighthouse",
        "/lighthouse-demo",
        "/profilecard-features",
        "/redirect",
        "/roles",
        "/team-dashboard",
        "/test-icons",
        "/training-needs-analysis",
        "/training-needs-analysis-demo",
        "/unauthorized",
        "/vendor-management",
        "/wins-of-week",
        "/homepage/ega-academy"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/access-badge": {
      "filePath": "access-badge.tsx"
    },
    "/animation-demo": {
      "filePath": "animation-demo.tsx"
    },
    "/auth": {
      "filePath": "auth.tsx"
    },
    "/auth-demo": {
      "filePath": "auth-demo.tsx"
    },
    "/deferred": {
      "filePath": "deferred.tsx"
    },
    "/demos": {
      "filePath": "demos.tsx"
    },
    "/icons-demo": {
      "filePath": "icons-demo.tsx"
    },
    "/lighthouse": {
      "filePath": "lighthouse.tsx"
    },
    "/lighthouse-demo": {
      "filePath": "lighthouse-demo.tsx"
    },
    "/profilecard-features": {
      "filePath": "profilecard-features.tsx"
    },
    "/redirect": {
      "filePath": "redirect.tsx"
    },
    "/roles": {
      "filePath": "roles.tsx",
      "children": [
        "/roles/admin",
        "/roles/coaches",
        "/roles/coordinators",
        "/roles/directors",
        "/roles/executive-coordinators",
        "/roles/managers",
        "/roles/senior-managers",
        "/roles/trainers",
        "/roles/"
      ]
    },
    "/team-dashboard": {
      "filePath": "team-dashboard.tsx"
    },
    "/test-icons": {
      "filePath": "test-icons.tsx"
    },
    "/training-needs-analysis": {
      "filePath": "training-needs-analysis.tsx"
    },
    "/training-needs-analysis-demo": {
      "filePath": "training-needs-analysis-demo.tsx"
    },
    "/unauthorized": {
      "filePath": "unauthorized.tsx"
    },
    "/vendor-management": {
      "filePath": "vendor-management.tsx"
    },
    "/wins-of-week": {
      "filePath": "wins-of-week.tsx",
      "children": [
        "/wins-of-week/achievement-list",
        "/wins-of-week/wins"
      ]
    },
    "/homepage/ega-academy": {
      "filePath": "homepage/ega-academy.tsx"
    },
    "/roles/admin": {
      "filePath": "roles/admin.tsx",
      "parent": "/roles"
    },
    "/roles/coaches": {
      "filePath": "roles/coaches.tsx",
      "parent": "/roles"
    },
    "/roles/coordinators": {
      "filePath": "roles/coordinators.tsx",
      "parent": "/roles"
    },
    "/roles/directors": {
      "filePath": "roles/directors.tsx",
      "parent": "/roles"
    },
    "/roles/executive-coordinators": {
      "filePath": "roles/executive-coordinators.tsx",
      "parent": "/roles"
    },
    "/roles/managers": {
      "filePath": "roles/managers.tsx",
      "parent": "/roles"
    },
    "/roles/senior-managers": {
      "filePath": "roles/senior-managers.tsx",
      "parent": "/roles"
    },
    "/roles/trainers": {
      "filePath": "roles/trainers.tsx",
      "parent": "/roles"
    },
    "/wins-of-week/achievement-list": {
      "filePath": "wins-of-week/achievement-list.tsx",
      "parent": "/wins-of-week"
    },
    "/wins-of-week/wins": {
      "filePath": "wins-of-week/wins.tsx",
      "parent": "/wins-of-week"
    },
    "/roles/": {
      "filePath": "roles/index.tsx",
      "parent": "/roles"
    }
  }
}
ROUTE_MANIFEST_END */
