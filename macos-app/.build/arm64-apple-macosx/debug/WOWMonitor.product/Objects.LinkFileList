/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/AFError.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/Alamofire.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/AlamofireExtended.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/AuthenticationInterceptor.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/CachedResponseHandler.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/Combine.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/Concurrency.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/DataRequest.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/DataStreamRequest.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/DispatchQueue+Alamofire.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/DownloadRequest.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/EventMonitor.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/HTTPHeaders.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/HTTPMethod.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/MultipartFormData.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/MultipartUpload.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/NetworkReachabilityManager.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/Notifications.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/OperationQueue+Alamofire.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/ParameterEncoder.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/ParameterEncoding.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/Protected.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/RedirectHandler.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/Request.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/RequestCompression.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/RequestInterceptor.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/RequestTaskMap.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/Response.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/ResponseSerialization.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/Result+Alamofire.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/RetryPolicy.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/ServerTrustEvaluation.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/Session.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/SessionDelegate.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/StringEncoding+Alamofire.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/URLConvertible+URLRequestConvertible.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/URLEncodedFormEncoder.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/URLRequest+Alamofire.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/URLSessionConfiguration+Alamofire.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/UploadRequest.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/Validation.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/WebSocketRequest.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/resource_bundle_accessor.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/AES-GCM.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/AES-GCM_boring.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/AES.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/AESWrap.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/AESWrap_boring.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/ASN1.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/ASN1Any.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/ASN1BitString.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/ASN1Boolean.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/ASN1Identifier.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/ASN1Integer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/ASN1Null.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/ASN1OctetString.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/ASN1Strings.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/ArraySliceBigint.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/ChaChaPoly.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/ChaChaPoly_boring.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/Cipher.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/CryptoKitErrors.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/CryptoKitErrors_boring.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/Curve25519.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/DH.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/DHKEM.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/Digest.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/Digest_boring.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/Digests.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/ECDH.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/ECDH_boring.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/ECDSA.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/ECDSASignature.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/ECDSASignature_boring.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/ECDSA_boring.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/Ed25519.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/Ed25519Keys.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/Ed25519_boring.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/EdDSA_boring.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/GeneralizedTime.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/HKDF.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/HMAC.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/HPKE-AEAD.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/HPKE-Ciphersuite.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/HPKE-Context.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/HPKE-Errors.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/HPKE-KDF.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/HPKE-KEM-Curve25519.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/HPKE-KEM.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/HPKE-KexKeyDerivation.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/HPKE-KeySchedule.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/HPKE-LabeledExtract.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/HPKE-Modes.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/HPKE-NIST-EC-KEMs.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/HPKE-Utils.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/HPKE.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/HashFunctions.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/HashFunctions_SHA2.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/Insecure.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/Insecure_HashFunctions.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/KEM.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/MACFunctions.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/MessageAuthenticationCode.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/NISTCurvesKeys.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/NISTCurvesKeys_boring.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/Nonces.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/ObjectIdentifier.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/PEMDocument.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/PKCS8PrivateKey.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/PrettyBytes.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/RNG_boring.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/SEC1PrivateKey.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/SafeCompare.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/SafeCompare_boring.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/SecureBytes.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/Signature.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/SubjectPublicKeyInfo.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/SymmetricKeys.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/X25519Keys.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/X25519Keys_boring.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/Zeroization.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/Zeroization_boring.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Crypto.build/resource_bundle_accessor.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/AnimatedMoveViewJob.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/AnimatedViewPortJob.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/AnimatedZoomViewJob.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/Animator.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/AxisBase.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/AxisRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/AxisValueFormatter.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/BarChartData.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/BarChartDataEntry.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/BarChartDataProvider.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/BarChartDataSet.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/BarChartDataSetProtocol.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/BarChartRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/BarChartView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/BarHighlighter.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/BarLineChartViewBase.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/BarLineScatterCandleBubbleChartData.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/BarLineScatterCandleBubbleChartDataProvider.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/BarLineScatterCandleBubbleChartDataSet.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/BarLineScatterCandleBubbleChartDataSetProtocol.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/BarLineScatterCandleBubbleRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/BubbleChartData.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/BubbleChartDataEntry.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/BubbleChartDataProvider.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/BubbleChartDataSet.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/BubbleChartDataSetProtocol.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/BubbleChartRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/BubbleChartView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/CandleChartData.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/CandleChartDataEntry.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/CandleChartDataProvider.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/CandleChartDataSet.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/CandleChartDataSetProtocol.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/CandleStickChartRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/CandleStickChartView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/ChartAnimationEasing.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/ChartBaseDataSet.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/ChartColorTemplates.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/ChartData.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/ChartDataEntry.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/ChartDataEntryBase.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/ChartDataProvider.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/ChartDataSet.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/ChartDataSetProtocol.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/ChartHighlighter.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/ChartLimitLine.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/ChartUtils.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/ChartViewBase.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/ChevronDownShapeRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/ChevronUpShapeRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/CircleShapeRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/CombinedChartData.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/CombinedChartDataProvider.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/CombinedChartRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/CombinedChartView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/CombinedHighlighter.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/ComponentBase.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/CrossShapeRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/DataApproximator+N.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/DataApproximator.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/DataRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/DefaultAxisValueFormatter.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/DefaultFillFormatter.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/DefaultValueFormatter.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/Description.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/Fill.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/FillFormatter.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/Highlight.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/Highlighter.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/HorizontalBarChartRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/HorizontalBarChartView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/HorizontalBarHighlighter.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/IndexAxisValueFormatter.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/Indexed.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/Legend.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/LegendEntry.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/LegendRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/LineChartData.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/LineChartDataProvider.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/LineChartDataSet.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/LineChartDataSetProtocol.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/LineChartRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/LineChartView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/LineRadarChartDataSet.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/LineRadarChartDataSetProtocol.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/LineRadarRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/LineScatterCandleRadarChartDataSet.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/LineScatterCandleRadarChartDataSetProtocol.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/LineScatterCandleRadarRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/Marker.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/MarkerImage.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/MarkerView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/MoveViewJob.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/Partition.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/PieChartData.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/PieChartDataEntry.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/PieChartDataSet.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/PieChartDataSetProtocol.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/PieChartRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/PieChartView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/PieHighlighter.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/PieRadarChartViewBase.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/PieRadarHighlighter.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/Platform+Accessibility.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/Platform+Color.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/Platform+Gestures.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/Platform+Graphics.swift.o
'/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/Platform+Touch Handling.swift.o'
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/Platform.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/RadarChartData.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/RadarChartDataEntry.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/RadarChartDataSet.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/RadarChartDataSetProtocol.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/RadarChartRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/RadarChartView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/RadarHighlighter.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/Range.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/Renderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/ScatterChartData.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/ScatterChartDataProvider.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/ScatterChartDataSet.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/ScatterChartDataSetProtocol.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/ScatterChartRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/ScatterChartView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/Sequence+KeyPath.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/ShapeRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/SquareShapeRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/Transformer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/TransformerHorizontalBarChart.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/TriangleShapeRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/ValueFormatter.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/ViewPortHandler.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/ViewPortJob.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/XAxis.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/XAxisRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/XAxisRendererHorizontalBarChart.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/XAxisRendererRadarChart.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/XShapeRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/YAxis.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/YAxisRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/YAxisRendererHorizontalBarChart.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/YAxisRendererRadarChart.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/ZoomViewJob.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/DGCharts.build/resource_bundle_accessor.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/HotKey.build/HotKey.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/HotKey.build/HotKeysController.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/HotKey.build/Key.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/HotKey.build/KeyCombo+System.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/HotKey.build/KeyCombo.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/HotKey.build/NSEventModifierFlags+HotKey.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/KeychainAccess.build/Keychain.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Starscream.build/Compression.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Starscream.build/Data+Extensions.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Starscream.build/Engine.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Starscream.build/FoundationHTTPHandler.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Starscream.build/FoundationHTTPServerHandler.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Starscream.build/FoundationSecurity.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Starscream.build/FoundationTransport.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Starscream.build/FrameCollector.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Starscream.build/Framer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Starscream.build/HTTPHandler.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Starscream.build/NativeEngine.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Starscream.build/Security.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Starscream.build/Server.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Starscream.build/StringHTTPHandler.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Starscream.build/TCPTransport.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Starscream.build/Transport.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Starscream.build/WSCompression.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Starscream.build/WSEngine.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Starscream.build/WebSocket.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Starscream.build/WebSocketServer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Starscream.build/resource_bundle_accessor.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/APIClient.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/App.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/AppState.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/ContentView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/DashboardView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/DockerView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/LogsView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/MenuBarView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/Models.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/NotificationManager.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/NotificationsView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/PreferencesManager.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/QuickSearchView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/ServicesView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/SettingsView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/SocketManager.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/SystemMetricsView.swift.o
