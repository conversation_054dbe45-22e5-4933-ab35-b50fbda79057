version: "Apple Swift version 6.1.2 (swiftlang-6.1.2.1.2 clang-1700.0.13.5)"
options: "8838702dbbc465a7627c23eaaf74f01e655428f7c31d1227f87a2665a66c6f24"
build_start_time: [1750472769, 409327000]
build_end_time: [1750472771, 658027000]
inputs:
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/HotKey/Sources/HotKey/HotKey.swift": [1750472008, 967688778]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/HotKey/Sources/HotKey/HotKeysController.swift": [1750472008, 967786234]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/HotKey/Sources/HotKey/Key.swift": [1750472008, 967884814]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/HotKey/Sources/HotKey/KeyCombo+System.swift": [1750472008, 967984478]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/HotKey/Sources/HotKey/KeyCombo.swift": [1750472008, 968062018]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/HotKey/Sources/HotKey/NSEventModifierFlags+HotKey.swift": [1750472008, 968145390]
