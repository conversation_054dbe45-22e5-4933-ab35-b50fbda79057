/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/AFError.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/Alamofire.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/AlamofireExtended.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/AuthenticationInterceptor.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/CachedResponseHandler.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/Combine.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/Concurrency.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/DataRequest.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/DataStreamRequest.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/DispatchQueue+Alamofire.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/DownloadRequest.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/EventMonitor.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/HTTPHeaders.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/HTTPMethod.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/MultipartFormData.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/MultipartUpload.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/NetworkReachabilityManager.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/Notifications.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/OperationQueue+Alamofire.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/ParameterEncoder.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/ParameterEncoding.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/Protected.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/RedirectHandler.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/Request.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/RequestCompression.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/RequestInterceptor.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/RequestTaskMap.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/Response.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/ResponseSerialization.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/Result+Alamofire.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/RetryPolicy.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/ServerTrustEvaluation.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/Session.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/SessionDelegate.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/StringEncoding+Alamofire.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/URLConvertible+URLRequestConvertible.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/URLEncodedFormEncoder.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/URLRequest+Alamofire.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/URLSessionConfiguration+Alamofire.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/UploadRequest.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/Validation.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/WebSocketRequest.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/resource_bundle_accessor.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/AES-GCM.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/AES-GCM_boring.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/AES.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/AESWrap.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/AESWrap_boring.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/ASN1.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/ASN1Any.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/ASN1BitString.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/ASN1Boolean.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/ASN1Identifier.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/ASN1Integer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/ASN1Null.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/ASN1OctetString.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/ASN1Strings.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/ArraySliceBigint.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/ChaChaPoly.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/ChaChaPoly_boring.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/Cipher.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/CryptoKitErrors.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/CryptoKitErrors_boring.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/Curve25519.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/DH.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/DHKEM.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/Digest.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/Digest_boring.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/Digests.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/ECDH.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/ECDH_boring.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/ECDSA.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/ECDSASignature.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/ECDSASignature_boring.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/ECDSA_boring.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/Ed25519.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/Ed25519Keys.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/Ed25519_boring.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/EdDSA_boring.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/GeneralizedTime.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/HKDF.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/HMAC.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/HPKE-AEAD.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/HPKE-Ciphersuite.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/HPKE-Context.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/HPKE-Errors.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/HPKE-KDF.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/HPKE-KEM-Curve25519.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/HPKE-KEM.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/HPKE-KexKeyDerivation.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/HPKE-KeySchedule.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/HPKE-LabeledExtract.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/HPKE-Modes.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/HPKE-NIST-EC-KEMs.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/HPKE-Utils.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/HPKE.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/HashFunctions.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/HashFunctions_SHA2.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/Insecure.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/Insecure_HashFunctions.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/KEM.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/MACFunctions.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/MessageAuthenticationCode.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/NISTCurvesKeys.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/NISTCurvesKeys_boring.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/Nonces.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/ObjectIdentifier.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/PEMDocument.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/PKCS8PrivateKey.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/PrettyBytes.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/RNG_boring.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/SEC1PrivateKey.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/SafeCompare.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/SafeCompare_boring.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/SecureBytes.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/Signature.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/SubjectPublicKeyInfo.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/SymmetricKeys.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/X25519Keys.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/X25519Keys_boring.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/Zeroization.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/Zeroization_boring.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/resource_bundle_accessor.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/AnimatedMoveViewJob.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/AnimatedViewPortJob.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/AnimatedZoomViewJob.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Animator.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/AxisBase.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/AxisRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/AxisValueFormatter.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarChartData.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarChartDataEntry.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarChartDataProvider.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarChartDataSet.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarChartDataSetProtocol.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarChartRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarChartView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarHighlighter.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarLineChartViewBase.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarLineScatterCandleBubbleChartData.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarLineScatterCandleBubbleChartDataProvider.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarLineScatterCandleBubbleChartDataSet.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarLineScatterCandleBubbleChartDataSetProtocol.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarLineScatterCandleBubbleRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BubbleChartData.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BubbleChartDataEntry.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BubbleChartDataProvider.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BubbleChartDataSet.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BubbleChartDataSetProtocol.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BubbleChartRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BubbleChartView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CandleChartData.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CandleChartDataEntry.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CandleChartDataProvider.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CandleChartDataSet.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CandleChartDataSetProtocol.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CandleStickChartRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CandleStickChartView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartAnimationEasing.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartBaseDataSet.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartColorTemplates.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartData.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartDataEntry.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartDataEntryBase.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartDataProvider.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartDataSet.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartDataSetProtocol.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartHighlighter.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartLimitLine.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartUtils.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartViewBase.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChevronDownShapeRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChevronUpShapeRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CircleShapeRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CombinedChartData.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CombinedChartDataProvider.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CombinedChartRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CombinedChartView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CombinedHighlighter.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ComponentBase.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CrossShapeRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/DataApproximator+N.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/DataApproximator.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/DataRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/DefaultAxisValueFormatter.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/DefaultFillFormatter.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/DefaultValueFormatter.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Description.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Fill.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/FillFormatter.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Highlight.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Highlighter.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/HorizontalBarChartRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/HorizontalBarChartView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/HorizontalBarHighlighter.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/IndexAxisValueFormatter.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Indexed.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Legend.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LegendEntry.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LegendRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineChartData.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineChartDataProvider.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineChartDataSet.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineChartDataSetProtocol.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineChartRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineChartView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineRadarChartDataSet.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineRadarChartDataSetProtocol.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineRadarRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineScatterCandleRadarChartDataSet.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineScatterCandleRadarChartDataSetProtocol.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineScatterCandleRadarRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Marker.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/MarkerImage.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/MarkerView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/MoveViewJob.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Partition.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieChartData.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieChartDataEntry.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieChartDataSet.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieChartDataSetProtocol.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieChartRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieChartView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieHighlighter.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieRadarChartViewBase.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieRadarHighlighter.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Platform+Accessibility.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Platform+Color.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Platform+Gestures.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Platform+Graphics.swift.o
'/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Platform+Touch Handling.swift.o'
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Platform.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/RadarChartData.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/RadarChartDataEntry.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/RadarChartDataSet.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/RadarChartDataSetProtocol.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/RadarChartRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/RadarChartView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/RadarHighlighter.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Range.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Renderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ScatterChartData.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ScatterChartDataProvider.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ScatterChartDataSet.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ScatterChartDataSetProtocol.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ScatterChartRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ScatterChartView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Sequence+KeyPath.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ShapeRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/SquareShapeRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Transformer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/TransformerHorizontalBarChart.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/TriangleShapeRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ValueFormatter.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ViewPortHandler.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ViewPortJob.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/XAxis.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/XAxisRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/XAxisRendererHorizontalBarChart.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/XAxisRendererRadarChart.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/XShapeRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/YAxis.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/YAxisRenderer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/YAxisRendererHorizontalBarChart.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/YAxisRendererRadarChart.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ZoomViewJob.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/resource_bundle_accessor.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HotKey.build/HotKey.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HotKey.build/HotKeysController.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HotKey.build/Key.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HotKey.build/KeyCombo+System.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HotKey.build/KeyCombo.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HotKey.build/NSEventModifierFlags+HotKey.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/KeychainAccess.build/Keychain.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Starscream.build/Compression.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Starscream.build/Data+Extensions.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Starscream.build/Engine.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Starscream.build/FoundationHTTPHandler.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Starscream.build/FoundationHTTPServerHandler.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Starscream.build/FoundationSecurity.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Starscream.build/FoundationTransport.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Starscream.build/FrameCollector.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Starscream.build/Framer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Starscream.build/HTTPHandler.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Starscream.build/NativeEngine.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Starscream.build/Security.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Starscream.build/Server.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Starscream.build/StringHTTPHandler.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Starscream.build/TCPTransport.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Starscream.build/Transport.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Starscream.build/WSCompression.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Starscream.build/WSEngine.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Starscream.build/WebSocket.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Starscream.build/WebSocketServer.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Starscream.build/resource_bundle_accessor.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/APIClient.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/App.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/AppState.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/ContentView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/DashboardView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/DockerView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/LogsView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/MenuBarView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/Models.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/NotificationManager.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/NotificationsView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/PreferencesManager.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/QuickSearchView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/ServicesView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/SettingsView.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/SocketManager.swift.o
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/SystemMetricsView.swift.o
