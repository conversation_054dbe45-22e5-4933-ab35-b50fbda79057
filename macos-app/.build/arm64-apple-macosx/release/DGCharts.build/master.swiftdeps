version: "Apple Swift version 6.1.2 (swiftlang-6.1.2.1.2 clang-1700.0.13.5)"
options: "4eed2e2f1b245c19244540aa0d22ef77ea322850e76054b57ee63ad67c3f071f"
build_start_time: [1750472769, 418702000]
build_end_time: [1750472783, 701309000]
inputs:
  ? "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/DerivedSources/resource_bundle_accessor.swift"
  : [1750472013, 964904059]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Animation/Animator.swift": [**********, 433360109]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Animation/ChartAnimationEasing.swift": [**********, 433502688]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/BarChartView.swift": [**********, 433701224]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/BarLineChartViewBase.swift": [**********, 433909051]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/BubbleChartView.swift": [**********, 434037381]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/CandleStickChartView.swift": [**********, 434120087]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/ChartViewBase.swift": [**********, 434253208]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/CombinedChartView.swift": [**********, 434366663]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/HorizontalBarChartView.swift": [**********, 434463285]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/LineChartView.swift": [**********, 434548241]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/PieChartView.swift": [**********, 434654988]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/PieRadarChartViewBase.swift": [**********, 434780692]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/RadarChartView.swift": [**********, 435085309]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/ScatterChartView.swift": [**********, 435225180]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/AxisBase.swift": [**********, 435375050]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/ChartLimitLine.swift": [**********, 435468756]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/ComponentBase.swift": [**********, 435548920]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/Description.swift": [**********, 435632918]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/Legend.swift": [**********, 435807996]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/LegendEntry.swift": [**********, 435892785]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/Marker.swift": [**********, 435982074]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/MarkerImage.swift": [**********, 436070863]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/MarkerView.swift": [**********, 436155611]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/XAxis.swift": [**********, 436256275]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/YAxis.swift": [**********, 436358188]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/ChartBaseDataSet.swift": [**********, 436751677]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/BarChartData.swift": [**********, 436903547]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/BarChartDataEntry.swift": [**********, 436997586]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/BarChartDataSet.swift": [**********, 437095625]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/BarLineScatterCandleBubbleChartData.swift"
  : [**********, 437194456]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/BarLineScatterCandleBubbleChartDataSet.swift"
  : [**********, 437279495]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/BubbleChartData.swift": [**********, 437361493]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/BubbleChartDataEntry.swift"
  : [**********, 437456490]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/BubbleChartDataSet.swift": [**********, 437545571]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/CandleChartData.swift": [**********, 437629068]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/CandleChartDataEntry.swift"
  : [**********, 437715691]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/CandleChartDataSet.swift": [**********, 437799230]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/ChartData.swift": [**********, 437951059]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/ChartDataEntry.swift": [**********, 438127054]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/ChartDataEntryBase.swift": [**********, 438324506]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/ChartDataSet.swift": [**********, 438475294]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/CombinedChartData.swift": [**********, 438608998]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/LineChartData.swift": [**********, 438727411]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/LineChartDataSet.swift": [**********, 438846949]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/LineRadarChartDataSet.swift"
  : [**********, 438977946]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/LineScatterCandleRadarChartDataSet.swift"
  : [**********, 439078609]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/PieChartData.swift": [**********, 439176648]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/PieChartDataEntry.swift": [**********, 439254104]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/PieChartDataSet.swift": [**********, 439338060]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/RadarChartData.swift": [**********, 439419474]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/RadarChartDataEntry.swift"
  : [**********, 439502181]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/RadarChartDataSet.swift": [**********, 439590386]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/ScatterChartData.swift": [**********, 439692967]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/ScatterChartDataSet.swift"
  : [**********, 439784422]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/BarChartDataSetProtocol.swift": [**********, 439935293]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/BarLineScatterCandleBubbleChartDataSetProtocol.swift"
  : [**********, 440035707]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/BubbleChartDataSetProtocol.swift": [**********, 440119121]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/CandleChartDataSetProtocol.swift": [**********, 440203410]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/ChartDataSetProtocol.swift": [**********, 440301866]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/LineChartDataSetProtocol.swift": [**********, 440387905]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/LineRadarChartDataSetProtocol.swift": [**********, 440466611]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/LineScatterCandleRadarChartDataSetProtocol.swift"
  : [**********, 440542150]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/PieChartDataSetProtocol.swift": [**********, 440623856]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/RadarChartDataSetProtocol.swift": [**********, 440702937]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/ScatterChartDataSetProtocol.swift": [**********, *********]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Filters/DataApproximator+N.swift": [**********, *********]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Filters/DataApproximator.swift": [**********, *********]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Formatters/AxisValueFormatter.swift": [**********, *********]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Formatters/DefaultAxisValueFormatter.swift": [**********, *********]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Formatters/DefaultFillFormatter.swift": [**********, *********]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Formatters/DefaultValueFormatter.swift": [**********, *********]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Formatters/FillFormatter.swift": [**********, *********]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Formatters/IndexAxisValueFormatter.swift": [**********, *********]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Formatters/ValueFormatter.swift": [**********, *********]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Highlight/BarHighlighter.swift": [**********, *********]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Highlight/ChartHighlighter.swift": [**********, *********]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Highlight/CombinedHighlighter.swift": [**********, 442117105]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Highlight/Highlight.swift": [**********, 442207769]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Highlight/Highlighter.swift": [**********, 442299183]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Highlight/HorizontalBarHighlighter.swift": [**********, 442391472]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Highlight/PieHighlighter.swift": [**********, 442486386]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Highlight/PieRadarHighlighter.swift": [**********, 442576925]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Highlight/RadarHighlighter.swift": [**********, 442663130]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Highlight/Range.swift": [**********, 442753836]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Interfaces/BarChartDataProvider.swift": [**********, 442888707]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Interfaces/BarLineScatterCandleBubbleChartDataProvider.swift"
  : [**********, 442979538]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Interfaces/BubbleChartDataProvider.swift": [**********, 443054244]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Interfaces/CandleChartDataProvider.swift": [**********, 443128783]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Interfaces/ChartDataProvider.swift": [**********, 443203573]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Interfaces/CombinedChartDataProvider.swift": [**********, 443282321]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Interfaces/LineChartDataProvider.swift": [**********, 443357693]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Interfaces/ScatterChartDataProvider.swift": [**********, 443433275]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Jobs/AnimatedMoveViewJob.swift": [**********, 443570312]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Jobs/AnimatedViewPortJob.swift": [**********, 443657185]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Jobs/AnimatedZoomViewJob.swift": [**********, 443733933]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Jobs/MoveViewJob.swift": [**********, 443808306]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Jobs/ViewPortJob.swift": [**********, 443893011]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Jobs/ZoomViewJob.swift": [**********, 443973592]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/AxisRenderer.swift": [**********, 444175920]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/BarChartRenderer.swift": [**********, 444306166]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/BarLineScatterCandleBubbleRenderer.swift": [**********, 444427954]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/BubbleChartRenderer.swift": [**********, 444601116]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/CandleStickChartRenderer.swift": [**********, 444712071]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/CombinedChartRenderer.swift": [**********, 444818443]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/DataRenderer.swift": [**********, 444909815]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/HorizontalBarChartRenderer.swift": [**********, 445063644]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/LegendRenderer.swift": [**********, 445183557]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/LineChartRenderer.swift": [**********, 445275096]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/LineRadarRenderer.swift": [**********, 445367969]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/LineScatterCandleRadarRenderer.swift": [**********, 445452924]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/PieChartRenderer.swift": [**********, 445577921]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/RadarChartRenderer.swift": [**********, 445691417]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/Renderer.swift": [**********, 445786290]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/Scatter/ChevronDownShapeRenderer.swift": [**********, 445920786]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/Scatter/ChevronUpShapeRenderer.swift": [**********, 445998242]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/Scatter/CircleShapeRenderer.swift": [**********, 446077948]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/Scatter/CrossShapeRenderer.swift": [**********, 446151237]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/Scatter/ShapeRenderer.swift": [**********, 446230777]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/Scatter/SquareShapeRenderer.swift": [**********, 446312233]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/Scatter/TriangleShapeRenderer.swift": [**********, 446393647]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/Scatter/XShapeRenderer.swift": [**********, 446474520]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/ScatterChartRenderer.swift": [**********, 446583183]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/XAxisRenderer.swift": [**********, 446701180]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/XAxisRendererHorizontalBarChart.swift": [**********, 446813677]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/XAxisRendererRadarChart.swift": [**********, 446896716]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/YAxisRenderer.swift": [**********, 447040337]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/YAxisRendererHorizontalBarChart.swift": [**********, 447150500]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/YAxisRendererRadarChart.swift": [**********, 447255372]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/ChartColorTemplates.swift": [**********, 447402451]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/ChartUtils.swift": [**********, 447505698]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Fill.swift": [**********, 447805939]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Indexed.swift": [**********, 449215940]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Partition.swift": [**********, 449445892]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Platform+Accessibility.swift": [**********, 449699468]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Platform+Color.swift": [**********, 450027583]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Platform+Gestures.swift": [**********, 450234994]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Platform+Graphics.swift": [**********, 450527319]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Platform+Touch Handling.swift": [**********, 451019263]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Platform.swift": [**********, 451334004]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Sequence+KeyPath.swift": [**********, 451445292]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Transformer.swift": [**********, 451536581]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/TransformerHorizontalBarChart.swift": [**********, 451621620]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/ViewPortHandler.swift": [**********, 451832239]
