version: "Apple Swift version 6.1.2 (swiftlang-6.1.2.1.2 clang-1700.0.13.5)"
options: "60e0ec2f2c1fb83ba78bd5db12fb26b66f6b02e8a5923eae0522045c51022c39"
build_start_time: [1750472769, 409734000]
build_end_time: [1750472778, 141078000]
inputs:
  ? "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/DerivedSources/resource_bundle_accessor.swift"
  : [1750472013, 966170990]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Alamofire.swift": [1750472010, 14800520]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/AFError.swift": [1750472010, 15218592]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/DataRequest.swift": [1750472010, 15459086]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/DataStreamRequest.swift": [1750472010, 15665747]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/DownloadRequest.swift": [1750472010, 15805202]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/HTTPHeaders.swift": [1750472010, 15968239]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/HTTPMethod.swift": [1750472010, 16417894]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/Notifications.swift": [1750472010, 16580098]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/ParameterEncoder.swift": [1750472010, 16682637]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/ParameterEncoding.swift": [1750472010, 16836050]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/Protected.swift": [1750472010, 16945047]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/Request.swift": [1750472010, 17318787]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/RequestTaskMap.swift": [1750472010, 17506615]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/Response.swift": [1750472010, 17648737]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/Session.swift": [1750472010, 17822066]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/SessionDelegate.swift": [1750472010, 17953187]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/URLConvertible+URLRequestConvertible.swift": [1750472010, 18072601]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/UploadRequest.swift": [1750472010, 18177931]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/WebSocketRequest.swift": [1750472010, 18299553]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Extensions/DispatchQueue+Alamofire.swift": [1750472010, 18492215]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Extensions/OperationQueue+Alamofire.swift": [1750472010, 18844872]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Extensions/Result+Alamofire.swift": [1750472010, 19000035]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Extensions/StringEncoding+Alamofire.swift": [1750472010, 19158197]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Extensions/URLRequest+Alamofire.swift": [1750472010, 19259903]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Extensions/URLSessionConfiguration+Alamofire.swift": [1750472010, 19378900]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/AlamofireExtended.swift": [1750472010, 19637851]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/AuthenticationInterceptor.swift": [1750472010, 19799805]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/CachedResponseHandler.swift": [1750472010, 20094131]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/Combine.swift": [1750472010, 20257918]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/Concurrency.swift": [1750472010, 20424539]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/EventMonitor.swift": [1750472010, 20559952]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/MultipartFormData.swift": [1750472010, 20686157]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/MultipartUpload.swift": [1750472010, 20790404]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/NetworkReachabilityManager.swift": [1750472010, 20888277]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/RedirectHandler.swift": [1750472010, 20979483]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/RequestCompression.swift": [1750472010, 21096146]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/RequestInterceptor.swift": [1750472010, 21213602]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/ResponseSerialization.swift": [1750472010, 21318807]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/RetryPolicy.swift": [1750472010, 21428846]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/ServerTrustEvaluation.swift": [1750472010, 21566426]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/URLEncodedFormEncoder.swift": [1750472010, 21712047]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/Validation.swift": [1750472010, 21850210]
