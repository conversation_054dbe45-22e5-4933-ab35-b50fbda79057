version: "Apple Swift version 6.1.2 (swiftlang-6.1.2.1.2 clang-1700.0.13.5)"
options: "7942881d3acc2d8cd34ea8fe3e7852fdfd7c1a11e870686e917fc875bc3b2cf9"
build_start_time: [1750472769, 410791000]
build_end_time: [1750472770, 151641000]
inputs:
  "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/DerivedSources/resource_bundle_accessor.swift": [1750472013, 963884164]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/AEADs/AES/GCM/AES-GCM.swift": [1750472009, 360093582]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/AEADs/AES/GCM/BoringSSL/AES-GCM_boring.swift": [1750472009, 360285327]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/AEADs/ChachaPoly/BoringSSL/ChaChaPoly_boring.swift"
  : [1750472009, 360509570]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/AEADs/ChachaPoly/ChaChaPoly.swift": [1750472009, 360612234]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/AEADs/Cipher.swift": [1750472009, 360721023]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/AEADs/Nonces.swift": [1750472009, 360836812]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/ASN1.swift": [1750472009, 361163927]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/Basic ASN1 Types/ASN1Any.swift": [1750472009, 361342089]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/Basic ASN1 Types/ASN1BitString.swift": [1750472009, 361457878]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/Basic ASN1 Types/ASN1Boolean.swift": [1750472009, 361558208]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/Basic ASN1 Types/ASN1Identifier.swift": [1750472009, 361669664]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/Basic ASN1 Types/ASN1Integer.swift": [1750472009, 361760703]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/Basic ASN1 Types/ASN1Null.swift": [1750472009, 361852742]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/Basic ASN1 Types/ASN1OctetString.swift": [1750472009, 361944073]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/Basic ASN1 Types/ASN1Strings.swift": [1750472009, 362040070]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/Basic ASN1 Types/ArraySliceBigint.swift": [1750472009, 362126984]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/Basic ASN1 Types/GeneralizedTime.swift": [1750472009, 362267189]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/Basic ASN1 Types/ObjectIdentifier.swift": [1750472009, 362358311]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/ECDSASignature.swift": [1750472009, 362470725]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/PEMDocument.swift": [1750472009, 362779175]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/PKCS8PrivateKey.swift": [1750472009, 364119721]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/SEC1PrivateKey.swift": [1750472009, 364401088]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/SubjectPublicKeyInfo.swift": [1750472009, 364554584]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/CryptoKitErrors.swift": [1750472009, 364794827]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Digests/BoringSSL/Digest_boring.swift": [1750472009, 365029571]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Digests/Digest.swift": [1750472009, 365121943]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Digests/Digests.swift": [1750472009, 365256190]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Digests/HashFunctions.swift": [1750472009, 365492183]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Digests/HashFunctions_SHA2.swift": [1750472009, 365577389]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/HPKE/Ciphersuite/HPKE-AEAD.swift": [1750472009, 365901713]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/HPKE/Ciphersuite/HPKE-Ciphersuite.swift": [1750472009, 365998628]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/HPKE/Ciphersuite/HPKE-KDF.swift": [1750472009, 366106583]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/HPKE/Ciphersuite/HPKE-KexKeyDerivation.swift": [1750472009, 366199330]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/HPKE/Ciphersuite/HPKE-LabeledExtract.swift": [1750472009, 366282703]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/HPKE/Ciphersuite/HPKE-Utils.swift": [1750472009, 366367992]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/HPKE/Ciphersuite/KEM/Conformances/DHKEM.swift": [1750472009, 367067265]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/HPKE/Ciphersuite/KEM/Conformances/HPKE-KEM-Curve25519.swift"
  : [1750472009, 367267509]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/HPKE/Ciphersuite/KEM/Conformances/HPKE-NIST-EC-KEMs.swift"
  : [1750472009, 367451712]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/HPKE/Ciphersuite/KEM/HPKE-KEM.swift": [1750472009, 367579667]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/HPKE/HPKE-Errors.swift": [1750472009, 367871284]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/HPKE/HPKE.swift": [1750472009, 367999697]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/HPKE/Key Schedule/HPKE-Context.swift": [1750472009, 368182734]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/HPKE/Key Schedule/HPKE-KeySchedule.swift": [1750472009, 368341146]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/HPKE/Modes/HPKE-Modes.swift": [1750472009, 368524808]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Insecure/Insecure.swift": [1750472009, 368660429]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Insecure/Insecure_HashFunctions.swift": [1750472009, 368790800]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/KEM/KEM.swift": [1750472009, 368998378]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Key Agreement/BoringSSL/ECDH_boring.swift": [1750472009, 369205122]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Key Agreement/DH.swift": [1750472009, 369297578]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Key Agreement/ECDH.swift": [1750472009, 369447074]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Key Derivation/HKDF.swift": [1750472009, 370076265]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Key Wrapping/AESWrap.swift": [1750472009, 370245260]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Key Wrapping/BoringSSL/AESWrap_boring.swift": [1750472009, 370597667]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Keys/EC/BoringSSL/Ed25519_boring.swift": [1750472009, 370945074]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Keys/EC/BoringSSL/NISTCurvesKeys_boring.swift": [1750472009, 371117903]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Keys/EC/BoringSSL/X25519Keys_boring.swift": [1750472009, 371251149]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Keys/EC/Curve25519.swift": [1750472009, 371338147]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Keys/EC/Ed25519Keys.swift": [1750472009, 371524642]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Keys/EC/NISTCurvesKeys.swift": [1750472009, 371893423]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Keys/EC/X25519Keys.swift": [1750472009, 372181499]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Keys/Symmetric/SymmetricKeys.swift": [1750472009, 373047017]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Message Authentication Codes/HMAC/HMAC.swift": [1750472009, 373390340]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Message Authentication Codes/MACFunctions.swift": [1750472009, 373517253]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Message Authentication Codes/MessageAuthenticationCode.swift"
  : [1750472009, 373621042]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/PRF/AES.swift": [1750472009, 373767455]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Signatures/BoringSSL/ECDSASignature_boring.swift"
  : [1750472009, 374401271]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Signatures/BoringSSL/ECDSA_boring.swift": [1750472009, 374569016]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Signatures/BoringSSL/EdDSA_boring.swift": [1750472009, 374699512]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Signatures/ECDSA.swift": [1750472009, 375050920]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Signatures/Ed25519.swift": [1750472009, 375343787]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Signatures/Signature.swift": [1750472009, 375461491]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Util/BoringSSL/CryptoKitErrors_boring.swift": [1750472009, 375713276]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Util/BoringSSL/RNG_boring.swift": [1750472009, 375812940]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Util/BoringSSL/SafeCompare_boring.swift": [1750472009, 375951478]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Util/BoringSSL/Zeroization_boring.swift": [1750472009, 376076391]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Util/PrettyBytes.swift": [1750472009, 376186596]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Util/SafeCompare.swift": [1750472009, 376334259]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Util/SecureBytes.swift": [1750472009, 376458172]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Util/Zeroization.swift": [1750472009, 376696916]
