import SwiftUI

struct SystemMetricsView: View {
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        VStack(alignment: .leading, spacing: 20) {
            // Header
            HStack {
                Text("System Metrics")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                
                Spacer()
                
                Button("Refresh") {
                    Task {
                        await appState.refreshAllData()
                    }
                }
                .buttonStyle(.bordered)
            }
            
            if let metrics = appState.systemMetrics {
                ScrollView {
                    LazyVStack(spacing: 20) {
                        // CPU Metrics
                        MetricsCard(title: "CPU Performance", icon: "cpu", color: .blue) {
                            VStack(spacing: 12) {
                                MetricRow(label: "Usage", value: "\(String(format: "%.1f", metrics.cpu.usage))%")
                                MetricRow(label: "Cores", value: "\(metrics.cpu.cores)")
                                
                                if !metrics.cpu.loadAverage.isEmpty {
                                    MetricRow(label: "Load Average", value: metrics.cpu.loadAverage.map { String(format: "%.2f", $0) }.joined(separator: ", "))
                                }
                                
                                if let temperature = metrics.cpu.temperature {
                                    MetricRow(label: "Temperature", value: "\(String(format: "%.1f", temperature))°C")
                                }
                            }
                        }
                        
                        // Memory Metrics
                        MetricsCard(title: "Memory Usage", icon: "memorychip", color: .green) {
                            VStack(spacing: 12) {
                                MetricRow(label: "Total", value: formatBytes(metrics.memory.total))
                                MetricRow(label: "Used", value: formatBytes(metrics.memory.used))
                                MetricRow(label: "Available", value: formatBytes(metrics.memory.available))
                                MetricRow(label: "Cached", value: formatBytes(metrics.memory.cached))
                                MetricRow(label: "Buffers", value: formatBytes(metrics.memory.buffers))
                                MetricRow(label: "Usage", value: "\(String(format: "%.1f", metrics.memory.usagePercentage))%")
                            }
                        }
                        
                        // Disk Metrics
                        MetricsCard(title: "Disk Usage", icon: "internaldrive", color: .orange) {
                            VStack(spacing: 12) {
                                MetricRow(label: "Total", value: formatBytes(metrics.disk.total))
                                MetricRow(label: "Used", value: formatBytes(metrics.disk.used))
                                MetricRow(label: "Available", value: formatBytes(metrics.disk.available))
                                MetricRow(label: "Usage", value: "\(String(format: "%.1f", metrics.disk.usagePercentage))%")
                                
                                Divider()
                                
                                MetricRow(label: "Read Operations", value: "\(metrics.disk.readOperations)")
                                MetricRow(label: "Write Operations", value: "\(metrics.disk.writeOperations)")
                                MetricRow(label: "Read Bytes", value: formatBytes(metrics.disk.readBytes))
                                MetricRow(label: "Write Bytes", value: formatBytes(metrics.disk.writeBytes))
                            }
                        }
                        
                        // Network Metrics
                        MetricsCard(title: "Network Activity", icon: "network", color: .purple) {
                            VStack(spacing: 12) {
                                MetricRow(label: "Bytes Received", value: formatBytes(metrics.network.bytesReceived))
                                MetricRow(label: "Bytes Sent", value: formatBytes(metrics.network.bytesSent))
                                MetricRow(label: "Packets Received", value: "\(metrics.network.packetsReceived)")
                                MetricRow(label: "Packets Sent", value: "\(metrics.network.packetsSent)")
                                MetricRow(label: "Errors", value: "\(metrics.network.errors)")
                            }
                        }
                    }
                }
            } else {
                VStack(spacing: 16) {
                    Image(systemName: "chart.line.uptrend.xyaxis")
                        .font(.system(size: 64))
                        .foregroundColor(.secondary)
                    
                    Text("No system metrics available")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    if !appState.isConnected {
                        Button("Connect to Server") {
                            Task {
                                await appState.connectToServer()
                            }
                        }
                        .buttonStyle(.borderedProminent)
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
        }
        .padding()
        .refreshable {
            await appState.refreshAllData()
        }
    }
    
    private func formatBytes(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.countStyle = .binary
        return formatter.string(fromByteCount: bytes)
    }
}

struct MetricsCard<Content: View>: View {
    let title: String
    let icon: String
    let color: Color
    let content: Content
    
    init(title: String, icon: String, color: Color, @ViewBuilder content: () -> Content) {
        self.title = title
        self.icon = icon
        self.color = color
        self.content = content()
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                    .font(.title2)
                
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            content
        }
        .padding(20)
        .background(Color(NSColor.windowBackgroundColor))
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

struct MetricRow: View {
    let label: String
    let value: String
    
    var body: some View {
        HStack {
            Text(label)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .font(.system(.subheadline, design: .monospaced))
                .fontWeight(.medium)
        }
        .font(.subheadline)
    }
}

#Preview {
    SystemMetricsView()
        .environmentObject(AppState())
        .frame(width: 800, height: 600)
}