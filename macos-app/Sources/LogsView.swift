import SwiftUI

struct LogsView: View {
    @EnvironmentObject var appState: AppState
    @State private var searchText = ""
    @State private var selectedLogLevel: LogLevel? = nil
    @State private var selectedSource = "All"
    @State private var autoScroll = true
    
    var body: some View {
        VStack(spacing: 0) {
            // Header with Controls
            VStack(spacing: 12) {
                HStack {
                    Text("Logs")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    Spacer()
                    
                    Button("Clear") {
                        // TODO: Implement clear logs
                    }
                    .buttonStyle(.bordered)
                    
                    Button("Refresh") {
                        Task {
                            await appState.refreshAllData()
                        }
                    }
                    .buttonStyle(.bordered)
                }
                
                // Filters
                HStack {
                    // Search
                    HStack {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(.secondary)
                        
                        TextField("Search logs...", text: $searchText)
                            .textFieldStyle(.roundedBorder)
                    }
                    
                    // Log Level Filter
                    Picker("Level", selection: $selectedLogLevel) {
                        Text("All Levels").tag(LogLevel?.none)
                        ForEach(LogLevel.allCases, id: \.self) { level in
                            Text(level.rawValue.capitalized).tag(LogLevel?.some(level))
                        }
                    }
                    .pickerStyle(.menu)
                    .frame(width: 150)
                    
                    // Source Filter
                    Picker("Source", selection: $selectedSource) {
                        Text("All Sources").tag("All")
                        // TODO: Add dynamic sources based on available log sources
                        Text("System").tag("System")
                        Text("Services").tag("Services")
                        Text("Docker").tag("Docker")
                    }
                    .pickerStyle(.menu)
                    .frame(width: 150)
                    
                    // Auto-scroll toggle
                    Toggle("Auto-scroll", isOn: $autoScroll)
                        .toggleStyle(.checkbox)
                }
            }
            .padding()
            .background(Color(NSColor.windowBackgroundColor))
            
            Divider()
            
            // Logs List
            if filteredLogs.isEmpty {
                VStack(spacing: 16) {
                    Image(systemName: "doc.text")
                        .font(.system(size: 64))
                        .foregroundColor(.secondary)
                    
                    Text(appState.logEntries.isEmpty ? "No logs available" : "No logs match your filters")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    if !appState.isConnected {
                        Button("Connect to Server") {
                            Task {
                                await appState.connectToServer()
                            }
                        }
                        .buttonStyle(.borderedProminent)
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                ScrollViewReader { proxy in
                    List(filteredLogs) { logEntry in
                        LogEntryView(logEntry: logEntry)
                            .id(logEntry.id)
                    }
                    .listStyle(.plain)
                    .onChange(of: filteredLogs.count) { _ in
                        if autoScroll && !filteredLogs.isEmpty {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                proxy.scrollTo(filteredLogs.last?.id, anchor: .bottom)
                            }
                        }
                    }
                }
            }
        }
        .refreshable {
            await appState.refreshAllData()
        }
    }
    
    private var filteredLogs: [LogEntry] {
        var logs = appState.logEntries
        
        // Filter by search text
        if !searchText.isEmpty {
            logs = logs.filter { entry in
                entry.message.localizedCaseInsensitiveContains(searchText) ||
                entry.source.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        // Filter by log level
        if let selectedLevel = selectedLogLevel {
            logs = logs.filter { $0.level == selectedLevel }
        }
        
        // Filter by source
        if selectedSource != "All" {
            logs = logs.filter { entry in
                entry.source.localizedCaseInsensitiveContains(selectedSource)
            }
        }
        
        return logs.sorted { $0.timestamp > $1.timestamp }
    }
}

struct LogEntryView: View {
    let logEntry: LogEntry
    @State private var isExpanded = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack(spacing: 12) {
                // Log Level Indicator
                Image(systemName: logEntry.level.systemImage)
                    .foregroundColor(logEntry.level.color)
                    .font(.subheadline)
                    .frame(width: 16)
                
                // Timestamp
                Text(logEntry.timestamp.formatted(.dateTime.hour().minute().second()))
                    .font(.caption)
                    .font(.system(.body, design: .monospaced))
                    .foregroundColor(.secondary)
                    .frame(width: 80, alignment: .leading)
                
                // Source
                Text(logEntry.source)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.secondary.opacity(0.1))
                    .clipShape(Capsule())
                
                Spacer()
                
                // Log Level Badge
                Text(logEntry.level.rawValue.uppercased())
                    .font(.caption2)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(logEntry.level.color)
                    .clipShape(Capsule())
            }
            
            // Log Message
            VStack(alignment: .leading, spacing: 4) {
                Text(logEntry.message)
                    .font(.system(.body, design: .monospaced))
                    .lineLimit(isExpanded ? nil : 3)
                    .textSelection(.enabled)
                
                if logEntry.message.count > 200 {
                    Button(isExpanded ? "Show Less" : "Show More") {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            isExpanded.toggle()
                        }
                    }
                    .font(.caption)
                    .foregroundColor(.accentColor)
                }
            }
            
            // Metadata (if available and expanded)
            if isExpanded, let metadata = logEntry.metadata, !metadata.isEmpty {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Metadata:")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.secondary)
                    
                    ForEach(Array(metadata.sorted(by: { $0.key < $1.key })), id: \.key) { key, value in
                        HStack {
                            Text("\(key):")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            
                            Text(value)
                                .font(.caption)
                                .font(.system(.body, design: .monospaced))
                                .textSelection(.enabled)
                            
                            Spacer()
                        }
                    }
                }
                .padding(.top, 4)
            }
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(logEntry.level == .error ? Color.red.opacity(0.05) :
                      logEntry.level == .warning ? Color.orange.opacity(0.05) :
                      Color.clear)
        )
        .onTapGesture {
            if logEntry.metadata != nil && !logEntry.metadata!.isEmpty {
                withAnimation(.easeInOut(duration: 0.2)) {
                    isExpanded.toggle()
                }
            }
        }
    }
}

#Preview {
    LogsView()
        .environmentObject(AppState())
        .frame(width: 1000, height: 700)
}