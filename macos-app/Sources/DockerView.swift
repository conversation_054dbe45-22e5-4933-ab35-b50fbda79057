import SwiftUI

struct DockerView: View {
    @EnvironmentObject var appState: AppState
    @State private var selectedTab: DockerTab = .containers
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            HStack {
                Text("Docker Management")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                
                Spacer()
                
                Button("Refresh") {
                    Task {
                        await appState.refreshAllData()
                    }
                }
                .buttonStyle(.bordered)
            }
            .padding()
            .background(Color(NSColor.windowBackgroundColor))
            
            // Tab Picker
            Picker("Docker View", selection: $selectedTab) {
                Text("Containers").tag(DockerTab.containers)
                Text("Images").tag(DockerTab.images)
            }
            .pickerStyle(.segmented)
            .padding(.horizontal)
            
            Divider()
            
            // Content
            Group {
                switch selectedTab {
                case .containers:
                    ContainersListView()
                case .images:
                    ImagesListView()
                }
            }
        }
        .refreshable {
            await appState.refreshAllData()
        }
    }
}

enum DockerTab {
    case containers
    case images
}

// MARK: - Containers List View

struct ContainersListView: View {
    @EnvironmentObject var appState: AppState
    @State private var searchText = ""
    @State private var selectedContainer: DockerContainer?
    @State private var showingContainerDetail = false
    
    var body: some View {
        VStack(spacing: 0) {
            // Search Bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                
                TextField("Search containers...", text: $searchText)
                    .textFieldStyle(.roundedBorder)
            }
            .padding()
            
            // Containers List
            if filteredContainers.isEmpty {
                VStack(spacing: 16) {
                    Image(systemName: "shippingbox")
                        .font(.system(size: 64))
                        .foregroundColor(.secondary)
                    
                    Text(appState.dockerContainers.isEmpty ? "No containers found" : "No containers match your search")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    if !appState.isConnected {
                        Button("Connect to Server") {
                            Task {
                                await appState.connectToServer()
                            }
                        }
                        .buttonStyle(.borderedProminent)
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                List(filteredContainers) { container in
                    ContainerRowView(container: container)
                        .onTapGesture {
                            selectedContainer = container
                            showingContainerDetail = true
                        }
                }
                .listStyle(.plain)
            }
        }
        .sheet(isPresented: $showingContainerDetail) {
            if let container = selectedContainer {
                ContainerDetailView(container: container)
            }
        }
    }
    
    private var filteredContainers: [DockerContainer] {
        if searchText.isEmpty {
            return appState.dockerContainers
        }
        return appState.dockerContainers.filter { container in
            container.name.localizedCaseInsensitiveContains(searchText) ||
            container.image.localizedCaseInsensitiveContains(searchText)
        }
    }
}

struct ContainerRowView: View {
    let container: DockerContainer
    @EnvironmentObject var appState: AppState
    @State private var isPerformingAction = false
    
    var body: some View {
        HStack(spacing: 16) {
            // Status Indicator
            VStack(spacing: 4) {
                Image(systemName: container.status.systemImage)
                    .font(.title2)
                    .foregroundColor(container.status.color)
                
                Text(container.status.rawValue.capitalized)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            .frame(width: 80)
            
            // Container Information
            VStack(alignment: .leading, spacing: 6) {
                HStack {
                    Text(container.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Spacer()
                }
                
                Text(container.image)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
                
                HStack {
                    Label(container.created.formatted(.relative(presentation: .named)), systemImage: "calendar")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    if !container.ports.isEmpty {
                        Label("\(container.ports.count) ports", systemImage: "network")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    if let stats = container.stats {
                        Label("\(String(format: "%.1f", stats.cpu))% CPU", systemImage: "cpu")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            Spacer()
            
            // Action Buttons
            HStack(spacing: 8) {
                if container.status == .running {
                    Button("Stop") {
                        performAction {
                            await appState.stopContainer(container.id)
                        }
                    }
                    .buttonStyle(.bordered)
                    
                    Button("Restart") {
                        performAction {
                            await appState.restartContainer(container.id)
                        }
                    }
                    .buttonStyle(.borderedProminent)
                } else if container.status == .exited {
                    Button("Start") {
                        performAction {
                            await appState.startContainer(container.id)
                        }
                    }
                    .buttonStyle(.borderedProminent)
                }
            }
            .disabled(isPerformingAction)
            .opacity(isPerformingAction ? 0.6 : 1.0)
        }
        .padding(.vertical, 8)
        .contentShape(Rectangle())
    }
    
    private func performAction(_ action: @escaping () async -> Void) {
        isPerformingAction = true
        Task {
            await action()
            isPerformingAction = false
        }
    }
}

// MARK: - Images List View

struct ImagesListView: View {
    @EnvironmentObject var appState: AppState
    @State private var searchText = ""
    
    var body: some View {
        VStack(spacing: 0) {
            // Search Bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                
                TextField("Search images...", text: $searchText)
                    .textFieldStyle(.roundedBorder)
            }
            .padding()
            
            // Images List
            if filteredImages.isEmpty {
                VStack(spacing: 16) {
                    Image(systemName: "externaldrive")
                        .font(.system(size: 64))
                        .foregroundColor(.secondary)
                    
                    Text(appState.dockerImages.isEmpty ? "No images found" : "No images match your search")
                        .font(.headline)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                List(filteredImages) { image in
                    ImageRowView(image: image)
                }
                .listStyle(.plain)
            }
        }
    }
    
    private var filteredImages: [DockerImage] {
        if searchText.isEmpty {
            return appState.dockerImages
        }
        return appState.dockerImages.filter { image in
            image.repository.localizedCaseInsensitiveContains(searchText) ||
            image.tag.localizedCaseInsensitiveContains(searchText)
        }
    }
}

struct ImageRowView: View {
    let image: DockerImage
    
    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: "externaldrive")
                .font(.title2)
                .foregroundColor(.blue)
                .frame(width: 40)
            
            VStack(alignment: .leading, spacing: 6) {
                HStack {
                    Text(image.repository)
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text(":\(image.tag)")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                }
                
                HStack {
                    Label(formatBytes(image.size), systemImage: "externaldrive")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Label(image.created.formatted(.relative(presentation: .named)), systemImage: "calendar")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Label("\(image.containers) containers", systemImage: "shippingbox")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
        }
        .padding(.vertical, 8)
        .contentShape(Rectangle())
    }
    
    private func formatBytes(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.countStyle = .binary
        return formatter.string(fromByteCount: bytes)
    }
}

// MARK: - Container Detail View

struct ContainerDetailView: View {
    let container: DockerContainer
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        VStack(alignment: .leading, spacing: 20) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    Text(container.name)
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    HStack(spacing: 12) {
                        Label(container.status.rawValue.capitalized, systemImage: container.status.systemImage)
                            .foregroundColor(container.status.color)
                        
                        Label(container.image, systemImage: "externaldrive")
                            .foregroundColor(.secondary)
                    }
                    .font(.subheadline)
                }
                
                Spacer()
                
                Button("Close") {
                    dismiss()
                }
                .buttonStyle(.bordered)
            }
            
            // Container Details
            ScrollView {
                VStack(alignment: .leading, spacing: 16) {
                    DetailSection(title: "Container Information") {
                        VStack(spacing: 8) {
                            DetailRow(label: "ID", value: container.id)
                            DetailRow(label: "Image", value: container.image)
                            DetailRow(label: "State", value: container.state)
                            DetailRow(label: "Created", value: container.created.formatted())
                            if let started = container.started {
                                DetailRow(label: "Started", value: started.formatted())
                            }
                        }
                    }
                    
                    if !container.ports.isEmpty {
                        DetailSection(title: "Port Mappings") {
                            VStack(spacing: 6) {
                                ForEach(container.ports, id: \.hostPort) { port in
                                    HStack {
                                        Text("\(port.hostPort)")
                                            .font(.system(.subheadline, design: .monospaced))
                                        Text("→")
                                            .foregroundColor(.secondary)
                                        Text("\(port.containerPort)/\(port.protocol)")
                                            .font(.system(.subheadline, design: .monospaced))
                                        Spacer()
                                    }
                                    .font(.subheadline)
                                }
                            }
                        }
                    }
                    
                    if !container.volumes.isEmpty {
                        DetailSection(title: "Volume Mounts") {
                            VStack(spacing: 6) {
                                ForEach(container.volumes, id: \.hostPath) { volume in
                                    VStack(alignment: .leading, spacing: 2) {
                                        Text(volume.hostPath)
                                            .font(.system(.subheadline, design: .monospaced))
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                        Text("→ \(volume.containerPath) (\(volume.mode))")
                                            .font(.system(.subheadline, design: .monospaced))
                                            .font(.subheadline)
                                    }
                                }
                            }
                        }
                    }
                    
                    if let stats = container.stats {
                        DetailSection(title: "Resource Usage") {
                            VStack(spacing: 8) {
                                DetailRow(label: "CPU", value: "\(String(format: "%.2f", stats.cpu))%")
                                DetailRow(label: "Memory", value: "\(formatBytes(stats.memory)) / \(formatBytes(stats.memoryLimit))")
                                DetailRow(label: "Memory %", value: "\(String(format: "%.1f", stats.memoryUsagePercentage))%")
                                DetailRow(label: "Network In", value: formatBytes(stats.networkIn))
                                DetailRow(label: "Network Out", value: formatBytes(stats.networkOut))
                                DetailRow(label: "Block Read", value: formatBytes(stats.blockRead))
                                DetailRow(label: "Block Write", value: formatBytes(stats.blockWrite))
                            }
                        }
                    }
                    
                    if !container.environment.isEmpty {
                        DetailSection(title: "Environment Variables") {
                            VStack(spacing: 6) {
                                ForEach(Array(container.environment.sorted(by: { $0.key < $1.key })), id: \.key) { key, value in
                                    HStack {
                                        Text(key)
                                            .font(.system(.subheadline, design: .monospaced))
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                        Text("=")
                                            .foregroundColor(.secondary)
                                        Text(value)
                                            .font(.system(.subheadline, design: .monospaced))
                                            .font(.caption)
                                            .lineLimit(1)
                                        Spacer()
                                    }
                                }
                            }
                        }
                    }
                }
            }
            
            Spacer()
        }
        .padding()
        .frame(width: 600, height: 700)
    }
    
    private func formatBytes(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.countStyle = .binary
        return formatter.string(fromByteCount: bytes)
    }
}

struct DetailRow: View {
    let label: String
    let value: String
    
    var body: some View {
        HStack {
            Text(label)
                .foregroundColor(.secondary)
                .frame(width: 120, alignment: .leading)
            
            Text(value)
                .font(.system(.subheadline, design: .monospaced))
                .textSelection(.enabled)
            
            Spacer()
        }
        .font(.subheadline)
    }
}

#Preview {
    DockerView()
        .environmentObject(AppState())
        .frame(width: 1000, height: 700)
}