import SwiftUI

struct NotificationsView: View {
    @EnvironmentObject var appState: AppState
    @State private var selectedFilter: NotificationFilter = .all
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            VStack(spacing: 12) {
                HStack {
                    Text("Notifications")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    Spacer()
                    
                    if !appState.notifications.isEmpty {
                        But<PERSON>("Mark All Read") {
                            markAllAsRead()
                        }
                        .buttonStyle(.bordered)
                    }
                    
                    <PERSON><PERSON>("Clear All") {
                        clearAllNotifications()
                    }
                    .buttonStyle(.bordered)
                }
                
                // Filter Picker
                Picker("Filter", selection: $selectedFilter) {
                    Text("All (\(appState.notifications.count))").tag(NotificationFilter.all)
                    Text("Unread (\(unreadCount))").tag(NotificationFilter.unread)
                    Text("Info").tag(NotificationFilter.info)
                    Text("Warning").tag(NotificationFilter.warning)
                    Text("Error").tag(NotificationFilter.error)
                    Text("Success").tag(NotificationFilter.success)
                }
                .pickerStyle(.segmented)
            }
            .padding()
            .background(Color(NSColor.windowBackgroundColor))
            
            Divider()
            
            // Notifications List
            if filteredNotifications.isEmpty {
                VStack(spacing: 16) {
                    Image(systemName: "bell.slash")
                        .font(.system(size: 64))
                        .foregroundColor(.secondary)
                    
                    Text(emptyStateMessage)
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    Text("Notifications will appear here when events occur")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                List(filteredNotifications) { notification in
                    NotificationRowView(notification: notification)
                        .swipeActions(edge: .trailing, allowsFullSwipe: true) {
                            Button("Delete") {
                                deleteNotification(notification)
                            }
                            .tint(.red)
                            
                            if !notification.read {
                                Button("Mark Read") {
                                    markAsRead(notification)
                                }
                                .tint(.blue)
                            }
                        }
                }
                .listStyle(.plain)
            }
        }
        .refreshable {
            // Refresh would typically reload from server
            // For now, this is just for UI consistency
        }
    }
    
    private var filteredNotifications: [AppNotification] {
        switch selectedFilter {
        case .all:
            return appState.notifications
        case .unread:
            return appState.notifications.filter { !$0.read }
        case .info:
            return appState.notifications.filter { $0.type == .info }
        case .warning:
            return appState.notifications.filter { $0.type == .warning }
        case .error:
            return appState.notifications.filter { $0.type == .error }
        case .success:
            return appState.notifications.filter { $0.type == .success }
        }
    }
    
    private var unreadCount: Int {
        appState.notifications.filter { !$0.read }.count
    }
    
    private var emptyStateMessage: String {
        switch selectedFilter {
        case .all:
            return "No notifications"
        case .unread:
            return "No unread notifications"
        case .info:
            return "No info notifications"
        case .warning:
            return "No warning notifications"
        case .error:
            return "No error notifications"
        case .success:
            return "No success notifications"
        }
    }
    
    private func markAsRead(_ notification: AppNotification) {
        appState.markNotificationAsRead(notification.id)
    }
    
    private func markAllAsRead() {
        for notification in appState.notifications.filter({ !$0.read }) {
            appState.markNotificationAsRead(notification.id)
        }
    }
    
    private func deleteNotification(_ notification: AppNotification) {
        if let index = appState.notifications.firstIndex(where: { $0.id == notification.id }) {
            appState.notifications.remove(at: index)
        }
    }
    
    private func clearAllNotifications() {
        appState.notifications.removeAll()
    }
}

enum NotificationFilter: CaseIterable {
    case all, unread, info, warning, error, success
}

struct NotificationRowView: View {
    let notification: AppNotification
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        HStack(alignment: .top, spacing: 16) {
            // Notification Icon
            Image(systemName: notification.type.systemImage)
                .foregroundColor(notification.type.color)
                .font(.title2)
                .frame(width: 24)
            
            // Content
            VStack(alignment: .leading, spacing: 8) {
                // Header
                HStack {
                    Text(notification.title)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    Text(notification.timestamp.formatted(.relative(presentation: .named)))
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    if !notification.read {
                        Circle()
                            .fill(Color.accentColor)
                            .frame(width: 8, height: 8)
                    }
                }
                
                // Message
                Text(notification.message)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .fixedSize(horizontal: false, vertical: true)
                
                // Actions (if available)
                if let actions = notification.actions, !actions.isEmpty {
                    HStack(spacing: 8) {
                        ForEach(actions, id: \.id) { action in
                            Button(action.title) {
                                // TODO: Handle notification action
                                print("Action: \(action.action)")
                            }
                            .buttonStyle(.bordered)
                            .controlSize(.small)
                        }
                    }
                    .padding(.top, 4)
                }
                
                // Type Badge
                HStack {
                    Text(notification.type.rawValue.uppercased())
                        .font(.caption2)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(notification.type.color)
                        .clipShape(Capsule())
                    
                    Spacer()
                    
                    Text(notification.timestamp.formatted(.dateTime.hour().minute().second()))
                        .font(.caption2)
                        .font(.system(.caption2, design: .monospaced))
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 16)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(notification.read ? Color.clear : Color.accentColor.opacity(0.05))
        )
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(notification.read ? Color.clear : Color.accentColor.opacity(0.2), lineWidth: 1)
        )
        .onTapGesture {
            if !notification.read {
                appState.markNotificationAsRead(notification.id)
            }
        }
    }
}

#Preview {
    NotificationsView()
        .environmentObject(AppState())
        .frame(width: 800, height: 600)
}