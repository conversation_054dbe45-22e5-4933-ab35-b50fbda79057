import SwiftUI

@main
struct WOWMonitorApp: App {
    @StateObject private var appState = AppState()
    
    init() {
        print("🚀 WOW Monitor App starting...")
    }
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(appState)
                .frame(minWidth: 1000, minHeight: 700)
        }
        .windowStyle(.hiddenTitleBar)
        .windowToolbarStyle(.unified(showsTitle: false))
        .commands {
            WOWMonitorCommands()
        }
        
        MenuBarExtra("WOW Monitor", systemImage: "server.rack") {
            MenuBarView()
                .environmentObject(appState)
        }
        .menuBarExtraStyle(.window)
        
        Settings {
            SettingsView()
                .environmentObject(appState)
        }
    }
}

struct WOWMonitorCommands: Commands {
    var body: some Commands {
        CommandGroup(after: .newItem) {
            Button("Quick Search") {
                NotificationCenter.default.post(name: .showQuickSearch, object: nil)
            }
            .keyboardShortcut("k", modifiers: .command)
            
            But<PERSON>("Refresh All Data") {
                NotificationCenter.default.post(name: .refreshAllData, object: nil)
            }
            .keyboardShortcut("r", modifiers: .command)
            
            Divider()
            
            But<PERSON>("Connect to Server") {
                NotificationCenter.default.post(name: .connectToServer, object: nil)
            }
            .keyboardShortcut("c", modifiers: [.command, .shift])
        }
        
        CommandGroup(after: .help) {
            Button("Check for Updates") {
                NotificationCenter.default.post(name: .checkForUpdates, object: nil)
            }
            .keyboardShortcut("u", modifiers: [.command, .shift])
        }
    }
}

extension Notification.Name {
    static let showQuickSearch = Notification.Name("showQuickSearch")
    static let refreshAllData = Notification.Name("refreshAllData")
    static let connectToServer = Notification.Name("connectToServer")
    static let checkForUpdates = Notification.Name("checkForUpdates")
}