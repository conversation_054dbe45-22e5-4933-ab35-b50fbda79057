import type { FastifyInstance } from 'fastify';
import { dockerService } from '../../services/docker.js';

export async function dockerApiRoutes(fastify: FastifyInstance) {
  // GET /api/docker/status - Get Docker status
  fastify.get('/docker/status', async (request, reply) => {
    try {
      const isRunning = await dockerService.isDockerRunning();
      
      reply.type('application/json').send({
        success: true,
        data: {
          running: isRunning,
          version: isRunning ? await dockerService.getDockerVersion() : null
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get Docker status',
        timestamp: new Date().toISOString()
      });
    }
  });

  // GET /api/docker/containers - List containers
  fastify.get('/docker/containers', async (request, reply) => {
    try {
      const { all = 'false' } = request.query as { all?: string };
      const containers = await dockerService.listContainers(all === 'true');
      
      reply.type('application/json').send({
        success: true,
        data: containers,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to list containers',
        timestamp: new Date().toISOString()
      });
    }
  });

  // GET /api/docker/containers/:id - Get specific container
  fastify.get('/docker/containers/:id', async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const container = await dockerService.getContainer(id);
      
      reply.type('application/json').send({
        success: true,
        data: container,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 500;
      reply.status(statusCode).send({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get container',
        timestamp: new Date().toISOString()
      });
    }
  });

  // POST /api/docker/containers/:id/start - Start container
  fastify.post('/docker/containers/:id/start', async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const result = await dockerService.startContainer(id);
      
      reply.type('application/json').send({
        success: true,
        data: { message: `Container ${id} started successfully`, result },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 500;
      reply.status(statusCode).send({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to start container',
        timestamp: new Date().toISOString()
      });
    }
  });

  // POST /api/docker/containers/:id/stop - Stop container
  fastify.post('/docker/containers/:id/stop', async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const result = await dockerService.stopContainer(id);
      
      reply.type('application/json').send({
        success: true,
        data: { message: `Container ${id} stopped successfully`, result },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 500;
      reply.status(statusCode).send({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to stop container',
        timestamp: new Date().toISOString()
      });
    }
  });

  // POST /api/docker/containers/:id/restart - Restart container
  fastify.post('/docker/containers/:id/restart', async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const result = await dockerService.restartContainer(id);
      
      reply.type('application/json').send({
        success: true,
        data: { message: `Container ${id} restarted successfully`, result },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 500;
      reply.status(statusCode).send({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to restart container',
        timestamp: new Date().toISOString()
      });
    }
  });

  // GET /api/docker/containers/:id/logs - Get container logs
  fastify.get('/docker/containers/:id/logs', async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const { lines = '100' } = request.query as { lines?: string };
      
      const logs = await dockerService.getContainerLogs(id, parseInt(lines, 10));
      
      // Convert logs to the expected format
      const logEntries = logs.map((logLine, index) => ({
        id: `${id}-${index}`,
        timestamp: new Date().toISOString(),
        level: 'info',
        source: `docker:${id}`,
        message: logLine,
        metadata: null
      }));
      
      reply.type('application/json').send({
        success: true,
        data: logEntries,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 500;
      reply.status(statusCode).send({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get container logs',
        timestamp: new Date().toISOString()
      });
    }
  });

  // GET /api/docker/images - List images
  fastify.get('/docker/images', async (request, reply) => {
    try {
      const images = await dockerService.listImages();
      
      reply.type('application/json').send({
        success: true,
        data: images,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to list images',
        timestamp: new Date().toISOString()
      });
    }
  });

  // DELETE /api/docker/containers/:id - Remove container
  fastify.delete('/docker/containers/:id', async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const { force = 'false' } = request.query as { force?: string };
      
      const result = await dockerService.removeContainer(id, force === 'true');
      
      reply.type('application/json').send({
        success: true,
        data: { message: `Container ${id} removed successfully`, result },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 500;
      reply.status(statusCode).send({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to remove container',
        timestamp: new Date().toISOString()
      });
    }
  });

  // GET /api/docker/stats/:id - Get container stats
  fastify.get('/docker/stats/:id', async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const stats = await dockerService.getContainerStats(id);
      
      reply.type('application/json').send({
        success: true,
        data: stats,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 500;
      reply.status(statusCode).send({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get container stats',
        timestamp: new Date().toISOString()
      });
    }
  });
}