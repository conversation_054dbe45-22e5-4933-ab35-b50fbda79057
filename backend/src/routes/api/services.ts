import type { FastifyInstance } from 'fastify';
import { serviceManager } from '../../services/service-manager.js';

export async function servicesApiRoutes(fastify: FastifyInstance) {
  // GET /api/services - Get all services
  fastify.get('/services', async (request, reply) => {
    try {
      const services = await serviceManager.getAllServices();
      
      reply.type('application/json').send({
        success: true,
        data: services,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get services',
        timestamp: new Date().toISOString()
      });
    }
  });

  // GET /api/services/:id - Get specific service
  fastify.get('/services/:id', async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const service = await serviceManager.getServiceInfo(id);
      
      reply.type('application/json').send({
        success: true,
        data: service,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 500;
      reply.status(statusCode).send({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get service',
        timestamp: new Date().toISOString()
      });
    }
  });

  // POST /api/services/:id/start - Start a service
  fastify.post('/services/:id/start', async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const result = await serviceManager.startService(id);
      
      if (result) {
        reply.type('application/json').send({
          success: true,
          data: { message: `Service ${id} started successfully` },
          timestamp: new Date().toISOString()
        });
      } else {
        reply.status(500).send({
          success: false,
          error: `Failed to start service ${id}`,
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 500;
      reply.status(statusCode).send({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to start service',
        timestamp: new Date().toISOString()
      });
    }
  });

  // POST /api/services/:id/stop - Stop a service
  fastify.post('/services/:id/stop', async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const result = await serviceManager.stopService(id);
      
      if (result) {
        reply.type('application/json').send({
          success: true,
          data: { message: `Service ${id} stopped successfully` },
          timestamp: new Date().toISOString()
        });
      } else {
        reply.status(500).send({
          success: false,
          error: `Failed to stop service ${id}`,
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 500;
      reply.status(statusCode).send({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to stop service',
        timestamp: new Date().toISOString()
      });
    }
  });

  // POST /api/services/:id/restart - Restart a service
  fastify.post('/services/:id/restart', async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const result = await serviceManager.restartService(id);
      
      if (result) {
        reply.type('application/json').send({
          success: true,
          data: { message: `Service ${id} restarted successfully` },
          timestamp: new Date().toISOString()
        });
      } else {
        reply.status(500).send({
          success: false,
          error: `Failed to restart service ${id}`,
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 500;
      reply.status(statusCode).send({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to restart service',
        timestamp: new Date().toISOString()
      });
    }
  });

  // GET /api/services/:id/logs - Get service logs
  fastify.get('/services/:id/logs', async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const { lines = '100' } = request.query as { lines?: string };
      
      const logs = await serviceManager.getServiceLogs(id, parseInt(lines, 10));
      
      // Convert logs to the expected format
      const logEntries = logs.map((logLine, index) => ({
        id: `${id}-${index}`,
        timestamp: new Date().toISOString(),
        level: 'info',
        source: id,
        message: logLine,
        metadata: null
      }));
      
      reply.type('application/json').send({
        success: true,
        data: logEntries,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 500;
      reply.status(statusCode).send({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get service logs',
        timestamp: new Date().toISOString()
      });
    }
  });
}