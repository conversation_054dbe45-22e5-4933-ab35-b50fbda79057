import type { FastifyInstance } from 'fastify';
import { exec } from 'child_process';
import { promisify } from 'util';
import { readFile, stat } from 'fs/promises';
import { join, dirname } from 'path';

const execAsync = promisify(exec);

export interface LogEntry {
  id: string;
  timestamp: string;
  level: string;
  source: string;
  message: string;
  metadata?: any;
}

export async function logsApiRoutes(fastify: FastifyInstance) {
  // GET /api/logs - Get application logs
  fastify.get('/logs', async (request, reply) => {
    try {
      const { 
        lines = '100', 
        level, 
        source, 
        since, 
        until 
      } = request.query as { 
        lines?: string; 
        level?: string; 
        source?: string; 
        since?: string; 
        until?: string; 
      };

      const logs = await getApplicationLogs({
        lines: parseInt(lines, 10),
        level,
        source,
        since,
        until
      });
      
      reply.type('application/json').send({
        success: true,
        data: logs,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get logs',
        timestamp: new Date().toISOString()
      });
    }
  });

  // GET /api/logs/system - Get system logs
  fastify.get('/logs/system', async (request, reply) => {
    try {
      const { lines = '100' } = request.query as { lines?: string };
      const logs = await getSystemLogs(parseInt(lines, 10));
      
      reply.type('application/json').send({
        success: true,
        data: logs,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get system logs',
        timestamp: new Date().toISOString()
      });
    }
  });

  // GET /api/logs/access - Get access logs
  fastify.get('/logs/access', async (request, reply) => {
    try {
      const { lines = '100' } = request.query as { lines?: string };
      const logs = await getAccessLogs(parseInt(lines, 10));
      
      reply.type('application/json').send({
        success: true,
        data: logs,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get access logs',
        timestamp: new Date().toISOString()
      });
    }
  });

  // GET /api/logs/error - Get error logs
  fastify.get('/logs/error', async (request, reply) => {
    try {
      const { lines = '100' } = request.query as { lines?: string };
      const logs = await getErrorLogs(parseInt(lines, 10));
      
      reply.type('application/json').send({
        success: true,
        data: logs,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get error logs',
        timestamp: new Date().toISOString()
      });
    }
  });
}

async function getApplicationLogs(options: {
  lines: number;
  level?: string;
  source?: string;
  since?: string;
  until?: string;
}): Promise<LogEntry[]> {
  try {
    // Try to read from common log locations
    const logPaths = [
      '/var/log/wow-backend.log',
      './logs/application.log',
      './application.log'
    ];

    let logs: LogEntry[] = [];

    // Try to read from log files
    for (const logPath of logPaths) {
      try {
        await stat(logPath);
        const content = await readFile(logPath, 'utf-8');
        const fileLines = content.split('\n').filter(line => line.trim());
        
        // Parse log lines (assuming JSON format)
        for (const line of fileLines.slice(-options.lines)) {
          try {
            const logEntry = JSON.parse(line);
            logs.push({
              id: crypto.randomUUID(),
              timestamp: logEntry.time || logEntry.timestamp || new Date().toISOString(),
              level: logEntry.level || 'info',
              source: logEntry.source || 'application',
              message: logEntry.msg || logEntry.message || line,
              metadata: logEntry
            });
          } catch {
            // If not JSON, treat as plain text
            logs.push({
              id: crypto.randomUUID(),
              timestamp: new Date().toISOString(),
              level: 'info',
              source: 'application',
              message: line,
              metadata: null
            });
          }
        }
        break; // Use first available log file
      } catch {
        continue; // Try next log path
      }
    }

    // If no log files found, generate mock application logs
    if (logs.length === 0) {
      logs = generateMockApplicationLogs(options.lines);
    }

    // Apply filters
    if (options.level) {
      logs = logs.filter(log => log.level === options.level);
    }
    
    if (options.source) {
      logs = logs.filter(log => log.source.includes(options.source));
    }

    if (options.since) {
      const sinceDate = new Date(options.since);
      logs = logs.filter(log => new Date(log.timestamp) >= sinceDate);
    }

    if (options.until) {
      const untilDate = new Date(options.until);
      logs = logs.filter(log => new Date(log.timestamp) <= untilDate);
    }

    return logs.slice(-options.lines);
  } catch (error) {
    console.error('Error getting application logs:', error);
    return generateMockApplicationLogs(options.lines);
  }
}

async function getSystemLogs(lines: number): Promise<LogEntry[]> {
  try {
    let command: string;
    
    if (process.platform === 'darwin') {
      // macOS
      command = `log show --predicate 'subsystem == "com.apple.system"' --last ${lines}`;
    } else if (process.platform === 'linux') {
      // Linux
      command = `journalctl -n ${lines} --no-pager --output=json`;
    } else {
      // Windows or other
      throw new Error('System logs not supported on this platform');
    }

    const { stdout } = await execAsync(command);
    const logLines = stdout.trim().split('\n').filter(line => line.trim());

    return logLines.map((line, index) => {
      try {
        if (process.platform === 'linux') {
          const logEntry = JSON.parse(line);
          return {
            id: `sys-${index}`,
            timestamp: logEntry.__REALTIME_TIMESTAMP ? 
              new Date(parseInt(logEntry.__REALTIME_TIMESTAMP) / 1000).toISOString() :
              new Date().toISOString(),
            level: logEntry.PRIORITY ? getPriorityLevel(logEntry.PRIORITY) : 'info',
            source: 'system',
            message: logEntry.MESSAGE || line,
            metadata: logEntry
          };
        } else {
          // macOS or fallback
          return {
            id: `sys-${index}`,
            timestamp: new Date().toISOString(),
            level: 'info',
            source: 'system',
            message: line,
            metadata: null
          };
        }
      } catch {
        return {
          id: `sys-${index}`,
          timestamp: new Date().toISOString(),
          level: 'info',
          source: 'system',
          message: line,
          metadata: null
        };
      }
    });
  } catch (error) {
    console.error('Error getting system logs:', error);
    return generateMockSystemLogs(lines);
  }
}

async function getAccessLogs(lines: number): Promise<LogEntry[]> {
  try {
    // Mock access logs - in a real implementation, you'd read from nginx/apache access logs
    return generateMockAccessLogs(lines);
  } catch (error) {
    console.error('Error getting access logs:', error);
    return [];
  }
}

async function getErrorLogs(lines: number): Promise<LogEntry[]> {
  try {
    // Try to read from common error log locations
    const errorLogPaths = [
      '/var/log/wow-backend-error.log',
      './logs/error.log',
      './error.log'
    ];

    for (const logPath of errorLogPaths) {
      try {
        await stat(logPath);
        const content = await readFile(logPath, 'utf-8');
        const fileLines = content.split('\n').filter(line => line.trim());
        
        return fileLines.slice(-lines).map((line, index) => ({
          id: `err-${index}`,
          timestamp: new Date().toISOString(),
          level: 'error',
          source: 'application',
          message: line,
          metadata: null
        }));
      } catch {
        continue;
      }
    }

    // If no error logs found, return empty array
    return [];
  } catch (error) {
    console.error('Error getting error logs:', error);
    return [];
  }
}

function generateMockApplicationLogs(lines: number): LogEntry[] {
  const levels = ['info', 'warn', 'error', 'debug'];
  const sources = ['application', 'database', 'auth', 'api', 'worker'];
  const messages = [
    'Server started successfully',
    'Database connection established',
    'User authenticated successfully',
    'API request processed',
    'Background job completed',
    'Cache invalidated',
    'File uploaded successfully',
    'Email sent',
    'Session created',
    'Metrics collected'
  ];

  return Array.from({ length: lines }, (_, index) => ({
    id: `app-${index}`,
    timestamp: new Date(Date.now() - (lines - index) * 1000).toISOString(),
    level: levels[Math.floor(Math.random() * levels.length)],
    source: sources[Math.floor(Math.random() * sources.length)],
    message: messages[Math.floor(Math.random() * messages.length)],
    metadata: {
      pid: process.pid,
      hostname: require('os').hostname(),
      requestId: crypto.randomUUID()
    }
  }));
}

function generateMockSystemLogs(lines: number): LogEntry[] {
  const messages = [
    'Kernel: CPU frequency scaling enabled',
    'NetworkManager: Connection established',
    'systemd: Service started',
    'SSH: Connection from remote host',
    'Firewall: Packet filtered',
    'Disk: I/O operation completed',
    'Memory: Page allocated',
    'Process: Signal received'
  ];

  return Array.from({ length: lines }, (_, index) => ({
    id: `sys-${index}`,
    timestamp: new Date(Date.now() - (lines - index) * 2000).toISOString(),
    level: 'info',
    source: 'system',
    message: messages[Math.floor(Math.random() * messages.length)],
    metadata: null
  }));
}

function generateMockAccessLogs(lines: number): LogEntry[] {
  const methods = ['GET', 'POST', 'PUT', 'DELETE'];
  const paths = ['/api/users', '/api/auth/login', '/api/metrics', '/health', '/api/docker/containers'];
  const statuses = [200, 201, 304, 400, 401, 404, 500];
  const userAgents = [
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
    'curl/7.68.0',
    'PostmanRuntime/7.29.0',
    'axios/0.24.0'
  ];

  return Array.from({ length: lines }, (_, index) => {
    const method = methods[Math.floor(Math.random() * methods.length)];
    const path = paths[Math.floor(Math.random() * paths.length)];
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const userAgent = userAgents[Math.floor(Math.random() * userAgents.length)];
    
    return {
      id: `access-${index}`,
      timestamp: new Date(Date.now() - (lines - index) * 500).toISOString(),
      level: status >= 400 ? 'warn' : 'info',
      source: 'access',
      message: `${method} ${path} ${status}`,
      metadata: {
        method,
        path,
        status,
        userAgent,
        ip: `192.168.1.${Math.floor(Math.random() * 255)}`,
        responseTime: Math.floor(Math.random() * 1000) + 'ms'
      }
    };
  });
}

function getPriorityLevel(priority: string): string {
  const priorityMap: Record<string, string> = {
    '0': 'emergency',
    '1': 'alert',
    '2': 'critical',
    '3': 'error',
    '4': 'warn',
    '5': 'notice',
    '6': 'info',
    '7': 'debug'
  };
  
  return priorityMap[priority] || 'info';
}