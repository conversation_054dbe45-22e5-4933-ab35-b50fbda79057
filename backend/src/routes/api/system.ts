import type { FastifyInstance } from 'fastify';
import { systemMonitor } from '../../services/system-monitor.js';

export async function systemApiRoutes(fastify: FastifyInstance) {
  // GET /api/system/metrics - Get current system metrics
  fastify.get('/system/metrics', async (request, reply) => {
    try {
      const metrics = await systemMonitor.getSystemMetrics();
      
      reply.type('application/json').send({
        success: true,
        data: metrics,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get system metrics',
        timestamp: new Date().toISOString()
      });
    }
  });

  // GET /api/system/health - Get system health status
  fastify.get('/system/health', async (request, reply) => {
    try {
      const metrics = await systemMonitor.getSystemMetrics();
      
      // Simple health assessment based on system metrics
      const health = {
        status: 'healthy' as 'healthy' | 'warning' | 'critical',
        checks: {
          cpu: metrics.cpu.usage < 80 ? 'healthy' : metrics.cpu.usage < 95 ? 'warning' : 'critical',
          memory: (metrics.memory.used / metrics.memory.total) * 100 < 80 ? 'healthy' : 
                  (metrics.memory.used / metrics.memory.total) * 100 < 95 ? 'warning' : 'critical',
          disk: (metrics.disk.used / metrics.disk.total) * 100 < 85 ? 'healthy' : 
                (metrics.disk.used / metrics.disk.total) * 100 < 95 ? 'warning' : 'critical'
        },
        metrics: {
          cpuUsage: metrics.cpu.usage,
          memoryUsage: Math.round((metrics.memory.used / metrics.memory.total) * 100 * 100) / 100,
          diskUsage: Math.round((metrics.disk.used / metrics.disk.total) * 100 * 100) / 100
        },
        timestamp: metrics.timestamp
      };

      // Determine overall health status
      const criticalChecks = Object.values(health.checks).filter(status => status === 'critical').length;
      const warningChecks = Object.values(health.checks).filter(status => status === 'warning').length;

      if (criticalChecks > 0) {
        health.status = 'critical';
      } else if (warningChecks > 0) {
        health.status = 'warning';
      }

      reply.type('application/json').send({
        success: true,
        data: health,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get system health',
        timestamp: new Date().toISOString()
      });
    }
  });

  // GET /api/system/processes - Get top processes
  fastify.get('/system/processes', async (request, reply) => {
    try {
      const processes = await systemMonitor.getProcessList();
      
      reply.type('application/json').send({
        success: true,
        data: processes,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get process list',
        timestamp: new Date().toISOString()
      });
    }
  });
}