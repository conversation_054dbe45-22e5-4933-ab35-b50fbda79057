import { exec } from 'child_process';
import { promisify } from 'util';
import { systemMonitor } from './system-monitor.js';

const execAsync = promisify(exec);

export interface ServiceInfo {
  id: string;
  name: string;
  status: ServiceStatus;
  port?: number;
  uptime?: number;
  description?: string;
  version?: string;
  dependencies: string[];
  resourceUsage?: ResourceUsage;
  lastUpdated: string;
}

export interface ResourceUsage {
  cpu: number;
  memory: number;
  diskRead: number;
  diskWrite: number;
  networkIn: number;
  networkOut: number;
}

export enum ServiceStatus {
  RUNNING = 'running',
  STOPPED = 'stopped',
  STARTING = 'starting',
  STOPPING = 'stopping',
  ERROR = 'error',
  UNKNOWN = 'unknown'
}

interface ServiceConfig {
  id: string;
  name: string;
  description: string;
  port?: number;
  command?: string;
  processName?: string;
  dependencies?: string[];
  version?: string;
}

export class ServiceManager {
  private static instance: ServiceManager;
  private services: Map<string, ServiceConfig> = new Map();

  static getInstance(): ServiceManager {
    if (!ServiceManager.instance) {
      ServiceManager.instance = new ServiceManager();
    }
    return ServiceManager.instance;
  }

  constructor() {
    this.initializeServices();
  }

  private initializeServices() {
    // Default services configuration
    const defaultServices: ServiceConfig[] = [
      {
        id: 'postgres',
        name: 'PostgreSQL',
        description: 'Primary database server',
        port: 5432,
        processName: 'postgres',
        dependencies: [],
        version: '15.0'
      },
      {
        id: 'redis',
        name: 'Redis',
        description: 'Cache and session store',
        port: 6379,
        processName: 'redis-server',
        dependencies: [],
        version: '7.0'
      },
      {
        id: 'milvus',
        name: 'Milvus',
        description: 'Vector database for AI/ML workloads',
        port: 19530,
        processName: 'milvus',
        dependencies: [],
        version: '2.3'
      },
      {
        id: 'backend',
        name: 'WOW Backend',
        description: 'Main backend API server',
        port: 4001,
        processName: 'node',
        dependencies: ['postgres', 'redis', 'milvus'],
        version: '0.1.0'
      },
      {
        id: 'websocket',
        name: 'WebSocket Server',
        description: 'Real-time updates server',
        port: 8081,
        processName: 'node',
        dependencies: ['backend'],
        version: '0.1.0'
      },
      {
        id: 'grafana',
        name: 'Grafana',
        description: 'Monitoring dashboard',
        port: 3000,
        processName: 'grafana-server',
        dependencies: [],
        version: '10.0'
      },
      {
        id: 'prometheus',
        name: 'Prometheus',
        description: 'Metrics collection and monitoring',
        port: 9090,
        processName: 'prometheus',
        dependencies: [],
        version: '2.45'
      }
    ];

    defaultServices.forEach(service => {
      this.services.set(service.id, service);
    });
  }

  async getAllServices(): Promise<ServiceInfo[]> {
    const serviceInfoPromises = Array.from(this.services.values()).map(async (config) => {
      return await this.getServiceInfo(config.id);
    });

    return Promise.all(serviceInfoPromises);
  }

  async getServiceInfo(serviceId: string): Promise<ServiceInfo> {
    const config = this.services.get(serviceId);
    if (!config) {
      throw new Error(`Service ${serviceId} not found`);
    }

    const status = await this.getServiceStatus(config);
    const uptime = await this.getServiceUptime(config);
    const resourceUsage = await this.getServiceResourceUsage(config);

    return {
      id: config.id,
      name: config.name,
      status,
      port: config.port,
      uptime,
      description: config.description,
      version: config.version,
      dependencies: config.dependencies || [],
      resourceUsage,
      lastUpdated: new Date().toISOString()
    };
  }

  private async getServiceStatus(config: ServiceConfig): Promise<ServiceStatus> {
    try {
      // Check if service is listening on its port
      if (config.port) {
        const portStatus = await this.checkPortStatus(config.port);
        if (portStatus) {
          return ServiceStatus.RUNNING;
        }
      }

      // Check if process is running
      if (config.processName) {
        const processStatus = await this.checkProcessStatus(config.processName);
        if (processStatus) {
          return ServiceStatus.RUNNING;
        }
      }

      return ServiceStatus.STOPPED;
    } catch (error) {
      console.error(`Error checking status for ${config.name}:`, error);
      return ServiceStatus.ERROR;
    }
  }

  private async checkPortStatus(port: number): Promise<boolean> {
    try {
      const command = process.platform === 'win32' 
        ? `netstat -an | findstr :${port}` 
        : `lsof -i :${port} || netstat -tuln | grep :${port}`;
      
      const { stdout } = await execAsync(command);
      return stdout.trim().length > 0;
    } catch {
      return false;
    }
  }

  private async checkProcessStatus(processName: string): Promise<boolean> {
    try {
      const command = process.platform === 'win32'
        ? `tasklist | findstr ${processName}`
        : `pgrep -f ${processName}`;
      
      const { stdout } = await execAsync(command);
      return stdout.trim().length > 0;
    } catch {
      return false;
    }
  }

  private async getServiceUptime(config: ServiceConfig): Promise<number | undefined> {
    try {
      if (!config.processName) return undefined;

      const command = process.platform === 'win32'
        ? `wmic process where name="${config.processName}.exe" get CreationDate`
        : `ps -o pid,etime -C ${config.processName} | tail -n +2 | head -n 1`;

      const { stdout } = await execAsync(command);
      
      if (process.platform === 'win32') {
        // Parse Windows CreationDate
        const lines = stdout.trim().split('\n');
        if (lines.length > 1) {
          const creationDate = lines[1].trim();
          if (creationDate) {
            const startTime = new Date(creationDate);
            return (Date.now() - startTime.getTime()) / 1000;
          }
        }
      } else {
        // Parse Unix etime format
        const etimeMatch = stdout.trim().match(/\s+(\S+)$/);
        if (etimeMatch) {
          const etime = etimeMatch[1];
          return this.parseEtimeToSeconds(etime);
        }
      }

      return undefined;
    } catch {
      return undefined;
    }
  }

  private parseEtimeToSeconds(etime: string): number {
    // Parse formats like "12:34:56", "1-12:34:56", "34:56", "56"
    const parts = etime.split('-');
    let timeStr = parts[parts.length - 1];
    let days = parts.length > 1 ? parseInt(parts[0]) : 0;

    const timeParts = timeStr.split(':').map(p => parseInt(p));
    let seconds = 0;

    if (timeParts.length === 3) {
      // HH:MM:SS
      seconds = timeParts[0] * 3600 + timeParts[1] * 60 + timeParts[2];
    } else if (timeParts.length === 2) {
      // MM:SS
      seconds = timeParts[0] * 60 + timeParts[1];
    } else if (timeParts.length === 1) {
      // SS
      seconds = timeParts[0];
    }

    return days * 86400 + seconds;
  }

  private async getServiceResourceUsage(config: ServiceConfig): Promise<ResourceUsage | undefined> {
    try {
      if (!config.processName) return undefined;

      const processes = await systemMonitor.getProcessList();
      const serviceProcess = processes.find(p => p.name.includes(config.processName));

      if (!serviceProcess) return undefined;

      return {
        cpu: serviceProcess.cpu,
        memory: serviceProcess.memory * 1024 * 1024, // Convert MB to bytes
        diskRead: 0, // Not available from process list
        diskWrite: 0, // Not available from process list
        networkIn: 0, // Not available from process list
        networkOut: 0 // Not available from process list
      };
    } catch {
      return undefined;
    }
  }

  async startService(serviceId: string): Promise<boolean> {
    const config = this.services.get(serviceId);
    if (!config) {
      throw new Error(`Service ${serviceId} not found`);
    }

    try {
      // For Docker-based services, use docker-compose
      if (['postgres', 'redis', 'milvus', 'grafana', 'prometheus'].includes(serviceId)) {
        const { stdout, stderr } = await execAsync(`docker-compose up -d ${serviceId}`);
        console.log(`Started ${config.name}:`, stdout);
        if (stderr) console.error(`Start ${config.name} warnings:`, stderr);
        return true;
      }

      // For custom services, implement specific start logic
      if (serviceId === 'backend') {
        // Start backend service (this would typically be handled by process manager)
        console.log(`Starting ${config.name}...`);
        return true;
      }

      return false;
    } catch (error) {
      console.error(`Failed to start ${config.name}:`, error);
      throw new Error(`Failed to start service ${config.name}`);
    }
  }

  async stopService(serviceId: string): Promise<boolean> {
    const config = this.services.get(serviceId);
    if (!config) {
      throw new Error(`Service ${serviceId} not found`);
    }

    try {
      // For Docker-based services
      if (['postgres', 'redis', 'milvus', 'grafana', 'prometheus'].includes(serviceId)) {
        const { stdout, stderr } = await execAsync(`docker-compose stop ${serviceId}`);
        console.log(`Stopped ${config.name}:`, stdout);
        if (stderr) console.error(`Stop ${config.name} warnings:`, stderr);
        return true;
      }

      // For custom services
      if (serviceId === 'backend') {
        console.log(`Stopping ${config.name}...`);
        return true;
      }

      return false;
    } catch (error) {
      console.error(`Failed to stop ${config.name}:`, error);
      throw new Error(`Failed to stop service ${config.name}`);
    }
  }

  async restartService(serviceId: string): Promise<boolean> {
    try {
      await this.stopService(serviceId);
      // Wait a moment for the service to fully stop
      await new Promise(resolve => setTimeout(resolve, 2000));
      await this.startService(serviceId);
      return true;
    } catch (error) {
      console.error(`Failed to restart service ${serviceId}:`, error);
      throw error;
    }
  }

  async getServiceLogs(serviceId: string, lines: number = 100): Promise<string[]> {
    const config = this.services.get(serviceId);
    if (!config) {
      throw new Error(`Service ${serviceId} not found`);
    }

    try {
      // For Docker-based services
      if (['postgres', 'redis', 'milvus', 'grafana', 'prometheus'].includes(serviceId)) {
        const { stdout } = await execAsync(`docker-compose logs --tail ${lines} ${serviceId}`);
        return stdout.split('\n').filter(line => line.trim().length > 0);
      }

      // For other services, return mock logs
      return [
        `[${new Date().toISOString()}] INFO: Service ${config.name} is running`,
        `[${new Date(Date.now() - 60000).toISOString()}] INFO: Health check passed`,
        `[${new Date(Date.now() - 120000).toISOString()}] INFO: Service started successfully`
      ];
    } catch (error) {
      console.error(`Failed to get logs for ${config.name}:`, error);
      return [`Error retrieving logs for ${config.name}: ${error}`];
    }
  }

  // Add a new service configuration
  addService(serviceConfig: ServiceConfig) {
    this.services.set(serviceConfig.id, serviceConfig);
  }

  // Remove a service configuration
  removeService(serviceId: string) {
    this.services.delete(serviceId);
  }
}

export const serviceManager = ServiceManager.getInstance();