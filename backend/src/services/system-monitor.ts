import si from 'systeminformation';

// macOS App Compatible System Metrics Interface
export interface SystemMetrics {
  id: string;
  timestamp: string;
  cpu: CPUMetrics;
  memory: MemoryMetrics;
  disk: DiskMetrics;
  network: NetworkMetrics;
}

export interface CPUMetrics {
  usage: number;
  cores: number;
  loadAverage: number[];
  temperature?: number;
}

export interface MemoryMetrics {
  total: number;
  used: number;
  available: number;
  cached: number;
  buffers: number;
}

export interface DiskMetrics {
  total: number;
  used: number;
  available: number;
  readOperations: number;
  writeOperations: number;
  readBytes: number;
  writeBytes: number;
}

export interface NetworkMetrics {
  bytesReceived: number;
  bytesSent: number;
  packetsReceived: number;
  packetsSent: number;
  errors: number;
}

export interface ServiceHealth {
  name: string;
  status: 'healthy' | 'unhealthy' | 'unknown';
  url?: string;
  responseTime?: number;
  lastCheck: string;
  error?: string;
}

export class SystemMonitorService {
  private static instance: SystemMonitorService;
  private networkStats: any = null;

  static getInstance(): SystemMonitorService {
    if (!SystemMonitorService.instance) {
      SystemMonitorService.instance = new SystemMonitorService();
    }
    return SystemMonitorService.instance;
  }

  async getSystemMetrics(): Promise<SystemMetrics> {
    try {
      const [
        cpuLoad,
        cpuTemp,
        cpuInfo,
        memory,
        diskUsage,
        networkStats,
        diskStats,
        osInfo
      ] = await Promise.all([
        si.currentLoad(),
        si.cpuTemperature().catch(() => null),
        si.cpu(),
        si.mem(),
        si.fsSize(),
        si.networkStats(),
        si.disksIO().catch(() => ({ rIO: 0, wIO: 0, rIO_sec: 0, wIO_sec: 0 })),
        si.osInfo()
      ]);

      // Get load average (Unix systems)
      const loadAverage = process.platform !== 'win32' ? [
        osInfo.os === 'Darwin' ? parseFloat((await si.currentLoad()).avgLoad || '0') : 0,
        0, // 5-minute average (not available in systeminformation)
        0  // 15-minute average (not available in systeminformation)
      ] : [0, 0, 0];

      // Aggregate network stats
      let networkBytesReceived = 0;
      let networkBytesSent = 0;
      let networkPacketsReceived = 0;
      let networkPacketsSent = 0;
      let networkErrors = 0;

      if (networkStats && networkStats.length > 0) {
        networkStats.forEach((stat: any) => {
          networkBytesReceived += stat.rx_bytes || 0;
          networkBytesSent += stat.tx_bytes || 0;
          networkPacketsReceived += stat.rx_packets || 0;
          networkPacketsSent += stat.tx_packets || 0;
          networkErrors += (stat.rx_errors || 0) + (stat.tx_errors || 0);
        });
      }

      // Calculate disk usage (use the primary disk)
      const primaryDisk = diskUsage.find((disk: any) => disk.mount === '/' || disk.mount === 'C:') || diskUsage[0];
      const diskTotal = primaryDisk?.size || 0;
      const diskUsed = primaryDisk?.used || 0;
      const diskAvailable = primaryDisk?.available || 0;

      return {
        id: crypto.randomUUID(),
        timestamp: new Date().toISOString(),
        cpu: {
          usage: Math.round(cpuLoad.currentLoad * 100) / 100,
          cores: cpuInfo.cores,
          loadAverage,
          temperature: cpuTemp?.main || undefined
        },
        memory: {
          total: memory.total,
          used: memory.used,
          available: memory.available,
          cached: memory.cached || 0,
          buffers: memory.buffcache || 0
        },
        disk: {
          total: diskTotal,
          used: diskUsed,
          available: diskAvailable,
          readOperations: diskStats.rIO || 0,
          writeOperations: diskStats.wIO || 0,
          readBytes: diskStats.rIO_sec || 0,
          writeBytes: diskStats.wIO_sec || 0
        },
        network: {
          bytesReceived: networkBytesReceived,
          bytesSent: networkBytesSent,
          packetsReceived: networkPacketsReceived,
          packetsSent: networkPacketsSent,
          errors: networkErrors
        }
      };
    } catch (error) {
      console.error('Error getting system metrics:', error);
      throw new Error('Failed to retrieve system metrics');
    }
  }

  async checkServiceHealth(services: Array<{ name: string; url: string; timeout?: number }>): Promise<ServiceHealth[]> {
    const healthChecks = services.map(async (service) => {
      const startTime = Date.now();
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), service.timeout || 5000);

        const response = await fetch(service.url, {
          signal: controller.signal,
          method: 'GET',
          headers: {
            'User-Agent': 'System-Monitor/1.0'
          }
        });

        clearTimeout(timeoutId);
        const responseTime = Date.now() - startTime;

        return {
          name: service.name,
          status: response.ok ? 'healthy' : 'unhealthy',
          url: service.url,
          responseTime,
          lastCheck: new Date().toISOString(),
          error: response.ok ? undefined : `HTTP ${response.status}: ${response.statusText}`
        } as ServiceHealth;
      } catch (error) {
        const responseTime = Date.now() - startTime;
        return {
          name: service.name,
          status: 'unhealthy',
          url: service.url,
          responseTime,
          lastCheck: new Date().toISOString(),
          error: error instanceof Error ? error.message : 'Unknown error'
        } as ServiceHealth;
      }
    });

    return Promise.all(healthChecks);
  }

  async getProcessList(): Promise<Array<{
    pid: number;
    name: string;
    cpu: number;
    memory: number;
    command: string;
  }>> {
    try {
      const processes = await si.processes();
      return processes.list
        .filter((proc: any) => proc.cpu > 0.1) // Only show processes using > 0.1% CPU
        .sort((a: any, b: any) => b.cpu - a.cpu) // Sort by CPU usage
        .slice(0, 10) // Top 10 processes
        .map((proc: any) => ({
          pid: proc.pid,
          name: proc.name,
          cpu: Math.round(proc.cpu * 100) / 100,
          memory: Math.round(proc.memRss / 1024 / 1024 * 100) / 100, // Convert to MB
          command: proc.command
        }));
    } catch (error) {
      console.error('Error getting process list:', error);
      return [];
    }
  }

  formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  formatUptime(seconds: number): string {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) {
      return `${days}d ${hours}h ${minutes}m`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  }
}

export const systemMonitor = SystemMonitorService.getInstance();
