console.log('Importing realtime monitor...');
import { WebSocketServer, WebSocket } from 'ws';
import { systemMonitor } from './system-monitor.js';
import { dockerService } from './docker.js';
console.log('Realtime monitor dependencies loaded successfully');

export interface WebSocketMessage {
  type: 'metrics' | 'docker' | 'alert' | 'error' | 'pong' | 'system_metrics' | 'service_update' | 'docker_update' | 'log_entry' | 'notification';
  data: any;
  timestamp: string;
}

export class RealTimeMonitorService {
  private wss: WebSocketServer;
  private clients: Set<WebSocket> = new Set();
  private intervals: NodeJS.Timeout[] = [];

  constructor(port: number = 8081) {
    console.log(`Initializing WebSocket server on port ${port}...`);
    this.wss = new WebSocketServer({ port });
    this.setupWebSocketServer();
    this.startMonitoring();
    console.log(`WebSocket server initialized successfully on port ${port}`);
  }

  private setupWebSocketServer() {
    this.wss.on('connection', (ws: WebSocket) => {
      console.log('New WebSocket client connected');
      this.clients.add(ws);

      // Send initial data
      this.sendInitialData(ws);

      ws.on('close', () => {
        console.log('WebSocket client disconnected');
        this.clients.delete(ws);
      });

      ws.on('error', (error) => {
        console.error('WebSocket error:', error);
        this.clients.delete(ws);
      });

      ws.on('message', (message) => {
        try {
          const data = JSON.parse(message.toString());
          this.handleClientMessage(ws, data);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      });
    });

    console.log(`WebSocket server running on port ${this.wss.options.port}`);
  }

  private async sendInitialData(ws: WebSocket) {
    try {
      // Send initial system metrics
      const metrics = await systemMonitor.getSystemMetrics();
      this.sendToClient(ws, {
        type: 'system_metrics',
        data: metrics,
        timestamp: new Date().toISOString()
      });

      // Send initial Docker status
      const dockerRunning = await dockerService.isDockerRunning();
      if (dockerRunning) {
        const containers = await dockerService.listContainers(true);
        this.sendToClient(ws, {
          type: 'docker_update',
          data: { running: true, containers },
          timestamp: new Date().toISOString()
        });
      } else {
        this.sendToClient(ws, {
          type: 'docker_update',
          data: { running: false, containers: [] },
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error('Error sending initial data:', error);
    }
  }

  private handleClientMessage(ws: WebSocket, message: any) {
    switch (message.type) {
      case 'ping':
        this.sendToClient(ws, {
          type: 'pong',
          data: { timestamp: new Date().toISOString() },
          timestamp: new Date().toISOString()
        });
        break;
      
      case 'requestMetrics':
        this.sendMetricsUpdate();
        break;
        
      case 'requestDocker':
        this.sendDockerUpdate();
        break;
        
      default:
        console.log('Unknown message type:', message.type);
    }
  }

  private startMonitoring() {
    // Send system metrics every 2 seconds
    const metricsInterval = setInterval(async () => {
      await this.sendMetricsUpdate();
    }, 2000);

    // Send Docker status every 5 seconds
    const dockerInterval = setInterval(async () => {
      await this.sendDockerUpdate();
    }, 5000);

    // Send alerts/random events every 30 seconds
    const alertsInterval = setInterval(async () => {
      await this.sendRandomAlert();
    }, 30000);

    this.intervals.push(metricsInterval, dockerInterval, alertsInterval);
  }

  private async sendMetricsUpdate() {
    try {
      const metrics = await systemMonitor.getSystemMetrics();
      this.broadcast({
        type: 'system_metrics',
        data: metrics,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error sending metrics update:', error);
    }
  }

  private async sendDockerUpdate() {
    try {
      const dockerRunning = await dockerService.isDockerRunning();
      
      if (dockerRunning) {
        const containers = await dockerService.listContainers(true);
        this.broadcast({
          type: 'docker_update',
          data: { running: true, containers },
          timestamp: new Date().toISOString()
        });
      } else {
        this.broadcast({
          type: 'docker_update',
          data: { running: false, containers: [] },
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error('Error sending Docker update:', error);
      this.broadcast({
        type: 'docker_update',
        data: { running: false, containers: [], error: 'Docker not accessible' },
        timestamp: new Date().toISOString()
      });
    }
  }

  private async sendRandomAlert() {
    // Generate random alerts for demo purposes
    const alertTypes = ['info', 'warning', 'error', 'success'];
    const messages = [
      'System backup completed successfully',
      'High memory usage detected',
      'Docker container restarted',
      'New user registered',
      'Database connection restored',
      'Low disk space warning'
    ];

    const randomAlert = {
      id: Date.now().toString(),
      type: alertTypes[Math.floor(Math.random() * alertTypes.length)],
      title: 'System Alert',
      message: messages[Math.floor(Math.random() * messages.length)],
      timestamp: new Date().toISOString()
    };

    this.broadcast({
      type: 'notification',
      data: randomAlert,
      timestamp: new Date().toISOString()
    });
  }

  private sendToClient(ws: WebSocket, message: WebSocketMessage) {
    if (ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(JSON.stringify(message));
      } catch (error) {
        console.error('Error sending message to client:', error);
      }
    }
  }

  private broadcast(message: WebSocketMessage) {
    this.clients.forEach(client => {
      this.sendToClient(client, message);
    });
  }

  public getClientCount(): number {
    return this.clients.size;
  }

  public stop() {
    // Clear all intervals
    this.intervals.forEach(interval => clearInterval(interval));
    
    // Close all client connections
    this.clients.forEach(client => {
      client.close();
    });
    
    // Close the WebSocket server
    this.wss.close();
    
    console.log('WebSocket server stopped');
  }
}

// Export a singleton instance
export const realTimeMonitor = new RealTimeMonitorService(8081);
